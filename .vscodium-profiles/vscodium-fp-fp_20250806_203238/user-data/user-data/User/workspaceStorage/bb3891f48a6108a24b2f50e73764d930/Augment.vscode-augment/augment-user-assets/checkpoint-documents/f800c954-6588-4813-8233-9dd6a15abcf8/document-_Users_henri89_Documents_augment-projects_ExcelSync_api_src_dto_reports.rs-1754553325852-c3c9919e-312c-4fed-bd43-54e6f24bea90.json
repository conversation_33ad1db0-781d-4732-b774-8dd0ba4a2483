{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/reports.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n/// Report generation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct ReportGenerationRequest {\n    pub report_type: ReportType,\n    pub format: ReportFormat,\n    pub filters: Option<ReportFilters>,\n    pub include_metadata: Option<bool>,\n    pub custom_fields: Option<Vec<String>>,\n}\n\n/// Report types\n#[derive(Debug, Deserialize, Serialize, Clone)]\n#[serde(rename_all = \"snake_case\")]\npub enum ReportType {\n    ProjectSummary,\n    FinancialAnalysis,\n    AuditReport,\n    TemplateUsage,\n    UserActivity,\n    SystemPerformance,\n    ComplianceReport,\n    CustomReport,\n}\n\n/// Report formats\n#[derive(Debug, Deserialize, Serialize, Clone)]\n#[serde(rename_all = \"lowercase\")]\npub enum ReportFormat {\n    Excel,\n    Csv,\n    Json,\n    Pdf,\n}\n\n/// Report filters\n#[derive(Debug, Deserialize, Serialize)]\npub struct ReportFilters {\n    pub date_range: Option<DateRange>,\n    pub project_ids: Option<Vec<String>>,\n    pub user_ids: Option<Vec<String>>,\n    pub organization_ids: Option<Vec<String>>,\n    pub template_ids: Option<Vec<String>>,\n    pub status_filter: Option<Vec<String>>,\n    pub custom_filters: Option<serde_json::Value>,\n}\n\n/// Date range for reports\n#[derive(Debug, Deserialize, Serialize)]\npub struct DateRange {\n    pub start_date: String,\n    pub end_date: String,\n}\n\n/// Report generation response\n#[derive(Debug, Serialize)]\npub struct ReportGenerationResponse {\n    pub report_id: String,\n    pub report_type: ReportType,\n    pub format: ReportFormat,\n    pub status: ReportStatus,\n    pub generated_at: String,\n    pub generated_by: String,\n    pub file_size: Option<u64>,\n    pub download_url: Option<String>,\n    pub expires_at: Option<String>,\n    pub metadata: ReportMetadata,\n}\n\n/// Report status\n#[derive(Debug, Serialize, Clone)]\n#[serde(rename_all = \"lowercase\")]\npub enum ReportStatus {\n    Pending,\n    Processing,\n    Completed,\n    Failed,\n    Expired,\n}\n\n/// Report metadata\n#[derive(Debug, Serialize)]\npub struct ReportMetadata {\n    pub total_records: u64,\n    pub processing_time_ms: u64,\n    pub data_sources: Vec<String>,\n    pub filters_applied: Vec<String>,\n    pub columns: Vec<ReportColumn>,\n}\n\n/// Report column definition\n#[derive(Debug, Serialize)]\npub struct ReportColumn {\n    pub name: String,\n    pub data_type: String,\n    pub description: Option<String>,\n}\n\n/// Report list request\n#[derive(Debug, Deserialize)]\npub struct ReportListRequest {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n    pub report_type: Option<ReportType>,\n    pub status: Option<ReportStatus>,\n    pub generated_by: Option<String>,\n}\n\n/// Report list response\n#[derive(Debug, Serialize)]\npub struct ReportListResponse {\n    pub reports: Vec<ReportSummary>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n}\n\n/// Report summary for listing\n#[derive(Debug, Serialize)]\npub struct ReportSummary {\n    pub report_id: String,\n    pub report_type: ReportType,\n    pub format: ReportFormat,\n    pub status: ReportStatus,\n    pub generated_at: String,\n    pub generated_by: String,\n    pub file_size: Option<u64>,\n    pub expires_at: Option<String>,\n}\n\n/// Excel report configuration\n#[derive(Debug, Serialize, Deserialize)]\npub struct ExcelReportConfig {\n    pub sheet_name: String,\n    pub include_charts: bool,\n    pub include_summary: bool,\n    pub auto_filter: bool,\n    pub freeze_header: bool,\n    pub column_widths: Option<Vec<f64>>,\n}\n\n/// CSV report configuration\n#[derive(Debug, Serialize, Deserialize)]\npub struct CsvReportConfig {\n    pub delimiter: char,\n    pub include_header: bool,\n    pub quote_all: bool,\n}\n\n/// PDF report configuration\n#[derive(Debug, Serialize, Deserialize)]\npub struct PdfReportConfig {\n    pub page_size: String,\n    pub orientation: String,\n    pub include_header: bool,\n    pub include_footer: bool,\n    pub font_size: u8,\n}\n\n/// Report data structure for internal processing\n#[derive(Debug)]\npub struct ReportData {\n    pub headers: Vec<String>,\n    pub rows: Vec<Vec<String>>,\n    pub metadata: ReportMetadata,\n}\n\n/// Report generation error\n#[derive(Debug, thiserror::Error)]\npub enum ReportError {\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    \n    #[error(\"Invalid report configuration: {0}\")]\n    InvalidConfiguration(String),\n    \n    #[error(\"Data processing error: {0}\")]\n    DataProcessingError(String),\n    \n    #[error(\"File generation error: {0}\")]\n    FileGenerationError(String),\n    \n    #[error(\"Report not found: {0}\")]\n    ReportNotFound(String),\n    \n    #[error(\"Permission denied: {0}\")]\n    PermissionDenied(String),\n    \n    #[error(\"Report expired: {0}\")]\n    ReportExpired(String),\n    \n    #[error(\"Unsupported format: {0}\")]\n    UnsupportedFormat(String),\n}\n\n/// Report job for async processing\n#[derive(Debug, Serialize, Deserialize)]\npub struct ReportJob {\n    pub job_id: String,\n    pub report_id: String,\n    pub user_id: String,\n    pub request: ReportGenerationRequest,\n    pub status: ReportStatus,\n    pub created_at: DateTime<Utc>,\n    pub started_at: Option<DateTime<Utc>>,\n    pub completed_at: Option<DateTime<Utc>>,\n    pub error_message: Option<String>,\n}\n\n/// Async report status response\n#[derive(Debug, Serialize)]\npub struct AsyncReportStatusResponse {\n    pub job_id: String,\n    pub report_id: String,\n    pub status: ReportStatus,\n    pub progress_percentage: Option<u8>,\n    pub estimated_completion: Option<String>,\n    pub error_message: Option<String>,\n}\n"}