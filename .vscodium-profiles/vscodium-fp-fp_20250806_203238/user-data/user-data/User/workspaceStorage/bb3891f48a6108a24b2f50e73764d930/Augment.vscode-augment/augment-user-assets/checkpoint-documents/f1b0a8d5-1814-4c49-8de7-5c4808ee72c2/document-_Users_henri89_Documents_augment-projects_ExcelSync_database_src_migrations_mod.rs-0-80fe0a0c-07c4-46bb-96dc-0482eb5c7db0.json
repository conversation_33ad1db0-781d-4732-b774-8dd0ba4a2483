{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/mod.rs"}, "originalCode": "use sea_orm_migration::prelude::*;\n\nmod m20250106_000001_create_organizations;\nmod m20250106_000002_create_users;\nmod m20250106_000003_create_projects;\nmod m20250106_000004_create_templates;\nmod m20250106_000005_create_project_data;\nmod m20250106_000006_create_sessions;\nmod m20250106_000007_create_audit_logs;\nmod m20250106_000008_create_indexes;\n\npub struct Migrator;\n\n#[async_trait::async_trait]\nimpl MigratorTrait for Migrator {\n    fn migrations() -> Vec<Box<dyn MigrationTrait>> {\n        vec![\n            Box::new(m20250106_000001_create_organizations::Migration),\n            Box::new(m20250106_000002_create_users::Migration),\n            Box::new(m20250106_000003_create_projects::Migration),\n            Box::new(m20250106_000004_create_templates::Migration),\n            Box::new(m20250106_000005_create_project_data::Migration),\n            Box::new(m20250106_000006_create_sessions::Migration),\n            Box::new(m20250106_000007_create_audit_logs::Migration),\n            Box::new(m20250106_000008_create_indexes::Migration),\n        ]\n    }\n}\n", "modifiedCode": "use sea_orm_migration::prelude::*;\n\nmod m20250106_000001_create_organizations;\nmod m20250106_000002_create_users;\nmod m20250106_000003_create_projects;\nmod m20250106_000004_create_templates;\nmod m20250106_000005_create_project_data;\nmod m20250106_000006_create_sessions;\nmod m20250106_000007_create_audit_logs;\nmod m20250106_000008_create_indexes;\n\npub struct Migrator;\n\n#[async_trait::async_trait]\nimpl MigratorTrait for Migrator {\n    fn migrations() -> Vec<Box<dyn MigrationTrait>> {\n        vec![\n            Box::new(m20250106_000001_create_organizations::Migration),\n            Box::new(m20250106_000002_create_users::Migration),\n            Box::new(m20250106_000003_create_projects::Migration),\n            Box::new(m20250106_000004_create_templates::Migration),\n            Box::new(m20250106_000005_create_project_data::Migration),\n            Box::new(m20250106_000006_create_sessions::Migration),\n            Box::new(m20250106_000007_create_audit_logs::Migration),\n            Box::new(m20250106_000008_create_indexes::Migration),\n        ]\n    }\n}\n"}