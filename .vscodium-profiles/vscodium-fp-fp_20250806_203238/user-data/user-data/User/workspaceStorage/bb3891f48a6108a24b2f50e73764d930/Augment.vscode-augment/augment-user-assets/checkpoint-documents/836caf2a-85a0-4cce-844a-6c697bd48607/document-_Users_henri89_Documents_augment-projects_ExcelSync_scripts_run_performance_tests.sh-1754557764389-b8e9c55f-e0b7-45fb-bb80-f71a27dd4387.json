{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/run_performance_tests.sh"}, "modifiedCode": "#!/bin/bash\n\n# Performance Testing Script for ExcelSync\n# This script runs various performance tests and generates reports\n\nset -e\n\necho \"🚀 ExcelSync Performance Testing Suite\"\necho \"======================================\"\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Function to print colored output\nprint_status() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nprint_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nprint_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\n# Check if required tools are installed\ncheck_dependencies() {\n    print_status \"Checking dependencies...\"\n    \n    if ! command -v cargo &> /dev/null; then\n        print_error \"Cargo is not installed. Please install Rust and Cargo.\"\n        exit 1\n    fi\n    \n    if ! command -v python3 &> /dev/null; then\n        print_error \"Python 3 is not installed. Please install Python 3.\"\n        exit 1\n    fi\n    \n    # Check if aiohttp is available\n    if ! python3 -c \"import aiohttp\" &> /dev/null; then\n        print_warning \"aiohttp is not installed. Installing...\"\n        pip3 install aiohttp\n    fi\n    \n    print_success \"All dependencies are available\"\n}\n\n# Run Rust benchmarks\nrun_rust_benchmarks() {\n    print_status \"Running Rust performance benchmarks...\"\n    \n    cd \"$(dirname \"$0\")/..\"\n    \n    # Run benchmarks and save output\n    cargo bench --package api > performance_results/rust_benchmarks.txt 2>&1 || {\n        print_error \"Rust benchmarks failed\"\n        return 1\n    }\n    \n    print_success \"Rust benchmarks completed\"\n    \n    # Extract key metrics\n    echo \"\"\n    echo \"📊 Key Benchmark Results:\"\n    echo \"========================\"\n    \n    if [ -f \"performance_results/rust_benchmarks.txt\" ]; then\n        # Extract timing information\n        grep -E \"(password_hash|jwt_generate|jwt_validate)\" performance_results/rust_benchmarks.txt | while read line; do\n            echo \"  $line\"\n        done\n    fi\n}\n\n# Run unit tests with coverage\nrun_unit_tests() {\n    print_status \"Running unit tests...\"\n    \n    cd \"$(dirname \"$0\")/..\"\n    \n    # Run tests\n    cargo test --all > performance_results/unit_test_results.txt 2>&1 || {\n        print_warning \"Some unit tests failed, but continuing...\"\n    }\n    \n    # Count test results\n    local passed=$(grep -c \"test result: ok\" performance_results/unit_test_results.txt || echo \"0\")\n    local total_tests=$(grep -o \"[0-9]* passed\" performance_results/unit_test_results.txt | head -1 | cut -d' ' -f1 || echo \"0\")\n    \n    print_success \"Unit tests completed: $total_tests tests\"\n}\n\n# Run integration tests\nrun_integration_tests() {\n    print_status \"Running integration tests...\"\n    \n    cd \"$(dirname \"$0\")/..\"\n    \n    # Run integration tests\n    cargo test --package api --test integration_tests > performance_results/integration_test_results.txt 2>&1 || {\n        print_warning \"Some integration tests failed, but continuing...\"\n    }\n    \n    cargo test --package api --test security_tests >> performance_results/integration_test_results.txt 2>&1 || {\n        print_warning \"Some security tests failed, but continuing...\"\n    }\n    \n    print_success \"Integration tests completed\"\n}\n\n# Run load tests (if API is running)\nrun_load_tests() {\n    print_status \"Checking if API is running for load tests...\"\n    \n    # Check if API is running on localhost:3000\n    if curl -s http://localhost:3000/health > /dev/null 2>&1; then\n        print_status \"API is running, starting load tests...\"\n        \n        # Run different load test scenarios\n        echo \"\"\n        echo \"🔥 Load Test Scenario 1: Light Load (50 users, 5 requests each)\"\n        python3 scripts/load_test.py --users 50 --requests 5 > performance_results/load_test_light.txt 2>&1 || {\n            print_warning \"Light load test failed\"\n        }\n        \n        echo \"\"\n        echo \"🔥 Load Test Scenario 2: Medium Load (100 users, 10 requests each)\"\n        python3 scripts/load_test.py --users 100 --requests 10 > performance_results/load_test_medium.txt 2>&1 || {\n            print_warning \"Medium load test failed\"\n        }\n        \n        echo \"\"\n        echo \"🔥 Load Test Scenario 3: Heavy Load (200 users, 15 requests each)\"\n        python3 scripts/load_test.py --users 200 --requests 15 > performance_results/load_test_heavy.txt 2>&1 || {\n            print_warning \"Heavy load test failed\"\n        }\n        \n        print_success \"Load tests completed\"\n    else\n        print_warning \"API is not running on localhost:3000. Skipping load tests.\"\n        print_status \"To run load tests, start the API server first with: cargo run --package api\"\n    fi\n}\n\n# Generate performance report\ngenerate_report() {\n    print_status \"Generating performance report...\"\n    \n    local report_file=\"performance_results/performance_report.md\"\n    \n    cat > \"$report_file\" << EOF\n# ExcelSync Performance Test Report\n\nGenerated on: $(date)\n\n## Test Environment\n- OS: $(uname -s)\n- Architecture: $(uname -m)\n- Rust Version: $(rustc --version)\n- Python Version: $(python3 --version)\n\n## Benchmark Results\n\n### Rust Benchmarks\nEOF\n    \n    if [ -f \"performance_results/rust_benchmarks.txt\" ]; then\n        echo \"```\" >> \"$report_file\"\n        tail -20 performance_results/rust_benchmarks.txt >> \"$report_file\"\n        echo \"```\" >> \"$report_file\"\n    fi\n    \n    cat >> \"$report_file\" << EOF\n\n### Unit Test Results\nEOF\n    \n    if [ -f \"performance_results/unit_test_results.txt\" ]; then\n        local test_summary=$(grep \"test result:\" performance_results/unit_test_results.txt | tail -1)\n        echo \"- $test_summary\" >> \"$report_file\"\n    fi\n    \n    cat >> \"$report_file\" << EOF\n\n### Integration Test Results\nEOF\n    \n    if [ -f \"performance_results/integration_test_results.txt\" ]; then\n        local integration_summary=$(grep \"test result:\" performance_results/integration_test_results.txt | tail -1)\n        echo \"- $integration_summary\" >> \"$report_file\"\n    fi\n    \n    cat >> \"$report_file\" << EOF\n\n### Load Test Results\n\n#### Light Load (50 users)\nEOF\n    \n    if [ -f \"performance_results/load_test_light.txt\" ]; then\n        echo \"```\" >> \"$report_file\"\n        grep -A 10 \"LOAD TEST RESULTS\" performance_results/load_test_light.txt >> \"$report_file\" 2>/dev/null || echo \"Load test data not available\" >> \"$report_file\"\n        echo \"```\" >> \"$report_file\"\n    fi\n    \n    print_success \"Performance report generated: $report_file\"\n}\n\n# Main execution\nmain() {\n    # Create results directory\n    mkdir -p performance_results\n    \n    # Run all tests\n    check_dependencies\n    run_rust_benchmarks\n    run_unit_tests\n    run_integration_tests\n    run_load_tests\n    generate_report\n    \n    echo \"\"\n    print_success \"🎉 Performance testing completed!\"\n    print_status \"Check the performance_results/ directory for detailed results\"\n    print_status \"Main report: performance_results/performance_report.md\"\n}\n\n# Run main function\nmain \"$@\"\n"}