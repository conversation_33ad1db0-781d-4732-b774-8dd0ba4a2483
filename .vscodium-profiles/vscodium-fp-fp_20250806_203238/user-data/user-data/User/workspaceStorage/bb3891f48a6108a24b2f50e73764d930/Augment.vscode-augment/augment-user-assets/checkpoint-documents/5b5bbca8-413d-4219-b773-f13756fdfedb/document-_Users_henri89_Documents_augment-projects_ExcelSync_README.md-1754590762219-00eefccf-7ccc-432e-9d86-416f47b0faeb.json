{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "README.md"}, "originalCode": "# ExcelSync\n\n**Zero-Knowledge Web Application for Real Estate Project Management**\n\n## Overview\n\nExcelSync is a modern web application designed for real estate project management with zero-knowledge privacy architecture. The system provides secure data input, automatic synchronization, and robust reporting capabilities through client-side encryption and a PostgreSQL database backend. All user data is encrypted client-side, ensuring neither administrators nor potential attackers can access sensitive information.\n\n## Architecture\n\n### Zero-Knowledge Security Architecture\n\nExcelSync implements a **zero-knowledge privacy architecture** where neither administrators nor potential attackers can access user data. All sensitive information is encrypted client-side before transmission, ensuring maximum security and privacy.\n\n```\nFrontend (WASM) ←→ Backend API\n     ↓                ↓\nCrypto Module    ZK Auth Endpoints\n     ↓                ↓\nChaCha20Poly1305  Encrypted DTOs\n     ↓                ↓\nArgon2 KDF       Session Management\n     ↓                ↓\nLocal Storage    JWT Tokens\n```\n\n### System Components\n- **Web Frontend**: Modern web application with integrated WASM crypto module\n- **WASM Crypto Module**: Client-side encryption/decryption using ChaCha20Poly1305\n- **Backend API**: Zero-knowledge RESTful API handling only encrypted data\n- **PostgreSQL Database**: Encrypted data storage with no plaintext access\n- **ZK Authentication**: Client-side key derivation with Argon2 KDF\n- **Progressive Web App**: Offline capability and native-like experience\n\n### Technology Stack\n- **Frontend**: Modern Web Framework (React/Vue/Svelte) + WebAssembly Crypto Module\n- **Encryption**: ChaCha20Poly1305 authenticated encryption\n- **Key Derivation**: Argon2id KDF for password-based key generation\n- **Backend**: Rust + Axum (zero-knowledge endpoints)\n- **Database**: PostgreSQL (encrypted data only)\n- **Deployment**: Progressive Web App with offline support\n- **Language**: Vietnamese interface\n\n## Database Configuration\n\n### UAT Environment\n```\nHost: *************\nPort: 5432\nDatabase: postgres\nUsername: uat01\nPassword: 123456\n```\n\n### Database Schema\nThe system uses the `Addin` schema with the following core tables:\n- `ADDI_USER_TRAN` - User authentication and management\n- `ORGA_TRAN` - Organization data\n- `PROJ_DSET_TRAN` - Project dataset transactions\n- `PROJ_INDI_TRAN` - Project individual transactions\n- `PROJ_INFO_TYPE` - Project information types\n- `PROJ_LAND_INFO_TRAN` - Project land information\n- `PROJ_VERT_TRAN` - Project vertical transactions\n\n## Core Features\n\n### 1. Zero-Knowledge Authentication\n- **Secure Sign-in**: Client-side key derivation with Argon2 KDF\n- **Session Management**: Encrypted local storage with automatic timeout\n- **Multi-Device Support**: Secure key synchronization across devices\n\n### 2. Encrypted Data Management\n- **Secure Data Entry**: Web forms with client-side encryption\n- **Template System**: Encrypted template storage and retrieval\n- **Version Control**: Encrypted data versioning with conflict resolution\n- **Offline Support**: Local encrypted storage for offline work\n\n### 3. Real-Time Analytics\n- **Encrypted Reports**: Server-side calculations on encrypted data\n- **Live Dashboard**: Real-time encrypted data visualization\n- **Secure Synchronization**: End-to-end encrypted data updates\n\n### 4. Modern Web Interface\n- **Responsive Design**: Mobile-first responsive web interface\n- **Progressive Web App**: Native app-like experience with offline support\n- **Intuitive Navigation**: Modern web UI with hierarchical organization\n- **Multi-language**: Vietnamese language support with i18n\n\n## Menu Structure\n\n### Level 1: Connect\n- Sign-in functionality with database connection\n- Sign-out with session management\n\n### Level 2: Template\n- **Project Land**: Land use cost calculations\n  - Project Information\n  - Project Design\n  - Project Assumptions\n- **Project Land Costing**: Detailed cost analysis\n  - Project Design\n  - Project Contract\n  - Project Cost Actual\n- **Tax Review**: Tax management\n  - Purchase Invoice List\n  - Sales Invoice List\n\n### Level 3: Report\n- **Project Land Cost**: Investment reports and estimates\n- **Dashboard**: Interactive data visualization\n- **Data Refresh**: Real-time synchronization\n\n### Level 4: Help\n- Help documentation\n- About information\n\n## API Integration\n\n### Authentication Endpoints\n- User login with email/password authentication\n- Session management and token handling\n\n### Data Operations\n- Template loading and deployment\n- Data validation and saving\n- Real-time synchronization\n- Version control and conflict resolution\n\n### Template Management\n- Dynamic template retrieval\n- Historical data loading\n- Data overwrite protection with user confirmation\n\n## Development Timeline\n\n### Design Phase (01/08/2025)\n- UI Mockup design (Reference: SAP Analytics Cloud)\n- Icon design and implementation\n- Database schema finalization\n\n### Implementation Phase (05/08/2025)\n- Core functionality development\n- API integration\n- Testing and validation\n\n## User Workflow\n\n### Data Input Process\n1. **Authentication**: User signs in with credentials\n2. **Template Selection**: Choose appropriate template from business area\n3. **Template Loading**: Drag template to active Excel sheet\n4. **Data Entry**: Fill template with project information\n5. **Data Validation**: System validates input data\n6. **Save Operation**: Data synchronized to backend database\n7. **Confirmation**: User receives save confirmation\n\n### Template Management\n- **First-time Use**: Complete template setup and data entry\n- **Updates**: Load existing data and create new versions\n- **Conflict Resolution**: Handle data overwrite scenarios with user prompts\n\n## Zero-Knowledge Security Architecture\n\n### Core Security Principles\n\n**Zero-Knowledge Privacy**: The server never sees plaintext data. All sensitive information is encrypted client-side using military-grade cryptography before transmission.\n\n**Administrator Blindness**: System administrators cannot access user data, even with full server access, as all data is encrypted with user-derived keys.\n\n**Hacker Resilience**: Even if the server is compromised, attackers only gain access to encrypted data that is computationally infeasible to decrypt.\n\n### Cryptographic Implementation\n\n#### Client-Side Encryption Stack\n- **ChaCha20Poly1305**: Modern authenticated encryption cipher providing both confidentiality and authenticity\n- **Argon2id KDF**: Memory-hard key derivation function resistant to GPU attacks\n- **WebAssembly Module**: High-performance cryptographic operations isolated from JavaScript\n- **Secure Key Storage**: Browser-native secure storage for encrypted session keys\n\n#### Security Flow\n1. **Password Entry**: User enters password in web application\n2. **Key Derivation**: Argon2id derives encryption key client-side (never transmitted)\n3. **Data Encryption**: All data encrypted with ChaCha20Poly1305 before API calls\n4. **Encrypted Transmission**: Only encrypted payloads sent to server\n5. **Server Processing**: Backend performs calculations on encrypted data\n6. **Encrypted Storage**: PostgreSQL stores only encrypted data\n7. **Client Decryption**: Data decrypted client-side for display in web interface\n\n### Authentication & Session Management\n\n#### Zero-Knowledge Authentication\n- **Client-Side Key Derivation**: Passwords never leave the client device\n- **Encrypted Session Tokens**: JWT tokens contain encrypted session data\n- **Local Key Storage**: Encryption keys stored securely in browser storage\n- **Automatic Key Rotation**: Regular key rotation for enhanced security\n\n#### Session Security\n- **Encrypted Session State**: All session data encrypted before local storage\n- **Automatic Timeout**: Sessions expire automatically with secure cleanup\n- **Multi-Device Support**: Secure key synchronization across devices\n- **Revocation Capability**: Immediate session termination and key invalidation\n\n## Security Architecture Benefits\n\n### Privacy Guarantees\n- **Complete Data Privacy**: User data remains encrypted at all times\n- **Zero Server Knowledge**: Backend never processes plaintext information\n- **Administrator Protection**: System admins cannot access user data\n- **Compliance Ready**: Meets strictest data protection regulations\n\n### Performance & Security Balance\n- **WASM Performance**: Near-native speed for cryptographic operations\n- **Minimal Overhead**: Efficient encryption with ChaCha20Poly1305\n- **Optimized Key Derivation**: Tuned Argon2 parameters for security/performance\n- **Smart Caching**: Encrypted local storage reduces server round-trips\n\n### Threat Model Protection\n- **Data Breach Resilience**: Stolen data is cryptographically protected\n- **Insider Threat Mitigation**: No privileged access to plaintext data\n- **Man-in-the-Middle Protection**: End-to-end encryption prevents interception\n- **Quantum Resistance**: Modern ciphers with post-quantum considerations\n\n## Performance Optimization\n\n### Cryptographic Performance\n- **WASM Acceleration**: WebAssembly provides near-native cryptographic performance\n- **Efficient Ciphers**: ChaCha20Poly1305 optimized for modern processors\n- **Smart Key Caching**: Derived keys cached securely to minimize computation\n- **Batch Operations**: Multiple data items encrypted in single operations\n\n### Data Handling\n- **Encrypted Template Loading**: Templates encrypted client-side before storage\n- **Optimized Database Queries**: Server queries on encrypted data with indexing\n- **Minimal Network Overhead**: Compressed encrypted payloads\n- **Progressive Decryption**: Large datasets decrypted incrementally\n\n### User Experience\n- **Transparent Security**: Encryption/decryption happens seamlessly\n- **Progress Indicators**: Clear feedback during cryptographic operations\n- **Error Handling**: Comprehensive error recovery for security failures\n- **Offline Capability**: Local encrypted storage enables offline work\n", "modifiedCode": "# ExcelSync\n\n**Zero-Knowledge Web Application for Real Estate Project Management**\n\n## Overview\n\nExcelSync is a modern web application designed for real estate project management with zero-knowledge privacy architecture. The system provides secure data input, automatic synchronization, and robust reporting capabilities through client-side encryption and a PostgreSQL database backend. All user data is encrypted client-side, ensuring neither administrators nor potential attackers can access sensitive information.\n\n## Architecture\n\n### Zero-Knowledge Security Architecture\n\nExcelSync implements a **zero-knowledge privacy architecture** where neither administrators nor potential attackers can access user data. All sensitive information is encrypted client-side before transmission, ensuring maximum security and privacy.\n\n```\nFrontend (WASM) ←→ Backend API\n     ↓                ↓\nCrypto Module    ZK Auth Endpoints\n     ↓                ↓\nChaCha20Poly1305  Encrypted DTOs\n     ↓                ↓\nArgon2 KDF       Session Management\n     ↓                ↓\nLocal Storage    JWT Tokens\n```\n\n### System Components\n- **Web Frontend**: Modern web application with integrated WASM crypto module\n- **WASM Crypto Module**: Client-side encryption/decryption using ChaCha20Poly1305\n- **Backend API**: Zero-knowledge RESTful API handling only encrypted data\n- **PostgreSQL Database**: Encrypted data storage with no plaintext access\n- **ZK Authentication**: Client-side key derivation with Argon2 KDF\n- **Progressive Web App**: Offline capability and native-like experience\n\n### Technology Stack\n- **Frontend**: Modern Web Framework (React/Vue/Svelte) + WebAssembly Crypto Module\n- **Encryption**: ChaCha20Poly1305 authenticated encryption\n- **Key Derivation**: Argon2id KDF for password-based key generation\n- **Backend**: Rust + Axum (zero-knowledge endpoints)\n- **Database**: PostgreSQL (encrypted data only)\n- **Deployment**: Progressive Web App with offline support\n- **Language**: Vietnamese interface\n\n## Database Configuration\n\n### UAT Environment\n```\nHost: *************\nPort: 5432\nDatabase: postgres\nUsername: uat01\nPassword: 123456\n```\n\n### Database Schema\nThe system uses the `Addin` schema with the following core tables:\n- `ADDI_USER_TRAN` - User authentication and management\n- `ORGA_TRAN` - Organization data\n- `PROJ_DSET_TRAN` - Project dataset transactions\n- `PROJ_INDI_TRAN` - Project individual transactions\n- `PROJ_INFO_TYPE` - Project information types\n- `PROJ_LAND_INFO_TRAN` - Project land information\n- `PROJ_VERT_TRAN` - Project vertical transactions\n\n## Core Features\n\n### 1. Zero-Knowledge Authentication\n- **Secure Sign-in**: Client-side key derivation with Argon2 KDF\n- **Session Management**: Encrypted local storage with automatic timeout\n- **Multi-Device Support**: Secure key synchronization across devices\n\n### 2. Encrypted Data Management\n- **Secure Data Entry**: Web forms with client-side encryption\n- **Template System**: Encrypted template storage and retrieval\n- **Version Control**: Encrypted data versioning with conflict resolution\n- **Offline Support**: Local encrypted storage for offline work\n\n### 3. Real-Time Analytics\n- **Encrypted Reports**: Server-side calculations on encrypted data\n- **Live Dashboard**: Real-time encrypted data visualization\n- **Secure Synchronization**: End-to-end encrypted data updates\n\n### 4. Modern Web Interface\n- **Responsive Design**: Mobile-first responsive web interface\n- **Progressive Web App**: Native app-like experience with offline support\n- **Intuitive Navigation**: Modern web UI with hierarchical organization\n- **Multi-language**: Vietnamese language support with i18n\n\n## Menu Structure\n\n### Level 1: Connect\n- Sign-in functionality with database connection\n- Sign-out with session management\n\n### Level 2: Template\n- **Project Land**: Land use cost calculations\n  - Project Information\n  - Project Design\n  - Project Assumptions\n- **Project Land Costing**: Detailed cost analysis\n  - Project Design\n  - Project Contract\n  - Project Cost Actual\n- **Tax Review**: Tax management\n  - Purchase Invoice List\n  - Sales Invoice List\n\n### Level 3: Report\n- **Project Land Cost**: Investment reports and estimates\n- **Dashboard**: Interactive data visualization\n- **Data Refresh**: Real-time synchronization\n\n### Level 4: Help\n- Help documentation\n- About information\n\n## API Integration\n\n### Authentication Endpoints\n- User login with email/password authentication\n- Session management and token handling\n\n### Data Operations\n- Template loading and deployment\n- Data validation and saving\n- Real-time synchronization\n- Version control and conflict resolution\n\n### Template Management\n- Dynamic template retrieval\n- Historical data loading\n- Data overwrite protection with user confirmation\n\n## Development Timeline\n\n### Design Phase (01/08/2025)\n- UI Mockup design (Reference: SAP Analytics Cloud)\n- Icon design and implementation\n- Database schema finalization\n\n### Implementation Phase (05/08/2025)\n- Core functionality development\n- API integration\n- Testing and validation\n\n## User Workflow\n\n### Data Input Process\n1. **Authentication**: User signs in with credentials\n2. **Template Selection**: Choose appropriate template from business area\n3. **Template Loading**: Drag template to active Excel sheet\n4. **Data Entry**: Fill template with project information\n5. **Data Validation**: System validates input data\n6. **Save Operation**: Data synchronized to backend database\n7. **Confirmation**: User receives save confirmation\n\n### Template Management\n- **First-time Use**: Complete template setup and data entry\n- **Updates**: Load existing data and create new versions\n- **Conflict Resolution**: Handle data overwrite scenarios with user prompts\n\n## Zero-Knowledge Security Architecture\n\n### Core Security Principles\n\n**Zero-Knowledge Privacy**: The server never sees plaintext data. All sensitive information is encrypted client-side using military-grade cryptography before transmission.\n\n**Administrator Blindness**: System administrators cannot access user data, even with full server access, as all data is encrypted with user-derived keys.\n\n**Hacker Resilience**: Even if the server is compromised, attackers only gain access to encrypted data that is computationally infeasible to decrypt.\n\n### Cryptographic Implementation\n\n#### Client-Side Encryption Stack\n- **ChaCha20Poly1305**: Modern authenticated encryption cipher providing both confidentiality and authenticity\n- **Argon2id KDF**: Memory-hard key derivation function resistant to GPU attacks\n- **WebAssembly Module**: High-performance cryptographic operations isolated from JavaScript\n- **Browser Secure Storage**: IndexedDB and Web Crypto API for encrypted session keys\n- **Service Worker**: Offline encryption/decryption capability\n\n#### Security Flow\n1. **Password Entry**: User enters password in web application\n2. **Key Derivation**: Argon2id derives encryption key client-side (never transmitted)\n3. **Data Encryption**: All data encrypted with ChaCha20Poly1305 before API calls\n4. **Encrypted Transmission**: Only encrypted payloads sent to server\n5. **Server Processing**: Backend performs calculations on encrypted data\n6. **Encrypted Storage**: PostgreSQL stores only encrypted data\n7. **Client Decryption**: Data decrypted client-side for display in web interface\n\n### Authentication & Session Management\n\n#### Zero-Knowledge Authentication\n- **Client-Side Key Derivation**: Passwords never leave the client device\n- **Encrypted Session Tokens**: JWT tokens contain encrypted session data\n- **Local Key Storage**: Encryption keys stored securely in browser storage\n- **Automatic Key Rotation**: Regular key rotation for enhanced security\n\n#### Session Security\n- **Encrypted Session State**: All session data encrypted before local storage\n- **Automatic Timeout**: Sessions expire automatically with secure cleanup\n- **Multi-Device Support**: Secure key synchronization across devices\n- **Revocation Capability**: Immediate session termination and key invalidation\n\n## Security Architecture Benefits\n\n### Privacy Guarantees\n- **Complete Data Privacy**: User data remains encrypted at all times\n- **Zero Server Knowledge**: Backend never processes plaintext information\n- **Administrator Protection**: System admins cannot access user data\n- **Compliance Ready**: Meets strictest data protection regulations\n\n### Performance & Security Balance\n- **WASM Performance**: Near-native speed for cryptographic operations\n- **Minimal Overhead**: Efficient encryption with ChaCha20Poly1305\n- **Optimized Key Derivation**: Tuned Argon2 parameters for security/performance\n- **Smart Caching**: Encrypted local storage reduces server round-trips\n\n### Threat Model Protection\n- **Data Breach Resilience**: Stolen data is cryptographically protected\n- **Insider Threat Mitigation**: No privileged access to plaintext data\n- **Man-in-the-Middle Protection**: End-to-end encryption prevents interception\n- **Quantum Resistance**: Modern ciphers with post-quantum considerations\n\n## Performance Optimization\n\n### Cryptographic Performance\n- **WASM Acceleration**: WebAssembly provides near-native cryptographic performance\n- **Efficient Ciphers**: ChaCha20Poly1305 optimized for modern processors\n- **Smart Key Caching**: Derived keys cached securely to minimize computation\n- **Batch Operations**: Multiple data items encrypted in single operations\n\n### Data Handling\n- **Encrypted Template Loading**: Templates encrypted client-side before storage\n- **Optimized Database Queries**: Server queries on encrypted data with indexing\n- **Minimal Network Overhead**: Compressed encrypted payloads\n- **Progressive Decryption**: Large datasets decrypted incrementally\n\n### User Experience\n- **Transparent Security**: Encryption/decryption happens seamlessly\n- **Progress Indicators**: Clear feedback during cryptographic operations\n- **Error Handling**: Comprehensive error recovery for security failures\n- **Offline Capability**: Local encrypted storage enables offline work\n"}