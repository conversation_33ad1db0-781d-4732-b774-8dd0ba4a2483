{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/mod.rs"}, "originalCode": "pub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod project_data;\npub mod sessions;\npub mod audit_logs;\n\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use project_data::*;\npub use sessions::*;\npub use audit_logs::*;\n\nuse chrono::{DateTime, Utc};\nuse sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\n/// Common fields for all entities\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Timestamps {\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n/// User roles in the system\n#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"user_role\")]\npub enum UserRole {\n    #[sea_orm(string_value = \"admin\")]\n    Admin,\n    #[sea_orm(string_value = \"project_manager\")]\n    ProjectManager,\n    #[sea_orm(string_value = \"analyst\")]\n    Analyst,\n    #[sea_orm(string_value = \"viewer\")]\n    Viewer,\n}\n\nimpl std::fmt::Display for UserRole {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            UserRole::Admin => write!(f, \"admin\"),\n            UserRole::ProjectManager => write!(f, \"project_manager\"),\n            UserRole::Analyst => write!(f, \"analyst\"),\n            UserRole::Viewer => write!(f, \"viewer\"),\n        }\n    }\n}\n\n/// Project types for real estate\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"project_type\")]\npub enum ProjectType {\n    #[sea_orm(string_value = \"land_development\")]\n    LandDevelopment,\n    #[sea_orm(string_value = \"residential_building\")]\n    ResidentialBuilding,\n    #[sea_orm(string_value = \"commercial_building\")]\n    CommercialBuilding,\n    #[sea_orm(string_value = \"mixed_use\")]\n    MixedUse,\n}\n\nimpl std::fmt::Display for ProjectType {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            ProjectType::LandDevelopment => write!(f, \"land_development\"),\n            ProjectType::ResidentialBuilding => write!(f, \"residential_building\"),\n            ProjectType::CommercialBuilding => write!(f, \"commercial_building\"),\n            ProjectType::MixedUse => write!(f, \"mixed_use\"),\n        }\n    }\n}\n\n/// Project status\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"project_status\")]\npub enum ProjectStatus {\n    #[sea_orm(string_value = \"planning\")]\n    Planning,\n    #[sea_orm(string_value = \"in_progress\")]\n    InProgress,\n    #[sea_orm(string_value = \"on_hold\")]\n    OnHold,\n    #[sea_orm(string_value = \"completed\")]\n    Completed,\n    #[sea_orm(string_value = \"cancelled\")]\n    Cancelled,\n}\n\nimpl std::fmt::Display for ProjectStatus {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            ProjectStatus::Planning => write!(f, \"planning\"),\n            ProjectStatus::InProgress => write!(f, \"in_progress\"),\n            ProjectStatus::OnHold => write!(f, \"on_hold\"),\n            ProjectStatus::Completed => write!(f, \"completed\"),\n            ProjectStatus::Cancelled => write!(f, \"cancelled\"),\n        }\n    }\n}\n\n/// Template types\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"template_type\")]\npub enum TemplateType {\n    #[sea_orm(string_value = \"project_land_info\")]\n    ProjectLandInfo,\n    #[sea_orm(string_value = \"project_design\")]\n    ProjectDesign,\n    #[sea_orm(string_value = \"project_assumptions\")]\n    ProjectAssumptions,\n    #[sea_orm(string_value = \"project_costing\")]\n    ProjectCosting,\n    #[sea_orm(string_value = \"tax_review\")]\n    TaxReview,\n}\n", "modifiedCode": "pub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod project_data;\npub mod sessions;\npub mod audit_logs;\n\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use project_data::*;\npub use sessions::*;\npub use audit_logs::*;\n\nuse chrono::{DateTime, Utc};\nuse sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\n/// Common fields for all entities\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Timestamps {\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n/// User roles in the system\n#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"user_role\")]\npub enum UserRole {\n    #[sea_orm(string_value = \"admin\")]\n    Admin,\n    #[sea_orm(string_value = \"project_manager\")]\n    ProjectManager,\n    #[sea_orm(string_value = \"analyst\")]\n    Analyst,\n    #[sea_orm(string_value = \"viewer\")]\n    Viewer,\n}\n\nimpl std::fmt::Display for UserRole {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            UserRole::Admin => write!(f, \"admin\"),\n            UserRole::ProjectManager => write!(f, \"project_manager\"),\n            UserRole::Analyst => write!(f, \"analyst\"),\n            UserRole::Viewer => write!(f, \"viewer\"),\n        }\n    }\n}\n\n/// Project types for real estate\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"project_type\")]\npub enum ProjectType {\n    #[sea_orm(string_value = \"land_development\")]\n    LandDevelopment,\n    #[sea_orm(string_value = \"residential_building\")]\n    ResidentialBuilding,\n    #[sea_orm(string_value = \"commercial_building\")]\n    CommercialBuilding,\n    #[sea_orm(string_value = \"mixed_use\")]\n    MixedUse,\n}\n\nimpl std::fmt::Display for ProjectType {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            ProjectType::LandDevelopment => write!(f, \"land_development\"),\n            ProjectType::ResidentialBuilding => write!(f, \"residential_building\"),\n            ProjectType::CommercialBuilding => write!(f, \"commercial_building\"),\n            ProjectType::MixedUse => write!(f, \"mixed_use\"),\n        }\n    }\n}\n\n/// Project status\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"project_status\")]\npub enum ProjectStatus {\n    #[sea_orm(string_value = \"planning\")]\n    Planning,\n    #[sea_orm(string_value = \"in_progress\")]\n    InProgress,\n    #[sea_orm(string_value = \"on_hold\")]\n    OnHold,\n    #[sea_orm(string_value = \"completed\")]\n    Completed,\n    #[sea_orm(string_value = \"cancelled\")]\n    Cancelled,\n}\n\nimpl std::fmt::Display for ProjectStatus {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        match self {\n            ProjectStatus::Planning => write!(f, \"planning\"),\n            ProjectStatus::InProgress => write!(f, \"in_progress\"),\n            ProjectStatus::OnHold => write!(f, \"on_hold\"),\n            ProjectStatus::Completed => write!(f, \"completed\"),\n            ProjectStatus::Cancelled => write!(f, \"cancelled\"),\n        }\n    }\n}\n\n/// Template types\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"template_type\")]\npub enum TemplateType {\n    #[sea_orm(string_value = \"project_land_info\")]\n    ProjectLandInfo,\n    #[sea_orm(string_value = \"project_design\")]\n    ProjectDesign,\n    #[sea_orm(string_value = \"project_assumptions\")]\n    ProjectAssumptions,\n    #[sea_orm(string_value = \"project_costing\")]\n    ProjectCosting,\n    #[sea_orm(string_value = \"tax_review\")]\n    TaxReview,\n}\n"}