{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "excelsync-database/src/connection.rs"}, "modifiedCode": "use anyhow::{Context, Result};\nuse sea_orm::{Database, DatabaseConnection as SeaOrmConnection, DbErr, ConnectOptions};\nuse std::time::Duration;\nuse tracing::{info, warn};\n\n#[derive(Clone)]\npub struct DatabaseConnection {\n    pub connection: SeaOrmConnection,\n}\n\nimpl DatabaseConnection {\n    /// Create a new database connection with optimized pool settings\n    pub async fn new(database_url: &str) -> Result<Self> {\n        let mut opt = ConnectOptions::new(database_url);\n        \n        // Connection pool configuration\n        opt.max_connections(20)\n            .min_connections(5)\n            .connect_timeout(Duration::from_secs(30))\n            .acquire_timeout(Duration::from_secs(30))\n            .idle_timeout(Duration::from_secs(600))\n            .max_lifetime(Duration::from_secs(3600))\n            .sqlx_logging(true)\n            .sqlx_logging_level(log::LevelFilter::Debug);\n\n        info!(\"Connecting to database with pool settings:\");\n        info!(\"  Max connections: 20\");\n        info!(\"  Min connections: 5\");\n        info!(\"  Connect timeout: 30s\");\n        info!(\"  Idle timeout: 600s\");\n        info!(\"  Max lifetime: 3600s\");\n\n        let connection = Database::connect(opt)\n            .await\n            .context(\"Failed to connect to database\")?;\n\n        // Test the connection\n        Self::health_check(&connection).await?;\n        info!(\"Database connection established successfully\");\n\n        Ok(Self { connection })\n    }\n\n    /// Health check for database connection\n    pub async fn health_check(db: &SeaOrmConnection) -> Result<(), DbErr> {\n        db.ping().await\n    }\n\n    /// Get the underlying SeaORM connection\n    pub fn get_connection(&self) -> &SeaOrmConnection {\n        &self.connection\n    }\n\n    /// Close the database connection\n    pub async fn close(self) -> Result<()> {\n        self.connection.close().await?;\n        info!(\"Database connection closed\");\n        Ok(())\n    }\n\n    /// Get connection pool statistics\n    pub fn get_pool_stats(&self) -> PoolStats {\n        // Note: SeaORM doesn't expose pool stats directly\n        // This is a placeholder for monitoring\n        PoolStats {\n            active_connections: 0,\n            idle_connections: 0,\n            total_connections: 0,\n        }\n    }\n}\n\n#[derive(Debug, Clone)]\npub struct PoolStats {\n    pub active_connections: u32,\n    pub idle_connections: u32,\n    pub total_connections: u32,\n}\n\n/// Database configuration for connection pooling\n#[derive(Debug, Clone)]\npub struct DatabaseConfig {\n    pub url: String,\n    pub max_connections: u32,\n    pub min_connections: u32,\n    pub connection_timeout: u64,\n    pub idle_timeout: u64,\n    pub max_lifetime: u64,\n}\n\nimpl Default for DatabaseConfig {\n    fn default() -> Self {\n        Self {\n            url: \"postgresql://localhost:5432/excelsync\".to_string(),\n            max_connections: 20,\n            min_connections: 5,\n            connection_timeout: 30,\n            idle_timeout: 600,\n            max_lifetime: 3600,\n        }\n    }\n}\n\nimpl DatabaseConfig {\n    /// Create database connection with custom configuration\n    pub async fn connect(&self) -> Result<DatabaseConnection> {\n        let mut opt = ConnectOptions::new(&self.url);\n        \n        opt.max_connections(self.max_connections)\n            .min_connections(self.min_connections)\n            .connect_timeout(Duration::from_secs(self.connection_timeout))\n            .acquire_timeout(Duration::from_secs(self.connection_timeout))\n            .idle_timeout(Duration::from_secs(self.idle_timeout))\n            .max_lifetime(Duration::from_secs(self.max_lifetime))\n            .sqlx_logging(true);\n\n        let connection = Database::connect(opt)\n            .await\n            .context(\"Failed to connect to database with custom config\")?;\n\n        DatabaseConnection::health_check(&connection).await?;\n        \n        Ok(DatabaseConnection { connection })\n    }\n\n    /// Validate configuration parameters\n    pub fn validate(&self) -> Result<()> {\n        if self.max_connections < self.min_connections {\n            return Err(anyhow::anyhow!(\n                \"max_connections ({}) must be >= min_connections ({})\",\n                self.max_connections,\n                self.min_connections\n            ));\n        }\n\n        if self.min_connections == 0 {\n            return Err(anyhow::anyhow!(\"min_connections must be > 0\"));\n        }\n\n        if self.connection_timeout == 0 {\n            return Err(anyhow::anyhow!(\"connection_timeout must be > 0\"));\n        }\n\n        Ok(())\n    }\n}\n"}