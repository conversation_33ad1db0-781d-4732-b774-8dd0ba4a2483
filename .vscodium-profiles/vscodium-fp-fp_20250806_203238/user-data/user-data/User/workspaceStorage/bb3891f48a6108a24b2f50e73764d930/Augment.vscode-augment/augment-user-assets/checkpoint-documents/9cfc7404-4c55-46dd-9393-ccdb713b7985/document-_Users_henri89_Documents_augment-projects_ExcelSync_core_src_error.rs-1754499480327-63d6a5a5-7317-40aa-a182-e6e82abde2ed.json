{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/error.rs"}, "modifiedCode": "use thiserror::Error;\n\n/// Core application errors\n#[derive(Debug, Error)]\npub enum CoreError {\n    #[error(\"Configuration error: {0}\")]\n    ConfigError(String),\n    \n    #[error(\"Validation error: {0}\")]\n    ValidationError(String),\n    \n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    \n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n    \n    #[error(\"Authorization error: {0}\")]\n    AuthzError(String),\n    \n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n    \n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n    \n    #[error(\"Internal server error: {0}\")]\n    InternalError(String),\n    \n    #[error(\"Service unavailable: {0}\")]\n    ServiceUnavailable(String),\n}\n\nimpl CoreError {\n    /// Get HTTP status code for error\n    pub fn status_code(&self) -> u16 {\n        match self {\n            CoreError::ValidationError(_) | CoreError::BadRequest(_) => 400,\n            CoreError::AuthError(_) => 401,\n            CoreError::AuthzError(_) => 403,\n            CoreError::NotFound(_) => 404,\n            CoreError::ServiceUnavailable(_) => 503,\n            _ => 500,\n        }\n    }\n    \n    /// Check if error is client error (4xx)\n    pub fn is_client_error(&self) -> bool {\n        let code = self.status_code();\n        code >= 400 && code < 500\n    }\n    \n    /// Check if error is server error (5xx)\n    pub fn is_server_error(&self) -> bool {\n        let code = self.status_code();\n        code >= 500\n    }\n}\n\n/// Result type alias for core operations\npub type CoreResult<T> = Result<T, CoreError>;\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_error_status_codes() {\n        assert_eq!(CoreError::BadRequest(\"test\".to_string()).status_code(), 400);\n        assert_eq!(CoreError::AuthError(\"test\".to_string()).status_code(), 401);\n        assert_eq!(CoreError::AuthzError(\"test\".to_string()).status_code(), 403);\n        assert_eq!(CoreError::NotFound(\"test\".to_string()).status_code(), 404);\n        assert_eq!(CoreError::InternalError(\"test\".to_string()).status_code(), 500);\n        assert_eq!(CoreError::ServiceUnavailable(\"test\".to_string()).status_code(), 503);\n    }\n\n    #[test]\n    fn test_error_classification() {\n        let client_error = CoreError::BadRequest(\"test\".to_string());\n        assert!(client_error.is_client_error());\n        assert!(!client_error.is_server_error());\n\n        let server_error = CoreError::InternalError(\"test\".to_string());\n        assert!(!server_error.is_client_error());\n        assert!(server_error.is_server_error());\n    }\n}\n"}