{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/health_service.rs"}, "modifiedCode": "use axum::{extract::State, http::StatusCode, response::<PERSON><PERSON>};\nuse chrono::{DateTime, Utc};\nuse serde::{Deserialize, Serialize};\nuse std::time::{Duration, SystemTime, UNIX_EPOCH};\nuse tracing::{error, info};\n\nuse crate::AppState;\n\n/// Health check status\n#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum HealthStatus {\n    Healthy,\n    Degraded,\n    Unhealthy,\n}\n\n/// Individual component health\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ComponentHealth {\n    pub status: HealthStatus,\n    pub message: Option<String>,\n    pub last_checked: DateTime<Utc>,\n    pub response_time_ms: Option<u64>,\n}\n\n/// Overall system health response\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct HealthResponse {\n    pub status: HealthStatus,\n    pub timestamp: DateTime<Utc>,\n    pub uptime_seconds: u64,\n    pub version: String,\n    pub components: HealthComponents,\n}\n\n/// Health status of all system components\n#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]\npub struct HealthComponents {\n    pub database: ComponentHealth,\n    pub redis: ComponentHealth,\n    pub api: ComponentHealth,\n    pub auth: ComponentHealth,\n}\n\n/// Readiness check response\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ReadinessResponse {\n    pub ready: bool,\n    pub timestamp: DateTime<Utc>,\n    pub checks: ReadinessChecks,\n}\n\n/// Readiness checks for all critical components\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct ReadinessChecks {\n    pub database_connected: bool,\n    pub redis_connected: bool,\n    pub migrations_applied: bool,\n    pub configuration_loaded: bool,\n}\n\n/// System metrics for monitoring\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct SystemMetrics {\n    pub timestamp: DateTime<Utc>,\n    pub uptime_seconds: u64,\n    pub memory_usage_mb: Option<f64>,\n    pub cpu_usage_percent: Option<f64>,\n    pub active_connections: u32,\n    pub total_requests: u64,\n    pub error_rate_percent: f64,\n    pub average_response_time_ms: f64,\n}\n\n/// Health service for monitoring system status\npub struct HealthService {\n    start_time: SystemTime,\n}\n\nimpl HealthService {\n    /// Create new health service\n    pub fn new() -> Self {\n        Self {\n            start_time: SystemTime::now(),\n        }\n    }\n\n    /// Get system uptime in seconds\n    pub fn get_uptime(&self) -> u64 {\n        self.start_time\n            .elapsed()\n            .unwrap_or(Duration::from_secs(0))\n            .as_secs()\n    }\n\n    /// Perform comprehensive health check\n    pub async fn health_check(&self, state: &AppState) -> HealthResponse {\n        let timestamp = Utc::now();\n        let uptime = self.get_uptime();\n\n        // Check database health\n        let database = self.check_database_health(&state.db).await;\n        \n        // Check Redis health\n        let redis = self.check_redis_health(&state.redis).await;\n        \n        // Check API health\n        let api = self.check_api_health().await;\n        \n        // Check auth service health\n        let auth = self.check_auth_health().await;\n\n        // Determine overall status\n        let overall_status = self.determine_overall_status(&[\n            &database.status,\n            &redis.status,\n            &api.status,\n            &auth.status,\n        ]);\n\n        HealthResponse {\n            status: overall_status,\n            timestamp,\n            uptime_seconds: uptime,\n            version: env!(\"CARGO_PKG_VERSION\").to_string(),\n            components: HealthComponents {\n                database,\n                redis,\n                api,\n                auth,\n            },\n        }\n    }\n\n    /// Perform readiness check\n    pub async fn readiness_check(&self, state: &AppState) -> ReadinessResponse {\n        let timestamp = Utc::now();\n\n        // Check database connection\n        let database_connected = self.is_database_ready(&state.db).await;\n        \n        // Check Redis connection\n        let redis_connected = self.is_redis_ready(&state.redis).await;\n        \n        // Check if migrations are applied (simplified check)\n        let migrations_applied = database_connected; // Assume if DB is connected, migrations are applied\n        \n        // Check if configuration is loaded\n        let configuration_loaded = true; // If we're running, config is loaded\n\n        let ready = database_connected && redis_connected && migrations_applied && configuration_loaded;\n\n        ReadinessResponse {\n            ready,\n            timestamp,\n            checks: ReadinessChecks {\n                database_connected,\n                redis_connected,\n                migrations_applied,\n                configuration_loaded,\n            },\n        }\n    }\n\n    /// Check database health\n    async fn check_database_health(&self, db: &sea_orm::DatabaseConnection) -> ComponentHealth {\n        let start = SystemTime::now();\n        let last_checked = Utc::now();\n\n        match sqlx::query(\"SELECT 1\").execute(db.get_database_backend().get_query_executor()).await {\n            Ok(_) => {\n                let response_time = start.elapsed().unwrap_or_default().as_millis() as u64;\n                ComponentHealth {\n                    status: HealthStatus::Healthy,\n                    message: Some(\"Database connection successful\".to_string()),\n                    last_checked,\n                    response_time_ms: Some(response_time),\n                }\n            }\n            Err(e) => {\n                error!(\"Database health check failed: {}\", e);\n                ComponentHealth {\n                    status: HealthStatus::Unhealthy,\n                    message: Some(format!(\"Database connection failed: {}\", e)),\n                    last_checked,\n                    response_time_ms: None,\n                }\n            }\n        }\n    }\n\n    /// Check Redis health\n    async fn check_redis_health(&self, redis: &redis::Client) -> ComponentHealth {\n        let start = SystemTime::now();\n        let last_checked = Utc::now();\n\n        match redis.get_connection_manager().await {\n            Ok(mut conn) => {\n                match redis::cmd(\"PING\").query_async::<_, String>(&mut conn).await {\n                    Ok(_) => {\n                        let response_time = start.elapsed().unwrap_or_default().as_millis() as u64;\n                        ComponentHealth {\n                            status: HealthStatus::Healthy,\n                            message: Some(\"Redis connection successful\".to_string()),\n                            last_checked,\n                            response_time_ms: Some(response_time),\n                        }\n                    }\n                    Err(e) => {\n                        error!(\"Redis ping failed: {}\", e);\n                        ComponentHealth {\n                            status: HealthStatus::Unhealthy,\n                            message: Some(format!(\"Redis ping failed: {}\", e)),\n                            last_checked,\n                            response_time_ms: None,\n                        }\n                    }\n                }\n            }\n            Err(e) => {\n                error!(\"Redis connection failed: {}\", e);\n                ComponentHealth {\n                    status: HealthStatus::Unhealthy,\n                    message: Some(format!(\"Redis connection failed: {}\", e)),\n                    last_checked,\n                    response_time_ms: None,\n                }\n            }\n        }\n    }\n\n    /// Check API health\n    async fn check_api_health(&self) -> ComponentHealth {\n        ComponentHealth {\n            status: HealthStatus::Healthy,\n            message: Some(\"API service running\".to_string()),\n            last_checked: Utc::now(),\n            response_time_ms: Some(1), // API is always responsive if we can execute this\n        }\n    }\n\n    /// Check auth service health\n    async fn check_auth_health(&self) -> ComponentHealth {\n        // TODO: Add actual auth service health checks\n        ComponentHealth {\n            status: HealthStatus::Healthy,\n            message: Some(\"Auth service running\".to_string()),\n            last_checked: Utc::now(),\n            response_time_ms: Some(1),\n        }\n    }\n\n    /// Check if database is ready\n    async fn is_database_ready(&self, db: &sea_orm::DatabaseConnection) -> bool {\n        sqlx::query(\"SELECT 1\")\n            .execute(db.get_database_backend().get_query_executor())\n            .await\n            .is_ok()\n    }\n\n    /// Check if Redis is ready\n    async fn is_redis_ready(&self, redis: &redis::Client) -> bool {\n        match redis.get_connection_manager().await {\n            Ok(mut conn) => {\n                redis::cmd(\"PING\")\n                    .query_async::<_, String>(&mut conn)\n                    .await\n                    .is_ok()\n            }\n            Err(_) => false,\n        }\n    }\n\n    /// Determine overall system status based on component statuses\n    fn determine_overall_status(&self, statuses: &[&HealthStatus]) -> HealthStatus {\n        if statuses.iter().any(|s| matches!(s, HealthStatus::Unhealthy)) {\n            HealthStatus::Unhealthy\n        } else if statuses.iter().any(|s| matches!(s, HealthStatus::Degraded)) {\n            HealthStatus::Degraded\n        } else {\n            HealthStatus::Healthy\n        }\n    }\n}\n\nimpl Default for HealthService {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n/// Health check endpoint handler\npub async fn health_handler(State(state): State<AppState>) -> Result<Json<HealthResponse>, StatusCode> {\n    let health_service = HealthService::new();\n    let health = health_service.health_check(&state).await;\n    \n    let status_code = match health.status {\n        HealthStatus::Healthy => StatusCode::OK,\n        HealthStatus::Degraded => StatusCode::OK, // Still return 200 for degraded\n        HealthStatus::Unhealthy => StatusCode::SERVICE_UNAVAILABLE,\n    };\n\n    info!(\"Health check completed with status: {:?}\", health.status);\n    \n    match status_code {\n        StatusCode::OK => Ok(Json(health)),\n        _ => Err(status_code),\n    }\n}\n\n/// Readiness check endpoint handler\npub async fn readiness_handler(State(state): State<AppState>) -> Result<Json<ReadinessResponse>, StatusCode> {\n    let health_service = HealthService::new();\n    let readiness = health_service.readiness_check(&state).await;\n    \n    if readiness.ready {\n        Ok(Json(readiness))\n    } else {\n        Err(StatusCode::SERVICE_UNAVAILABLE)\n    }\n}\n"}