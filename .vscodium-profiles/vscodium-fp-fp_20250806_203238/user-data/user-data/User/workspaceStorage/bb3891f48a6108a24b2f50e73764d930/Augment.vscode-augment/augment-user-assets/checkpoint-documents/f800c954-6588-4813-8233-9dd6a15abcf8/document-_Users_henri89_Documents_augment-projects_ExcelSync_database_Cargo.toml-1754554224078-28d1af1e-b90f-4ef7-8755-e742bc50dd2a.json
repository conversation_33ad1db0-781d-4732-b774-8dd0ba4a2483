{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/Cargo.toml"}, "originalCode": "[package]\nname = \"database\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Database - PostgreSQL schema and migrations\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\nsqlx = { workspace = true }\nsea-orm = { workspace = true }\nsea-orm-migration = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\ntracing = { workspace = true }\nlog = { workspace = true }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n", "modifiedCode": "[package]\nname = \"database\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Database - PostgreSQL schema and migrations\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\nsqlx = { workspace = true }\nsea-orm = { workspace = true }\nsea-orm-migration = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\ntracing = { workspace = true }\nlog = { workspace = true }\nsha2 = \"0.10\"\ntokio = { workspace = true }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n"}