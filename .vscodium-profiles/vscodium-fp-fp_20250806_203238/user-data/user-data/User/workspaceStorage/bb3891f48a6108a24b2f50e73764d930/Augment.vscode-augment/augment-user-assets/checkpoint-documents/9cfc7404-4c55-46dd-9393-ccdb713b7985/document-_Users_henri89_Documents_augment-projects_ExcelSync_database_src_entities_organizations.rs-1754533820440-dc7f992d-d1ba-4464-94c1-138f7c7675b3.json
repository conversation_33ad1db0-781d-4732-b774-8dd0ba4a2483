{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/organizations.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"organizations\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub address: Option<String>,\n    pub phone: Option<String>,\n    pub email: Option<String>,\n    pub tax_code: Option<String>,\n    pub is_active: bool,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, C<PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(has_many = \"super::users::Entity\")]\n    Users,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Users.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"organizations\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub address: Option<String>,\n    pub phone: Option<String>,\n    pub email: Option<String>,\n    pub tax_code: Option<String>,\n    pub is_active: bool,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, C<PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(has_many = \"super::users::Entity\")]\n    Users,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Users.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n\n}\n"}