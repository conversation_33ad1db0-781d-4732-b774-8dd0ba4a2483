{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/auth.rs"}, "originalCode": "use axum::{\n    extract::State,\n    response::Json,\n    routing::post,\n    Router,\n};\nuse serde_json::{json, Value};\nuse utoipa;\nuse validator::Validate;\n\nuse crate::{\n    dto::auth::*,\n    handlers::{json_response, ApiError},\n    services::UserService,\n    AppState\n};\n\n/// Authentication routes\npub fn auth_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/login\", post(login))\n        .route(\"/logout\", post(logout))\n        .route(\"/refresh\", post(refresh_token))\n}\n\n/// Login endpoint\npub async fn login(\n    State(state): State<AppState>,\n    Json(request): Json<LoginRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // Validate request\n    request.validate().map_err(|_| ApiError::BadRequest(\"Invalid login request\".to_string()))?;\n\n    // Get user service\n    let user_service = UserService::new(state.db.clone(), state.auth_service.clone());\n\n    // Find user by email\n    let user = user_service.get_user_by_email(&request.email).await?;\n\n    // Verify password\n    let is_valid = state\n        .auth_service\n        .verify_password(&request.password, &user.password_hash)\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to verify password: {}\", e)))?;\n\n    if !is_valid {\n        return Err(ApiError::BadRequest(\"Invalid email or password\".to_string()));\n    }\n\n    // Check if user is active\n    if !user.is_active {\n        return Err(ApiError::BadRequest(\"Account is disabled\".to_string()));\n    }\n\n    // Generate JWT token\n    let token = state\n        .auth_service\n        .generate_token(\n            user.id,\n            user.email.clone(),\n            user.role.to_string(),\n            user.organization_id,\n        )\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to generate token: {}\", e)))?;\n\n    // Update last login\n    if let Err(e) = user_service.update_last_login(user.id).await {\n        // Log error but don't fail the login\n        tracing::warn!(\"Failed to update last login for user {}: {}\", user.id, e);\n    }\n\n    // Create response\n    let response = LoginResponse {\n        token,\n        expires_at: (chrono::Utc::now() + chrono::Duration::hours(8)).to_rfc3339(),\n        user: UserInfo {\n            id: user.id.to_string(),\n            email: user.email,\n            full_name: user.full_name,\n            role: user.role.to_string(),\n            organization_id: user.organization_id.map(|id| id.to_string()),\n        },\n    };\n\n    Ok(json_response(\"Login successful\", response))\n}\n\n/// Logout endpoint\npub async fn logout(\n    State(_state): State<AppState>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement session invalidation\n    Ok(Json(json!({\n        \"success\": true,\n        \"message\": \"Logout successful\"\n    })))\n}\n\n/// Refresh token endpoint\n#[utoipa::path(\n    post,\n    path = \"/api/v1/auth/refresh\",\n    tag = \"Authentication\",\n    summary = \"Refresh access token\",\n    description = \"Refresh an expired access token using a refresh token\",\n    request_body = RefreshTokenRequest,\n    responses(\n        (status = 200, description = \"Token refreshed successfully\", body = TokenResponse),\n        (status = 400, description = \"Bad request\", body = ErrorResponse),\n        (status = 401, description = \"Invalid refresh token\", body = ErrorResponse),\n        (status = 500, description = \"Internal server error\", body = ErrorResponse)\n    )\n)]\npub async fn refresh_token(\n    State(_state): State<AppState>,\n    Json(request): Json<RefreshTokenRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement token refresh logic\n    let response = RefreshTokenResponse {\n        token: format!(\"refreshed-{}\", request.token),\n        expires_at: chrono::Utc::now().to_rfc3339(),\n    };\n\n    Ok(Json(json!({\n        \"success\": true,\n        \"data\": response,\n        \"message\": \"Token refreshed successfully\"\n    })))\n}\n\n/// Register endpoint\n#[utoipa::path(\n    post,\n    path = \"/api/v1/auth/register\",\n    tag = \"Authentication\",\n    summary = \"User registration\",\n    description = \"Register a new user account\",\n    request_body = RegisterRequest,\n    responses(\n        (status = 201, description = \"Registration successful\", body = UserResponse),\n        (status = 400, description = \"Bad request\", body = ErrorResponse),\n        (status = 409, description = \"User already exists\", body = ErrorResponse),\n        (status = 500, description = \"Internal server error\", body = ErrorResponse)\n    )\n)]\npub async fn register(\n    State(_state): State<AppState>,\n    Json(_register_request): Json<RegisterRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement user registration logic\n    Err(ApiError::InternalServerError(\"Registration not implemented yet\".to_string()))\n}\n\n\n", "modifiedCode": "use axum::{\n    extract::State,\n    response::Json,\n    routing::post,\n    Router,\n};\nuse serde_json::{json, Value};\nuse utoipa;\nuse validator::Validate;\n\nuse crate::{\n    dto::auth::*,\n    handlers::{json_response, ApiError},\n    services::UserService,\n    AppState\n};\n\n/// Authentication routes\npub fn auth_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/login\", post(login))\n        .route(\"/logout\", post(logout))\n        .route(\"/refresh\", post(refresh_token))\n}\n\n/// Login endpoint\npub async fn login(\n    State(state): State<AppState>,\n    Json(request): Json<LoginRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // Validate request\n    request.validate().map_err(|_| ApiError::BadRequest(\"Invalid login request\".to_string()))?;\n\n    // Get user service\n    let user_service = UserService::new(state.db.clone(), state.auth_service.clone());\n\n    // Find user by email\n    let user = user_service.get_user_by_email(&request.email).await?;\n\n    // Verify password\n    let is_valid = state\n        .auth_service\n        .verify_password(&request.password, &user.password_hash)\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to verify password: {}\", e)))?;\n\n    if !is_valid {\n        return Err(ApiError::BadRequest(\"Invalid email or password\".to_string()));\n    }\n\n    // Check if user is active\n    if !user.is_active {\n        return Err(ApiError::BadRequest(\"Account is disabled\".to_string()));\n    }\n\n    // Generate JWT token\n    let token = state\n        .auth_service\n        .generate_token(\n            user.id,\n            user.email.clone(),\n            user.role.to_string(),\n            user.organization_id,\n        )\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to generate token: {}\", e)))?;\n\n    // Update last login\n    if let Err(e) = user_service.update_last_login(user.id).await {\n        // Log error but don't fail the login\n        tracing::warn!(\"Failed to update last login for user {}: {}\", user.id, e);\n    }\n\n    // Create response\n    let response = LoginResponse {\n        token,\n        expires_at: (chrono::Utc::now() + chrono::Duration::hours(8)).to_rfc3339(),\n        user: UserInfo {\n            id: user.id.to_string(),\n            email: user.email,\n            full_name: user.full_name,\n            role: user.role.to_string(),\n            organization_id: user.organization_id.map(|id| id.to_string()),\n        },\n    };\n\n    Ok(json_response(\"Login successful\", response))\n}\n\n/// Logout endpoint\npub async fn logout(\n    State(_state): State<AppState>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement session invalidation\n    Ok(Json(json!({\n        \"success\": true,\n        \"message\": \"Logout successful\"\n    })))\n}\n\n/// Refresh token endpoint\npub async fn refresh_token(\n    State(_state): State<AppState>,\n    Json(request): Json<RefreshTokenRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement token refresh logic\n    let response = RefreshTokenResponse {\n        token: format!(\"refreshed-{}\", request.token),\n        expires_at: chrono::Utc::now().to_rfc3339(),\n    };\n\n    Ok(Json(json!({\n        \"success\": true,\n        \"data\": response,\n        \"message\": \"Token refreshed successfully\"\n    })))\n}\n\n/// Register endpoint\n#[utoipa::path(\n    post,\n    path = \"/api/v1/auth/register\",\n    tag = \"Authentication\",\n    summary = \"User registration\",\n    description = \"Register a new user account\",\n    request_body = RegisterRequest,\n    responses(\n        (status = 201, description = \"Registration successful\", body = UserResponse),\n        (status = 400, description = \"Bad request\", body = ErrorResponse),\n        (status = 409, description = \"User already exists\", body = ErrorResponse),\n        (status = 500, description = \"Internal server error\", body = ErrorResponse)\n    )\n)]\npub async fn register(\n    State(_state): State<AppState>,\n    Json(_register_request): Json<RegisterRequest>,\n) -> Result<Json<Value>, ApiError> {\n    // TODO: Implement user registration logic\n    Err(ApiError::InternalServerError(\"Registration not implemented yet\".to_string()))\n}\n\n\n"}