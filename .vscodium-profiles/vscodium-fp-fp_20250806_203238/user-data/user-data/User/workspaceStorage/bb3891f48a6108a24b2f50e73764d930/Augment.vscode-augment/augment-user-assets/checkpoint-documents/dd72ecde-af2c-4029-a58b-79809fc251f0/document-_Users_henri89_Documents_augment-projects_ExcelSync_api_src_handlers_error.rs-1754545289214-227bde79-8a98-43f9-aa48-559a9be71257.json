{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/error.rs"}, "originalCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    Json,\n};\nuse serde_json::json;\n\n/// API error types\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let (status, error_message) = match self {\n            ApiError::InternalServerError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Internal server error\"),\n            ApiError::BadRequest(_) => (StatusCode::BAD_REQUEST, \"Bad request\"),\n            ApiError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, \"Unauthorized\"),\n            ApiError::Forbidden(_) => (StatusCode::FORBIDDEN, \"Forbidden\"),\n            ApiError::NotFound(_) => (StatusCode::NOT_FOUND, \"Not found\"),\n            ApiError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Database error\"),\n            ApiError::AuthError(_) => (StatusCode::UNAUTHORIZED, \"Authentication error\"),\n        };\n\n        let body = Json(json!({\n            \"error\": error_message,\n            \"message\": self.to_string()\n        }));\n\n        (status, body).into_response()\n    }\n}\n", "modifiedCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    J<PERSON>,\n};\nuse serde::{Deserialize, Serialize};\nuse serde_json::json;\nuse std::collections::HashMap;\nuse uuid::Uuid;\n\n/// API error types with enhanced context and details\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n\n    #[error(\"Validation error: {0}\")]\n    ValidationError(ValidationErrorDetails),\n\n    #[error(\"Business rule violation: {0}\")]\n    BusinessRuleViolation(BusinessRuleError),\n\n    #[error(\"Rate limit exceeded: {0}\")]\n    RateLimitExceeded(RateLimitError),\n\n    #[error(\"File processing error: {0}\")]\n    FileProcessingError(FileError),\n\n    #[error(\"External service error: {0}\")]\n    ExternalServiceError(ExternalServiceError),\n\n    #[error(\"Conflict: {0}\")]\n    Conflict(ConflictError),\n\n    #[error(\"Timeout: {0}\")]\n    Timeout(TimeoutError),\n\n    #[error(\"Service unavailable: {0}\")]\n    ServiceUnavailable(String),\n\n    #[error(\"Payment required: {0}\")]\n    PaymentRequired(PaymentError),\n\n    #[error(\"Too many requests: {0}\")]\n    TooManyRequests(String),\n\n    #[error(\"Unprocessable entity: {0}\")]\n    UnprocessableEntity(UnprocessableEntityError),\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let (status, error_message) = match self {\n            ApiError::InternalServerError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Internal server error\"),\n            ApiError::BadRequest(_) => (StatusCode::BAD_REQUEST, \"Bad request\"),\n            ApiError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, \"Unauthorized\"),\n            ApiError::Forbidden(_) => (StatusCode::FORBIDDEN, \"Forbidden\"),\n            ApiError::NotFound(_) => (StatusCode::NOT_FOUND, \"Not found\"),\n            ApiError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Database error\"),\n            ApiError::AuthError(_) => (StatusCode::UNAUTHORIZED, \"Authentication error\"),\n        };\n\n        let body = Json(json!({\n            \"error\": error_message,\n            \"message\": self.to_string()\n        }));\n\n        (status, body).into_response()\n    }\n}\n"}