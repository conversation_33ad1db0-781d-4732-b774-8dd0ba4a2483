{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/config.rs"}, "modifiedCode": "use anyhow::{Context, Result};\nuse config::{Config as ConfigBuilder, Environment, File};\nuse serde::{Deserialize, Serialize};\nuse std::env;\nuse validator::Validate;\n\n/// Main application configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct Config {\n    pub server: ServerConfig,\n    pub database: DatabaseConfig,\n    pub redis: RedisConfig,\n    pub security: SecurityConfig,\n    pub logging: LoggingConfig,\n    pub performance: PerformanceConfig,\n}\n\n/// Server configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct ServerConfig {\n    #[validate(length(min = 1))]\n    pub host: String,\n    #[validate(range(min = 1, max = 65535))]\n    pub port: u16,\n    #[validate(range(min = 1, max = 32))]\n    pub workers: usize,\n}\n\n/// Database configuration\n#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]\npub struct DatabaseConfig {\n    #[validate(length(min = 1))]\n    pub url: String,\n    #[validate(range(min = 1, max = 100))]\n    pub max_connections: u32,\n    #[validate(range(min = 1, max = 50))]\n    pub min_connections: u32,\n    #[validate(range(min = 1, max = 300))]\n    pub connection_timeout: u64,\n    #[validate(range(min = 60, max = 3600))]\n    pub idle_timeout: u64,\n}\n\n/// Redis configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct RedisConfig {\n    #[validate(length(min = 1))]\n    pub url: String,\n    #[validate(range(min = 1, max = 100))]\n    pub max_connections: u32,\n    #[validate(range(min = 1, max = 300))]\n    pub connection_timeout: u64,\n}\n\n/// Security configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct SecurityConfig {\n    #[validate(length(min = 32))]\n    pub jwt_secret: String,\n    #[validate(length(min = 64))]\n    pub encryption_key: String,\n    #[validate(length(min = 16))]\n    pub password_salt: String,\n    #[validate(range(min = 3600, max = 86400))]\n    pub session_timeout: u64,\n    #[validate(range(min = 1, max = 10))]\n    pub max_sessions_per_user: u32,\n}\n\n/// Logging configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct LoggingConfig {\n    #[validate(length(min = 1))]\n    pub level: String,\n    #[validate(length(min = 1))]\n    pub format: String,\n    pub file: Option<String>,\n}\n\n/// Performance configuration\n#[derive(Debug, Clone, Serialize, Deserialize, Validate)]\npub struct PerformanceConfig {\n    #[validate(range(min = 10, max = 1000))]\n    pub rate_limit_requests: u32,\n    #[validate(range(min = 10, max = 3600))]\n    pub rate_limit_window: u64,\n}\n\nimpl Config {\n    /// Load configuration from environment and files\n    pub fn load() -> Result<Self> {\n        let mut builder = ConfigBuilder::builder();\n\n        // Load from config file if it exists\n        if let Ok(config_path) = env::var(\"CONFIG_PATH\") {\n            builder = builder.add_source(File::with_name(&config_path).required(false));\n        } else {\n            builder = builder.add_source(File::with_name(\"config/default\").required(false));\n        }\n\n        // Load from environment variables\n        builder = builder.add_source(\n            Environment::with_prefix(\"EXCELSYNC\")\n                .separator(\"_\")\n                .try_parsing(true),\n        );\n\n        let config = builder\n            .build()\n            .context(\"Failed to build configuration\")?\n            .try_deserialize::<Config>()\n            .context(\"Failed to deserialize configuration\")?;\n\n        Ok(config)\n    }\n\n    /// Validate configuration\n    pub fn validate(&self) -> Result<()> {\n        self.validate()\n            .map_err(|e| anyhow::anyhow!(\"Configuration validation failed: {}\", e))?;\n        Ok(())\n    }\n}\n\nimpl Default for Config {\n    fn default() -> Self {\n        Self {\n            server: ServerConfig {\n                host: \"0.0.0.0\".to_string(),\n                port: 8080,\n                workers: 4,\n            },\n            database: DatabaseConfig {\n                url: \"postgresql://localhost:5432/excelsync\".to_string(),\n                max_connections: 20,\n                min_connections: 5,\n                connection_timeout: 30,\n                idle_timeout: 600,\n            },\n            redis: RedisConfig {\n                url: \"redis://localhost:6379\".to_string(),\n                max_connections: 10,\n                connection_timeout: 30,\n            },\n            security: SecurityConfig {\n                jwt_secret: \"your-super-secret-jwt-key-minimum-32-characters-long\".to_string(),\n                encryption_key: \"0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef\".to_string(),\n                password_salt: \"your-password-salt-for-additional-security\".to_string(),\n                session_timeout: 28800, // 8 hours\n                max_sessions_per_user: 5,\n            },\n            logging: LoggingConfig {\n                level: \"info\".to_string(),\n                format: \"json\".to_string(),\n                file: None,\n            },\n            performance: PerformanceConfig {\n                rate_limit_requests: 100,\n                rate_limit_window: 60,\n            },\n        }\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_default_config() {\n        let config = Config::default();\n        assert_eq!(config.server.host, \"0.0.0.0\");\n        assert_eq!(config.server.port, 8080);\n        assert_eq!(config.database.max_connections, 20);\n    }\n\n    #[test]\n    fn test_config_validation() {\n        let config = Config::default();\n        assert!(config.validate().is_ok());\n    }\n}\n"}