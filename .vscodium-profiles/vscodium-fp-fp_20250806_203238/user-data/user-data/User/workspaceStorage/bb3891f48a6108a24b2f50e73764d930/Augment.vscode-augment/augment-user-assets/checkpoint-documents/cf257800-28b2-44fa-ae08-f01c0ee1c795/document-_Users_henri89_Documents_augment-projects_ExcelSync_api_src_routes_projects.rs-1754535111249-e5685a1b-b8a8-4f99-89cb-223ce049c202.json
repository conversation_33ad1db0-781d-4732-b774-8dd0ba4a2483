{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/projects.rs"}, "modifiedCode": "use axum::{routing::get, Router};\nuse crate::AppState;\n\n/// Project management routes\npub fn project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_projects))\n        .route(\"/:id\", get(get_project))\n}\n\n/// List projects endpoint (placeholder)\npub async fn list_projects() -> &'static str {\n    \"List projects endpoint - TODO: implement\"\n}\n\n/// Get project endpoint (placeholder)\npub async fn get_project() -> &'static str {\n    \"Get project endpoint - TODO: implement\"\n}\n"}