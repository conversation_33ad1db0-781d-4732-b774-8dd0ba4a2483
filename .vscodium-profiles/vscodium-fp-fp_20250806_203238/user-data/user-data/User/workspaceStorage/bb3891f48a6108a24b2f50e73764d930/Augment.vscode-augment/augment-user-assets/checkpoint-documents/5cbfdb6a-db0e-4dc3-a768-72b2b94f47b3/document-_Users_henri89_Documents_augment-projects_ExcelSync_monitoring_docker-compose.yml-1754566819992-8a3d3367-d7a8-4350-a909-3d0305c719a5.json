{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/docker-compose.yml"}, "modifiedCode": "version: '3.8'\n\nservices:\n  # Prometheus for metrics collection\n  prometheus:\n    image: prom/prometheus:latest\n    container_name: excelsync-prometheus\n    ports:\n      - \"9090:9090\"\n    volumes:\n      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml\n      - ./prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml\n      - prometheus_data:/prometheus\n    command:\n      - '--config.file=/etc/prometheus/prometheus.yml'\n      - '--storage.tsdb.path=/prometheus'\n      - '--web.console.libraries=/etc/prometheus/console_libraries'\n      - '--web.console.templates=/etc/prometheus/consoles'\n      - '--storage.tsdb.retention.time=200h'\n      - '--web.enable-lifecycle'\n      - '--web.enable-admin-api'\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # Grafana for visualization\n  grafana:\n    image: grafana/grafana:latest\n    container_name: excelsync-grafana\n    ports:\n      - \"3001:3000\"\n    volumes:\n      - grafana_data:/var/lib/grafana\n      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards\n      - ./grafana/datasources:/etc/grafana/provisioning/datasources\n    environment:\n      - GF_SECURITY_ADMIN_USER=admin\n      - GF_SECURITY_ADMIN_PASSWORD=admin123\n      - GF_USERS_ALLOW_SIGN_UP=false\n      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource\n    networks:\n      - monitoring\n    restart: unless-stopped\n    depends_on:\n      - prometheus\n\n  # Alertmanager for alert handling\n  alertmanager:\n    image: prom/alertmanager:latest\n    container_name: excelsync-alertmanager\n    ports:\n      - \"9093:9093\"\n    volumes:\n      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml\n      - alertmanager_data:/alertmanager\n    command:\n      - '--config.file=/etc/alertmanager/alertmanager.yml'\n      - '--storage.path=/alertmanager'\n      - '--web.external-url=http://localhost:9093'\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # Node Exporter for system metrics\n  node-exporter:\n    image: prom/node-exporter:latest\n    container_name: excelsync-node-exporter\n    ports:\n      - \"9100:9100\"\n    volumes:\n      - /proc:/host/proc:ro\n      - /sys:/host/sys:ro\n      - /:/rootfs:ro\n    command:\n      - '--path.procfs=/host/proc'\n      - '--path.rootfs=/rootfs'\n      - '--path.sysfs=/host/sys'\n      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # PostgreSQL Exporter for database metrics\n  postgres-exporter:\n    image: prometheuscommunity/postgres-exporter:latest\n    container_name: excelsync-postgres-exporter\n    ports:\n      - \"9187:9187\"\n    environment:\n      - DATA_SOURCE_NAME=postgresql://username:password@localhost:5432/excelsync?sslmode=disable\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # Redis Exporter for Redis metrics\n  redis-exporter:\n    image: oliver006/redis_exporter:latest\n    container_name: excelsync-redis-exporter\n    ports:\n      - \"9121:9121\"\n    environment:\n      - REDIS_ADDR=redis://localhost:6379\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # Loki for log aggregation (optional)\n  loki:\n    image: grafana/loki:latest\n    container_name: excelsync-loki\n    ports:\n      - \"3100:3100\"\n    volumes:\n      - ./loki/loki-config.yml:/etc/loki/local-config.yaml\n      - loki_data:/loki\n    command: -config.file=/etc/loki/local-config.yaml\n    networks:\n      - monitoring\n    restart: unless-stopped\n\n  # Promtail for log collection (optional)\n  promtail:\n    image: grafana/promtail:latest\n    container_name: excelsync-promtail\n    volumes:\n      - ./promtail/promtail-config.yml:/etc/promtail/config.yml\n      - /var/log:/var/log:ro\n      - /var/lib/docker/containers:/var/lib/docker/containers:ro\n    command: -config.file=/etc/promtail/config.yml\n    networks:\n      - monitoring\n    restart: unless-stopped\n    depends_on:\n      - loki\n\nvolumes:\n  prometheus_data:\n  grafana_data:\n  alertmanager_data:\n  loki_data:\n\nnetworks:\n  monitoring:\n    driver: bridge\n"}