{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/financial.rs"}, "modifiedCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    http::StatusCode,\n    response::Json,\n    routing::{get, post, delete},\n    Router,\n};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\n\nuse crate::{\n    dto::financial::{FinancialCalculationRequest, FinancialHistoryRequest},\n    handlers::{json_response, ApiError},\n    services::FinancialService,\n    AppState,\n};\n\n/// Query parameters for financial calculation history\n#[derive(Debug, Deserialize)]\npub struct FinancialHistoryQuery {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n    pub calculation_type: Option<String>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n}\n\n/// Financial calculation routes\npub fn financial_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/calculate\", post(calculate_financial_metrics))\n        .route(\"/history/:project_id\", get(get_financial_history))\n        .route(\"/calculation/:calculation_id\", get(get_financial_calculation).delete(delete_financial_calculation))\n        .route(\"/statistics/:project_id\", get(get_calculation_statistics))\n}\n\n/// Calculate financial metrics endpoint\npub async fn calculate_financial_metrics(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let calculation_request: FinancialCalculationRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    // Validate the request\n    calculation_request.validate()\n        .map_err(|e| ApiError::BadRequest(format!(\"Validation failed: {}\", e)))?;\n\n    let financial_service = FinancialService::new(state.db.get_connection().clone());\n    let result = financial_service\n        .calculate_financial_metrics(calculation_request, claims.user_id)\n        .await?;\n\n    Ok(json_response(\"Financial calculation completed successfully\", result))\n}\n\n/// Get financial calculation history endpoint\npub async fn get_financial_history(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Query(query): Query<FinancialHistoryQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let history_request = FinancialHistoryRequest {\n        project_id: project_id.to_string(),\n        calculation_type: query.calculation_type.and_then(|ct| {\n            serde_json::from_str(&format!(\"\\\"{}\\\"\", ct)).ok()\n        }),\n        start_date: query.start_date,\n        end_date: query.end_date,\n    };\n\n    let financial_service = FinancialService::new(state.db.get_connection().clone());\n    let result = financial_service\n        .get_financial_history(\n            history_request,\n            claims.user_id,\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n        )\n        .await?;\n\n    Ok(json_response(\"Financial calculation history retrieved successfully\", result))\n}\n\n/// Get specific financial calculation endpoint\npub async fn get_financial_calculation(\n    State(state): State<AppState>,\n    Path(calculation_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let financial_service = FinancialService::new(state.db.get_connection().clone());\n    let result = financial_service\n        .get_financial_calculation(calculation_id, claims.user_id)\n        .await?;\n\n    Ok(json_response(\"Financial calculation retrieved successfully\", result))\n}\n\n/// Delete financial calculation endpoint\npub async fn delete_financial_calculation(\n    State(state): State<AppState>,\n    Path(calculation_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let financial_service = FinancialService::new(state.db.get_connection().clone());\n    financial_service\n        .delete_financial_calculation(calculation_id, claims.user_id)\n        .await?;\n\n    Ok(json_response(\"Financial calculation deleted successfully\", ()))\n}\n\n/// Get calculation statistics endpoint\npub async fn get_calculation_statistics(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let financial_service = FinancialService::new(state.db.get_connection().clone());\n    let result = financial_service\n        .get_calculation_statistics(project_id, claims.user_id)\n        .await?;\n\n    Ok(json_response(\"Calculation statistics retrieved successfully\", result))\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use crate::dto::financial::{FinancialCalculationType, FinancialInputData};\n    use axum::body::Body;\n    use axum::http::{Method, Request};\n    use serde_json::json;\n\n    #[tokio::test]\n    async fn test_financial_calculation_request_validation() {\n        let valid_request = FinancialCalculationRequest {\n            project_id: \"123e4567-e89b-12d3-a456-426614174000\".to_string(),\n            calculation_type: FinancialCalculationType::TaxCalculation,\n            input_data: FinancialInputData {\n                total_investment: 1_000_000.0,\n                expected_revenue: 1_500_000.0,\n                land_area: Some(1000.0),\n                construction_area: Some(800.0),\n                project_duration_months: 24,\n                land_location: Some(\"urban_class_1\".to_string()),\n                land_use_type: Some(\"commercial\".to_string()),\n                additional_data: None,\n            },\n        };\n\n        // This should pass validation\n        assert!(valid_request.validate().is_ok());\n\n        let invalid_request = FinancialCalculationRequest {\n            project_id: \"invalid-uuid\".to_string(),\n            calculation_type: FinancialCalculationType::TaxCalculation,\n            input_data: FinancialInputData {\n                total_investment: -1000.0, // Invalid negative investment\n                expected_revenue: 1_500_000.0,\n                land_area: Some(-100.0), // Invalid negative area\n                construction_area: Some(800.0),\n                project_duration_months: 0, // Invalid duration\n                land_location: Some(\"urban_class_1\".to_string()),\n                land_use_type: Some(\"commercial\".to_string()),\n                additional_data: None,\n            },\n        };\n\n        // This should fail validation\n        assert!(invalid_request.validate().is_err());\n    }\n\n    #[test]\n    fn test_financial_history_query_parsing() {\n        let query = FinancialHistoryQuery {\n            page: Some(1),\n            per_page: Some(10),\n            calculation_type: Some(\"tax_calculation\".to_string()),\n            start_date: Some(\"2024-01-01T00:00:00Z\".to_string()),\n            end_date: Some(\"2024-12-31T23:59:59Z\".to_string()),\n        };\n\n        assert_eq!(query.page, Some(1));\n        assert_eq!(query.per_page, Some(10));\n        assert_eq!(query.calculation_type, Some(\"tax_calculation\".to_string()));\n    }\n\n    #[test]\n    fn test_route_paths() {\n        let router = financial_routes();\n        \n        // Test that the router is created successfully\n        // In a real test, you would test the actual route handling\n        assert!(!format!(\"{:?}\", router).is_empty());\n    }\n}\n"}