{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/metrics/mod.rs"}, "originalCode": "use axum::{\n    extract::MatchedPath,\n    http::{Request, StatusCode},\n    middleware::Next,\n    response::Response,\n    routing::get,\n    Router,\n};\nuse axum_prometheus::PrometheusMetricLayer;\nuse prometheus::{\n    register_counter_vec, register_gauge_vec, register_histogram_vec, CounterVec, Encoder, Gauge,\n    GaugeVec, HistogramVec, TextEncoder,\n};\nuse std::time::Instant;\nuse tracing::{error, info};\n\n/// Prometheus metrics for ExcelSync API\n#[derive(Clone)]\npub struct ApiMetrics {\n    /// HTTP request counter by method, path, and status\n    pub http_requests_total: CounterVec,\n    /// HTTP request duration histogram\n    pub http_request_duration_seconds: HistogramVec,\n    /// Active connections gauge\n    pub active_connections: GaugeVec,\n    /// Database connection pool metrics\n    pub db_connections_active: Gauge,\n    pub db_connections_idle: Gauge,\n    /// Redis connection metrics\n    pub redis_connections_active: Gauge,\n    /// Business metrics\n    pub projects_total: Gauge,\n    pub users_total: Gauge,\n    pub templates_total: Gauge,\n    /// Error counters\n    pub errors_total: CounterVec,\n    /// Authentication metrics\n    pub auth_attempts_total: CounterVec,\n    pub jwt_tokens_issued: CounterVec,\n}\n\nimpl ApiMetrics {\n    /// Create new metrics instance with all collectors registered\n    pub fn new() -> Result<Self, prometheus::Error> {\n        let http_requests_total = register_counter_vec!(\n            \"excelsync_http_requests_total\",\n            \"Total number of HTTP requests\",\n            &[\"method\", \"path\", \"status\"]\n        )?;\n\n        let http_request_duration_seconds = register_histogram_vec!(\n            \"excelsync_http_request_duration_seconds\",\n            \"HTTP request duration in seconds\",\n            &[\"method\", \"path\"],\n            vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]\n        )?;\n\n        let active_connections = register_gauge_vec!(\n            \"excelsync_active_connections\",\n            \"Number of active connections\",\n            &[\"type\"]\n        )?;\n\n        let db_connections_active = prometheus::register_gauge!(\n            \"excelsync_db_connections_active\",\n            \"Number of active database connections\"\n        )?;\n\n        let db_connections_idle = prometheus::register_gauge!(\n            \"excelsync_db_connections_idle\",\n            \"Number of idle database connections\"\n        )?;\n\n        let redis_connections_active = prometheus::register_gauge!(\n            \"excelsync_redis_connections_active\",\n            \"Number of active Redis connections\"\n        )?;\n\n        let projects_total = prometheus::register_gauge!(\n            \"excelsync_projects_total\",\n            \"Total number of projects in the system\"\n        )?;\n\n        let users_total = prometheus::register_gauge!(\n            \"excelsync_users_total\",\n            \"Total number of users in the system\"\n        )?;\n\n        let templates_total = prometheus::register_gauge!(\n            \"excelsync_templates_total\",\n            \"Total number of templates in the system\"\n        )?;\n\n        let errors_total = register_counter_vec!(\n            \"excelsync_errors_total\",\n            \"Total number of errors by type\",\n            &[\"error_type\", \"component\"]\n        )?;\n\n        let auth_attempts_total = register_counter_vec!(\n            \"excelsync_auth_attempts_total\",\n            \"Total authentication attempts\",\n            &[\"result\", \"method\"]\n        )?;\n\n        let jwt_tokens_issued = register_counter_vec!(\n            \"excelsync_jwt_tokens_issued_total\",\n            \"Total JWT tokens issued\",\n            &[\"token_type\"]\n        )?;\n\n        Ok(Self {\n            http_requests_total,\n            http_request_duration_seconds,\n            active_connections,\n            db_connections_active,\n            db_connections_idle,\n            redis_connections_active,\n            projects_total,\n            users_total,\n            templates_total,\n            errors_total,\n            auth_attempts_total,\n            jwt_tokens_issued,\n        })\n    }\n\n    /// Record HTTP request metrics\n    pub fn record_http_request(&self, method: &str, path: &str, status: StatusCode, duration: f64) {\n        self.http_requests_total\n            .with_label_values(&[method, path, &status.as_u16().to_string()])\n            .inc();\n\n        self.http_request_duration_seconds\n            .with_label_values(&[method, path])\n            .observe(duration);\n    }\n\n    /// Record authentication attempt\n    pub fn record_auth_attempt(&self, result: &str, method: &str) {\n        self.auth_attempts_total\n            .with_label_values(&[result, method])\n            .inc();\n    }\n\n    /// Record JWT token issuance\n    pub fn record_jwt_token_issued(&self, token_type: &str) {\n        self.jwt_tokens_issued\n            .with_label_values(&[token_type])\n            .inc();\n    }\n\n    /// Record error occurrence\n    pub fn record_error(&self, error_type: &str, component: &str) {\n        self.errors_total\n            .with_label_values(&[error_type, component])\n            .inc();\n    }\n\n    /// Update business metrics\n    pub fn update_business_metrics(&self, projects: f64, users: f64, templates: f64) {\n        self.projects_total.set(projects);\n        self.users_total.set(users);\n        self.templates_total.set(templates);\n    }\n\n    /// Update database connection metrics\n    pub fn update_db_metrics(&self, active: f64, idle: f64) {\n        self.db_connections_active.set(active);\n        self.db_connections_idle.set(idle);\n    }\n\n    /// Update Redis connection metrics\n    pub fn update_redis_metrics(&self, active: f64) {\n        self.redis_connections_active.set(active);\n    }\n}\n\nimpl Default for ApiMetrics {\n    fn default() -> Self {\n        Self::new().expect(\"Failed to create metrics\")\n    }\n}\n\n/// Middleware to track HTTP request metrics\npub async fn track_metrics<B>(\n    req: Request<B>,\n    next: Next<B>,\n) -> Result<Response, StatusCode> {\n    let start = Instant::now();\n    let method = req.method().to_string();\n    let path = req\n        .extensions()\n        .get::<MatchedPath>()\n        .map(|matched_path| matched_path.as_str())\n        .unwrap_or(\"unknown\")\n        .to_string();\n\n    let response = next.run(req).await;\n    let duration = start.elapsed().as_secs_f64();\n    let status = response.status();\n\n    // Record metrics (this would need access to the metrics instance)\n    info!(\n        method = %method,\n        path = %path,\n        status = %status.as_u16(),\n        duration = %duration,\n        \"HTTP request completed\"\n    );\n\n    Ok(response)\n}\n\n/// Handler for Prometheus metrics endpoint\npub async fn metrics_handler() -> Result<String, StatusCode> {\n    let encoder = TextEncoder::new();\n    let metric_families = prometheus::gather();\n    \n    match encoder.encode_to_string(&metric_families) {\n        Ok(output) => Ok(output),\n        Err(e) => {\n            error!(\"Failed to encode metrics: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// Create metrics router with health check and metrics endpoints\npub fn create_metrics_router<S>() -> Router<S>\nwhere\n    S: Clone + Send + Sync + 'static,\n{\n    Router::new()\n        .route(\"/metrics\", get(metrics_handler))\n}\n\n/// Health check endpoint\npub async fn health_check() -> &'static str {\n    \"OK\"\n}\n\n/// Readiness check endpoint\npub async fn readiness_check() -> Result<&'static str, StatusCode> {\n    // TODO: Add actual readiness checks (database, Redis, etc.)\n    Ok(\"READY\")\n}\n\n/// Create Prometheus metric layer for automatic HTTP metrics\npub fn create_prometheus_layer() -> PrometheusMetricLayer<'static> {\n    PrometheusMetricLayer::new()\n}\n", "modifiedCode": "use axum::{\n    extract::MatchedPath,\n    http::{Request, StatusCode},\n    middleware::Next,\n    response::Response,\n    routing::get,\n    Router,\n};\nuse axum_prometheus::PrometheusMetricLayer;\nuse prometheus::{\n    register_counter_vec, register_gauge_vec, register_histogram_vec, CounterVec, Encoder, Gauge,\n    GaugeVec, HistogramVec, TextEncoder,\n};\nuse std::time::Instant;\nuse tracing::{error, info};\n\n/// Prometheus metrics for ExcelSync API\n#[derive(Clone)]\npub struct ApiMetrics {\n    /// HTTP request counter by method, path, and status\n    pub http_requests_total: CounterVec,\n    /// HTTP request duration histogram\n    pub http_request_duration_seconds: HistogramVec,\n    /// Active connections gauge\n    pub active_connections: GaugeVec,\n    /// Database connection pool metrics\n    pub db_connections_active: Gauge,\n    pub db_connections_idle: Gauge,\n    /// Redis connection metrics\n    pub redis_connections_active: Gauge,\n    /// Business metrics\n    pub projects_total: Gauge,\n    pub users_total: Gauge,\n    pub templates_total: Gauge,\n    /// Error counters\n    pub errors_total: CounterVec,\n    /// Authentication metrics\n    pub auth_attempts_total: CounterVec,\n    pub jwt_tokens_issued: CounterVec,\n}\n\nimpl ApiMetrics {\n    /// Create new metrics instance with all collectors registered\n    pub fn new() -> Result<Self, prometheus::Error> {\n        let http_requests_total = register_counter_vec!(\n            \"excelsync_http_requests_total\",\n            \"Total number of HTTP requests\",\n            &[\"method\", \"path\", \"status\"]\n        )?;\n\n        let http_request_duration_seconds = register_histogram_vec!(\n            \"excelsync_http_request_duration_seconds\",\n            \"HTTP request duration in seconds\",\n            &[\"method\", \"path\"],\n            vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]\n        )?;\n\n        let active_connections = register_gauge_vec!(\n            \"excelsync_active_connections\",\n            \"Number of active connections\",\n            &[\"type\"]\n        )?;\n\n        let db_connections_active = prometheus::register_gauge!(\n            \"excelsync_db_connections_active\",\n            \"Number of active database connections\"\n        )?;\n\n        let db_connections_idle = prometheus::register_gauge!(\n            \"excelsync_db_connections_idle\",\n            \"Number of idle database connections\"\n        )?;\n\n        let redis_connections_active = prometheus::register_gauge!(\n            \"excelsync_redis_connections_active\",\n            \"Number of active Redis connections\"\n        )?;\n\n        let projects_total = prometheus::register_gauge!(\n            \"excelsync_projects_total\",\n            \"Total number of projects in the system\"\n        )?;\n\n        let users_total = prometheus::register_gauge!(\n            \"excelsync_users_total\",\n            \"Total number of users in the system\"\n        )?;\n\n        let templates_total = prometheus::register_gauge!(\n            \"excelsync_templates_total\",\n            \"Total number of templates in the system\"\n        )?;\n\n        let errors_total = register_counter_vec!(\n            \"excelsync_errors_total\",\n            \"Total number of errors by type\",\n            &[\"error_type\", \"component\"]\n        )?;\n\n        let auth_attempts_total = register_counter_vec!(\n            \"excelsync_auth_attempts_total\",\n            \"Total authentication attempts\",\n            &[\"result\", \"method\"]\n        )?;\n\n        let jwt_tokens_issued = register_counter_vec!(\n            \"excelsync_jwt_tokens_issued_total\",\n            \"Total JWT tokens issued\",\n            &[\"token_type\"]\n        )?;\n\n        Ok(Self {\n            http_requests_total,\n            http_request_duration_seconds,\n            active_connections,\n            db_connections_active,\n            db_connections_idle,\n            redis_connections_active,\n            projects_total,\n            users_total,\n            templates_total,\n            errors_total,\n            auth_attempts_total,\n            jwt_tokens_issued,\n        })\n    }\n\n    /// Record HTTP request metrics\n    pub fn record_http_request(&self, method: &str, path: &str, status: StatusCode, duration: f64) {\n        self.http_requests_total\n            .with_label_values(&[method, path, &status.as_u16().to_string()])\n            .inc();\n\n        self.http_request_duration_seconds\n            .with_label_values(&[method, path])\n            .observe(duration);\n    }\n\n    /// Record authentication attempt\n    pub fn record_auth_attempt(&self, result: &str, method: &str) {\n        self.auth_attempts_total\n            .with_label_values(&[result, method])\n            .inc();\n    }\n\n    /// Record JWT token issuance\n    pub fn record_jwt_token_issued(&self, token_type: &str) {\n        self.jwt_tokens_issued\n            .with_label_values(&[token_type])\n            .inc();\n    }\n\n    /// Record error occurrence\n    pub fn record_error(&self, error_type: &str, component: &str) {\n        self.errors_total\n            .with_label_values(&[error_type, component])\n            .inc();\n    }\n\n    /// Update business metrics\n    pub fn update_business_metrics(&self, projects: f64, users: f64, templates: f64) {\n        self.projects_total.set(projects);\n        self.users_total.set(users);\n        self.templates_total.set(templates);\n    }\n\n    /// Update database connection metrics\n    pub fn update_db_metrics(&self, active: f64, idle: f64) {\n        self.db_connections_active.set(active);\n        self.db_connections_idle.set(idle);\n    }\n\n    /// Update Redis connection metrics\n    pub fn update_redis_metrics(&self, active: f64) {\n        self.redis_connections_active.set(active);\n    }\n}\n\nimpl Default for ApiMetrics {\n    fn default() -> Self {\n        Self::new().expect(\"Failed to create metrics\")\n    }\n}\n\n/// Middleware to track HTTP request metrics\npub async fn track_metrics(\n    req: Request<axum::body::Body>,\n    next: Next<axum::body::Body>,\n) -> Result<Response, StatusCode> {\n    let start = Instant::now();\n    let method = req.method().to_string();\n    let path = req\n        .extensions()\n        .get::<MatchedPath>()\n        .map(|matched_path| matched_path.as_str())\n        .unwrap_or(\"unknown\")\n        .to_string();\n\n    let response = next.run(req).await;\n    let duration = start.elapsed().as_secs_f64();\n    let status = response.status();\n\n    // Record metrics (this would need access to the metrics instance)\n    info!(\n        method = %method,\n        path = %path,\n        status = %status.as_u16(),\n        duration = %duration,\n        \"HTTP request completed\"\n    );\n\n    Ok(response)\n}\n\n/// Handler for Prometheus metrics endpoint\npub async fn metrics_handler() -> Result<String, StatusCode> {\n    let encoder = TextEncoder::new();\n    let metric_families = prometheus::gather();\n    \n    match encoder.encode_to_string(&metric_families) {\n        Ok(output) => Ok(output),\n        Err(e) => {\n            error!(\"Failed to encode metrics: {}\", e);\n            Err(StatusCode::INTERNAL_SERVER_ERROR)\n        }\n    }\n}\n\n/// Create metrics router with health check and metrics endpoints\npub fn create_metrics_router<S>() -> Router<S>\nwhere\n    S: Clone + Send + Sync + 'static,\n{\n    Router::new()\n        .route(\"/metrics\", get(metrics_handler))\n}\n\n/// Health check endpoint\npub async fn health_check() -> &'static str {\n    \"OK\"\n}\n\n/// Readiness check endpoint\npub async fn readiness_check() -> Result<&'static str, StatusCode> {\n    // TODO: Add actual readiness checks (database, Redis, etc.)\n    Ok(\"READY\")\n}\n\n/// Create Prometheus metric layer for automatic HTTP metrics\npub fn create_prometheus_layer() -> PrometheusMetricLayer<'static> {\n    PrometheusMetricLayer::new()\n}\n"}