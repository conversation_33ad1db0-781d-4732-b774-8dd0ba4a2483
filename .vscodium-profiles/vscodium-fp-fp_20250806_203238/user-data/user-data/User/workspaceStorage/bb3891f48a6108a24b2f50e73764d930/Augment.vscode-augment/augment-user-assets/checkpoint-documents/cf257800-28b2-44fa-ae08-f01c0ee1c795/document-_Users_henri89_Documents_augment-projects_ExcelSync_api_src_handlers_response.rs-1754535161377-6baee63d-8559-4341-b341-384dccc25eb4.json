{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/response.rs"}, "modifiedCode": "use axum::{http::StatusCode, response::J<PERSON>};\nuse serde::{Deserialize, Serialize};\nuse serde_json::Value;\n\n/// Standard API response wrapper\n#[derive(Debug, Serialize, Deserialize)]\npub struct ApiResponse<T> {\n    pub success: bool,\n    pub data: Option<T>,\n    pub message: Option<String>,\n    pub timestamp: String,\n}\n\nimpl<T> ApiResponse<T>\nwhere\n    T: Serialize,\n{\n    /// Create successful response\n    pub fn success(data: T) -> Self {\n        Self {\n            success: true,\n            data: Some(data),\n            message: None,\n            timestamp: chrono::Utc::now().to_rfc3339(),\n        }\n    }\n\n    /// Create successful response with message\n    pub fn success_with_message(data: T, message: String) -> Self {\n        Self {\n            success: true,\n            data: Some(data),\n            message: Some(message),\n            timestamp: chrono::Utc::now().to_rfc3339(),\n        }\n    }\n}\n\nimpl ApiResponse<Value> {\n    /// Create error response\n    pub fn error(message: String) -> Self {\n        Self {\n            success: false,\n            data: None,\n            message: Some(message),\n            timestamp: chrono::Utc::now().to_rfc3339(),\n        }\n    }\n}\n\n/// Helper function to create JSON response\npub fn json_response<T>(data: T, status: StatusCode) -> (StatusCode, Json<ApiResponse<T>>)\nwhere\n    T: Serialize,\n{\n    (status, Json(ApiResponse::success(data)))\n}\n"}