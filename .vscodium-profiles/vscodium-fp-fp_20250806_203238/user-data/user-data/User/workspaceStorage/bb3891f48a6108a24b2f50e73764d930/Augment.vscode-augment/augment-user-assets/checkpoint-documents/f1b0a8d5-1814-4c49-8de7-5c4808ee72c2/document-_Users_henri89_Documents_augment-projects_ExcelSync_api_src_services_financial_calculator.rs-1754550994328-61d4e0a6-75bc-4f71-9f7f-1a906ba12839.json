{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/financial_calculator.rs"}, "originalCode": "use rust_decimal::{Decimal, MathematicalOps};\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse std::str::FromStr;\nuse anyhow::Result;\nuse thiserror::Error;\nuse chrono::{DateTime, Utc, Datelike};\nuse crate::dto::financial::{*, TaxBreakdownItem, RiskAssessment, RiskLevel};\nuse super::vietnamese_tax::{VietnameseTaxCalculator, TaxCalculationInput, LandLocationCategory, LandUseType};\n\n/// Financial calculation errors\n#[derive(Error, Debug)]\npub enum FinancialCalculationError {\n    #[error(\"Invalid input data: {0}\")]\n    InvalidInput(String),\n    #[error(\"Calculation failed: {0}\")]\n    CalculationFailed(String),\n    #[error(\"Tax calculation error: {0}\")]\n    TaxCalculationError(String),\n    #[error(\"Insufficient data for calculation: {0}\")]\n    InsufficientData(String),\n}\n\n/// Financial calculator service for Vietnamese real estate projects\npub struct FinancialCalculator {\n    tax_calculator: VietnameseTaxCalculator,\n}\n\nimpl FinancialCalculator {\n    pub fn new() -> Self {\n        Self {\n            tax_calculator: VietnameseTaxCalculator::new(),\n        }\n    }\n\n    /// Main entry point for financial calculations\n    pub async fn calculate_financial_metrics(\n        &self,\n        request: &FinancialCalculationRequest,\n    ) -> Result<FinancialCalculationResponse, FinancialCalculationError> {\n        let calculation_id = uuid::Uuid::new_v4().to_string();\n        let calculated_at = Utc::now().to_rfc3339();\n\n        let results = match request.calculation_type {\n            FinancialCalculationType::TaxCalculation => {\n                let tax_results = self.calculate_taxes(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: Some(tax_results),\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::RoiAnalysis => {\n                let roi_results = self.calculate_roi_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: Some(roi_results),\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::InvestmentAnalysis => {\n                let investment_results = self.calculate_investment_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: Some(investment_results),\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::CashFlowProjection => {\n                let cash_flow_results = self.calculate_cash_flow_projection(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: Some(cash_flow_results),\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::BreakEvenAnalysis => {\n                let break_even_results = self.calculate_break_even_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: Some(break_even_results),\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::ProfitabilityAnalysis => {\n                let profitability_results = self.calculate_profitability_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: Some(profitability_results),\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n        };\n\n        Ok(FinancialCalculationResponse {\n            calculation_id,\n            project_id: request.project_id.clone(),\n            calculation_type: request.calculation_type.clone(),\n            results,\n            calculated_at,\n            valid_until: Some(self.calculate_validity_date()),\n        })\n    }\n\n    /// Calculate Vietnamese taxes for real estate project\n    async fn calculate_taxes(&self, input: &FinancialInputData) -> Result<TaxCalculationResults, FinancialCalculationError> {\n        let land_location = self.parse_land_location(&input.land_location)?;\n        let land_use_type = self.parse_land_use_type(&input.land_use_type)?;\n\n        let tax_input = TaxCalculationInput {\n            land_area: Decimal::from_f64_retain(input.land_area.unwrap_or(1000.0))\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid land area\".to_string()))?,\n            land_value_per_sqm: Decimal::from_f64_retain(50_000_000.0) // Default 50M VND per sqm\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid land value\".to_string()))?,\n            land_location,\n            land_use_type,\n            total_investment: Decimal::from_f64_retain(input.total_investment)\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?,\n            construction_area: input.construction_area.and_then(|area| Decimal::from_f64_retain(area)),\n            project_duration_months: input.project_duration_months,\n            is_foreign_investor: false, // Default to domestic investor\n        };\n\n        let tax_result = self.tax_calculator.calculate_taxes(&tax_input)\n            .map_err(|e| FinancialCalculationError::TaxCalculationError(e.to_string()))?;\n\n        Ok(TaxCalculationResults {\n            vat_amount: self.decimal_to_string(tax_result.vat),\n            vat_rate: \"5%\".to_string(),\n            corporate_tax: self.decimal_to_string(tax_result.property_transfer_tax),\n            corporate_tax_rate: \"2%\".to_string(),\n            land_use_tax: Some(self.decimal_to_string(tax_result.land_use_fee)),\n            construction_permit_fees: Some(self.decimal_to_string(tax_result.registration_fee + tax_result.notarization_fee)),\n            total_tax_obligations: self.decimal_to_string(tax_result.total_taxes_and_fees),\n            tax_breakdown: vec![\n                TaxBreakdownItem {\n                    tax_type: \"Land Use Tax\".to_string(),\n                    amount: self.decimal_to_string(tax_result.land_use_fee),\n                    rate: \"Variable\".to_string(),\n                    description: \"Annual land use fee based on location and use type\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Property Transfer Tax\".to_string(),\n                    amount: self.decimal_to_string(tax_result.property_transfer_tax),\n                    rate: \"2%\".to_string(),\n                    description: \"Tax on property transfer value\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"VAT\".to_string(),\n                    amount: self.decimal_to_string(tax_result.vat),\n                    rate: \"5%\".to_string(),\n                    description: \"Value Added Tax on investment\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Registration Fees\".to_string(),\n                    amount: self.decimal_to_string(tax_result.registration_fee),\n                    rate: \"Fixed\".to_string(),\n                    description: \"Property registration and administrative fees\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Notarization Fees\".to_string(),\n                    amount: self.decimal_to_string(tax_result.notarization_fee),\n                    rate: \"Variable\".to_string(),\n                    description: \"Document notarization fees based on property value\".to_string(),\n                },\n            ],\n        })\n    }\n\n    /// Calculate ROI analysis\n    async fn calculate_roi_analysis(&self, input: &FinancialInputData) -> Result<RoiAnalysisResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let gross_profit = expected_revenue - total_investment;\n        let roi_percentage = if total_investment > Decimal::ZERO {\n            (gross_profit / total_investment) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        // Calculate payback period in months\n        let monthly_revenue = expected_revenue / Decimal::from(input.project_duration_months);\n        let payback_period_months = if monthly_revenue > Decimal::ZERO {\n            total_investment / monthly_revenue\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        // Calculate NPV with 10% discount rate\n        let discount_rate = \"0.10\".parse().unwrap();\n        let npv = self.calculate_npv(total_investment, expected_revenue, input.project_duration_months, discount_rate);\n\n        // Calculate IRR (simplified estimation)\n        let irr = self.estimate_irr(total_investment, expected_revenue, input.project_duration_months);\n\n        Ok(RoiAnalysisResults {\n            roi_percentage: self.decimal_to_string(roi_percentage),\n            payback_period_months: payback_period_months.to_u32().unwrap_or(0),\n            net_present_value: self.decimal_to_string(npv),\n            internal_rate_of_return: self.decimal_to_string(irr),\n            profitability_index: self.decimal_to_string(expected_revenue / total_investment),\n            risk_assessment: RiskAssessment {\n                risk_level: if roi_percentage >= Decimal::from(25) { RiskLevel::Low }\n                           else if roi_percentage >= Decimal::from(15) { RiskLevel::Medium }\n                           else { RiskLevel::High },\n                risk_factors: vec![\n                    \"Market volatility\".to_string(),\n                    \"Construction cost overruns\".to_string(),\n                    \"Regulatory changes\".to_string(),\n                ],\n                mitigation_suggestions: vec![\n                    \"Diversify investment portfolio\".to_string(),\n                    \"Secure fixed-price construction contracts\".to_string(),\n                    \"Monitor regulatory environment\".to_string(),\n                ],\n            },\n        })\n    }\n\n    /// Calculate comprehensive investment analysis\n    async fn calculate_investment_analysis(&self, input: &FinancialInputData) -> Result<InvestmentAnalysisResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n\n        // Cost breakdown estimation\n        let cost_breakdown = CostBreakdown {\n            land_acquisition: self.decimal_to_string(total_investment * \"0.40\".parse::<Decimal>().unwrap()),\n            construction_costs: self.decimal_to_string(total_investment * \"0.35\".parse::<Decimal>().unwrap()),\n            permits_and_fees: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n            financing_costs: self.decimal_to_string(total_investment * \"0.08\".parse::<Decimal>().unwrap()),\n            marketing_costs: self.decimal_to_string(total_investment * \"0.07\".parse::<Decimal>().unwrap()),\n            contingency: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n            total: self.decimal_to_string(total_investment),\n        };\n\n        // Revenue projections\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let revenue_projections = RevenueProjections {\n            sales_revenue: self.decimal_to_string(expected_revenue * \"0.80\".parse::<Decimal>().unwrap()),\n            rental_income: Some(self.decimal_to_string(expected_revenue * \"0.15\".parse::<Decimal>().unwrap())),\n            other_income: Some(self.decimal_to_string(expected_revenue * \"0.05\".parse::<Decimal>().unwrap())),\n            total_revenue: self.decimal_to_string(expected_revenue),\n            revenue_timeline: vec![\n                RevenueTimelineItem {\n                    period: \"Year 1\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.20\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue * \"0.20\".parse::<Decimal>().unwrap()),\n                },\n                RevenueTimelineItem {\n                    period: \"Year 2\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.40\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue * \"0.60\".parse::<Decimal>().unwrap()),\n                },\n                RevenueTimelineItem {\n                    period: \"Year 3\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.40\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue),\n                },\n            ],\n        };\n\n        // Sensitivity analysis\n        let sensitivity_analysis = SensitivityAnalysis {\n            variables: vec![\n                SensitivityResult {\n                    variable: \"Construction Cost\".to_string(),\n                    base_case: \"100%\".to_string(),\n                    optimistic: \"90%\".to_string(),\n                    pessimistic: \"120%\".to_string(),\n                    impact_on_roi: \"±15%\".to_string(),\n                },\n                SensitivityResult {\n                    variable: \"Sales Price\".to_string(),\n                    base_case: \"100%\".to_string(),\n                    optimistic: \"110%\".to_string(),\n                    pessimistic: \"85%\".to_string(),\n                    impact_on_roi: \"±25%\".to_string(),\n                },\n            ],\n        };\n\n        // Scenario analysis\n        let scenario_analysis = vec![\n            ScenarioResult {\n                scenario_name: \"Optimistic\".to_string(),\n                probability: \"25%\".to_string(),\n                roi: \"35%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.35\".parse::<Decimal>().unwrap()),\n                description: \"Best case scenario with favorable market conditions\".to_string(),\n            },\n            ScenarioResult {\n                scenario_name: \"Base Case\".to_string(),\n                probability: \"50%\".to_string(),\n                roi: \"20%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.20\".parse::<Decimal>().unwrap()),\n                description: \"Expected scenario based on current market conditions\".to_string(),\n            },\n            ScenarioResult {\n                scenario_name: \"Pessimistic\".to_string(),\n                probability: \"25%\".to_string(),\n                roi: \"5%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n                description: \"Worst case scenario with market downturn\".to_string(),\n            },\n        ];\n\n        Ok(InvestmentAnalysisResults {\n            total_cost_breakdown: cost_breakdown,\n            revenue_projections,\n            sensitivity_analysis,\n            scenario_analysis,\n        })\n    }\n\n    /// Calculate cash flow projection\n    async fn calculate_cash_flow_projection(&self, input: &FinancialInputData) -> Result<CashFlowResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let project_duration = input.project_duration_months;\n        let mut monthly_cash_flow = Vec::new();\n        let mut cumulative_cash_flow = Vec::new();\n        let mut cumulative_amount = Decimal::ZERO;\n\n        // Generate monthly cash flow\n        for month in 1..=project_duration {\n            let month_progress = Decimal::from(month) / Decimal::from(project_duration);\n            \n            // Investment outflow (front-loaded)\n            let outflow = if month <= project_duration / 2 {\n                total_investment / Decimal::from(project_duration / 2)\n            } else {\n                Decimal::ZERO\n            };\n\n            // Revenue inflow (back-loaded)\n            let inflow = if month > project_duration / 2 {\n                expected_revenue / Decimal::from(project_duration / 2)\n            } else {\n                Decimal::ZERO\n            };\n\n            let net_flow = inflow - outflow;\n            cumulative_amount += net_flow;\n\n            monthly_cash_flow.push(CashFlowItem {\n                period: format!(\"Month {}\", month),\n                inflow: self.decimal_to_string(inflow),\n                outflow: self.decimal_to_string(outflow),\n                net_flow: self.decimal_to_string(net_flow),\n            });\n\n            cumulative_cash_flow.push(CashFlowItem {\n                period: format!(\"Month {}\", month),\n                inflow: self.decimal_to_string(inflow),\n                outflow: self.decimal_to_string(outflow),\n                net_flow: self.decimal_to_string(cumulative_amount),\n            });\n        }\n\n        // Find break-even month\n        let break_even_month = cumulative_cash_flow.iter()\n            .position(|item| {\n                Decimal::from_str(&item.net_flow).unwrap_or(Decimal::ZERO) >= Decimal::ZERO\n            })\n            .map(|pos| pos as u32 + 1);\n\n        let peak_funding_requirement = cumulative_cash_flow.iter()\n            .map(|item| Decimal::from_str(&item.net_flow).unwrap_or(Decimal::ZERO))\n            .min()\n            .unwrap_or(Decimal::ZERO)\n            .abs();\n\n        let cash_flow_summary = CashFlowSummary {\n            total_inflow: self.decimal_to_string(expected_revenue),\n            total_outflow: self.decimal_to_string(total_investment),\n            net_cash_flow: self.decimal_to_string(expected_revenue - total_investment),\n            break_even_month,\n        };\n\n        Ok(CashFlowResults {\n            monthly_cash_flow,\n            cumulative_cash_flow,\n            peak_funding_requirement: self.decimal_to_string(peak_funding_requirement),\n            cash_flow_summary,\n        })\n    }\n\n    /// Calculate break-even analysis\n    async fn calculate_break_even_analysis(&self, input: &FinancialInputData) -> Result<BreakEvenResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        // Estimate variable and fixed costs\n        let variable_cost_ratio = \"0.60\".parse().unwrap(); // 60% of revenue\n        let fixed_costs = total_investment * \"0.20\".parse::<Decimal>().unwrap(); // 20% of investment\n\n        let contribution_margin_ratio = Decimal::ONE - variable_cost_ratio;\n        let break_even_revenue = fixed_costs / contribution_margin_ratio;\n        \n        let break_even_timeline = if expected_revenue > Decimal::ZERO {\n            (break_even_revenue / expected_revenue) * Decimal::from(input.project_duration_months)\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        let margin_of_safety = if expected_revenue > break_even_revenue {\n            ((expected_revenue - break_even_revenue) / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        Ok(BreakEvenResults {\n            break_even_point: self.decimal_to_string(break_even_revenue),\n            break_even_timeline: format!(\"{} months\", self.decimal_to_string(break_even_timeline)),\n            margin_of_safety: format!(\"{}%\", self.decimal_to_string(margin_of_safety)),\n            contribution_margin: format!(\"{}%\", self.decimal_to_string(contribution_margin_ratio * Decimal::from(100))),\n        })\n    }\n\n    /// Calculate profitability analysis\n    async fn calculate_profitability_analysis(&self, input: &FinancialInputData) -> Result<ProfitabilityResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        // Cost estimations\n        let cost_of_goods_sold = total_investment * \"0.70\".parse::<Decimal>().unwrap();\n        let operating_expenses = total_investment * \"0.15\".parse::<Decimal>().unwrap();\n        let depreciation = total_investment * \"0.05\".parse::<Decimal>().unwrap();\n        let interest_expense = total_investment * \"0.03\".parse::<Decimal>().unwrap();\n\n        let gross_profit = expected_revenue - cost_of_goods_sold;\n        let gross_profit_margin = if expected_revenue > Decimal::ZERO {\n            (gross_profit / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let ebitda = gross_profit - operating_expenses;\n        let ebitda_margin = if expected_revenue > Decimal::ZERO {\n            (ebitda / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let net_profit = ebitda - depreciation - interest_expense;\n        let net_profit_margin = if expected_revenue > Decimal::ZERO {\n            (net_profit / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        Ok(ProfitabilityResults {\n            gross_profit: self.decimal_to_string(gross_profit),\n            gross_profit_margin: format!(\"{}%\", self.decimal_to_string(gross_profit_margin)),\n            net_profit: self.decimal_to_string(net_profit),\n            net_profit_margin: format!(\"{}%\", self.decimal_to_string(net_profit_margin)),\n            ebitda: self.decimal_to_string(ebitda),\n            ebitda_margin: format!(\"{}%\", self.decimal_to_string(ebitda_margin)),\n        })\n    }\n\n    // Helper methods\n    fn parse_land_location(&self, location: &Option<String>) -> Result<LandLocationCategory, FinancialCalculationError> {\n        match location {\n            Some(loc) => LandLocationCategory::from_string(loc)\n                .map_err(|e| FinancialCalculationError::InvalidInput(e.to_string())),\n            None => Ok(LandLocationCategory::UrbanClass2), // Default\n        }\n    }\n\n    fn parse_land_use_type(&self, use_type: &Option<String>) -> Result<LandUseType, FinancialCalculationError> {\n        match use_type {\n            Some(ut) => LandUseType::from_string(ut)\n                .map_err(|e| FinancialCalculationError::InvalidInput(e.to_string())),\n            None => Ok(LandUseType::Mixed), // Default\n        }\n    }\n\n    fn decimal_to_string(&self, value: Decimal) -> String {\n        format!(\"{:.2}\", value)\n    }\n\n    fn calculate_npv(&self, investment: Decimal, revenue: Decimal, duration_months: u32, discount_rate: Decimal) -> Decimal {\n        let annual_discount_rate = discount_rate;\n        let monthly_discount_rate = annual_discount_rate / Decimal::from(12);\n        let monthly_cash_flow = revenue / Decimal::from(duration_months);\n        \n        let mut npv = -investment; // Initial investment as negative cash flow\n        \n        for month in 1..=duration_months {\n            let discount_factor = (Decimal::ONE + monthly_discount_rate).powi(-(month as i64));\n            npv += monthly_cash_flow * discount_factor;\n        }\n        \n        npv\n    }\n\n    fn estimate_irr(&self, investment: Decimal, revenue: Decimal, duration_months: u32) -> Decimal {\n        // Simplified IRR estimation using approximation\n        let total_return = revenue / investment;\n        let annual_periods = Decimal::from(12) / Decimal::from(duration_months);\n        \n        if total_return > Decimal::ONE {\n            (total_return - Decimal::ONE) * annual_periods * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        }\n    }\n\n    fn create_summary(&self, input: &FinancialInputData, calc_type: &FinancialCalculationType) -> Result<FinancialSummary, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let estimated_profit = expected_revenue - total_investment;\n        let roi_percentage = if total_investment > Decimal::ZERO {\n            (estimated_profit / total_investment) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let payback_period = if expected_revenue > Decimal::ZERO {\n            (total_investment / (expected_revenue / Decimal::from(input.project_duration_months)))\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        let recommendation = if roi_percentage >= Decimal::from(25) {\n            RecommendationType::StronglyRecommend\n        } else if roi_percentage >= Decimal::from(15) {\n            RecommendationType::Recommend\n        } else if roi_percentage >= Decimal::from(5) {\n            RecommendationType::Neutral\n        } else if roi_percentage >= Decimal::ZERO {\n            RecommendationType::Caution\n        } else {\n            RecommendationType::NotRecommended\n        };\n\n        Ok(FinancialSummary {\n            total_investment: self.decimal_to_string(total_investment),\n            expected_revenue: self.decimal_to_string(expected_revenue),\n            estimated_profit: self.decimal_to_string(estimated_profit),\n            roi_percentage: format!(\"{}%\", self.decimal_to_string(roi_percentage)),\n            payback_period: format!(\"{} months\", self.decimal_to_string(payback_period)),\n            recommendation: FinancialRecommendation {\n                recommendation,\n                confidence_level: \"Medium\".to_string(),\n                key_factors: vec![\n                    \"Market conditions\".to_string(),\n                    \"Construction costs\".to_string(),\n                    \"Regulatory environment\".to_string(),\n                ],\n                next_steps: vec![\n                    \"Conduct detailed market analysis\".to_string(),\n                    \"Obtain construction quotes\".to_string(),\n                    \"Review regulatory requirements\".to_string(),\n                ],\n            },\n        })\n    }\n\n    fn calculate_validity_date(&self) -> String {\n        let validity_duration = chrono::Duration::days(30); // Valid for 30 days\n        (Utc::now() + validity_duration).to_rfc3339()\n    }\n}\n\nimpl Default for FinancialCalculator {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n\n", "modifiedCode": "use rust_decimal::{Decimal, MathematicalOps};\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse std::str::FromStr;\nuse anyhow::Result;\nuse thiserror::Error;\nuse chrono::{DateTime, Utc, Datelike};\nuse crate::dto::financial::{*, TaxBreakdownItem, RiskAssessment, RiskLevel, RevenueTimelineItem};\nuse super::vietnamese_tax::{VietnameseTaxCalculator, TaxCalculationInput, LandLocationCategory, LandUseType};\n\n/// Financial calculation errors\n#[derive(Error, Debug)]\npub enum FinancialCalculationError {\n    #[error(\"Invalid input data: {0}\")]\n    InvalidInput(String),\n    #[error(\"Calculation failed: {0}\")]\n    CalculationFailed(String),\n    #[error(\"Tax calculation error: {0}\")]\n    TaxCalculationError(String),\n    #[error(\"Insufficient data for calculation: {0}\")]\n    InsufficientData(String),\n}\n\n/// Financial calculator service for Vietnamese real estate projects\npub struct FinancialCalculator {\n    tax_calculator: VietnameseTaxCalculator,\n}\n\nimpl FinancialCalculator {\n    pub fn new() -> Self {\n        Self {\n            tax_calculator: VietnameseTaxCalculator::new(),\n        }\n    }\n\n    /// Main entry point for financial calculations\n    pub async fn calculate_financial_metrics(\n        &self,\n        request: &FinancialCalculationRequest,\n    ) -> Result<FinancialCalculationResponse, FinancialCalculationError> {\n        let calculation_id = uuid::Uuid::new_v4().to_string();\n        let calculated_at = Utc::now().to_rfc3339();\n\n        let results = match request.calculation_type {\n            FinancialCalculationType::TaxCalculation => {\n                let tax_results = self.calculate_taxes(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: Some(tax_results),\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::RoiAnalysis => {\n                let roi_results = self.calculate_roi_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: Some(roi_results),\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::InvestmentAnalysis => {\n                let investment_results = self.calculate_investment_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: Some(investment_results),\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::CashFlowProjection => {\n                let cash_flow_results = self.calculate_cash_flow_projection(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: Some(cash_flow_results),\n                    break_even: None,\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::BreakEvenAnalysis => {\n                let break_even_results = self.calculate_break_even_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: Some(break_even_results),\n                    profitability: None,\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n            FinancialCalculationType::ProfitabilityAnalysis => {\n                let profitability_results = self.calculate_profitability_analysis(&request.input_data).await?;\n                FinancialResults {\n                    tax_calculations: None,\n                    roi_analysis: None,\n                    investment_analysis: None,\n                    cash_flow: None,\n                    break_even: None,\n                    profitability: Some(profitability_results),\n                    summary: self.create_summary(&request.input_data, &request.calculation_type)?,\n                }\n            }\n        };\n\n        Ok(FinancialCalculationResponse {\n            calculation_id,\n            project_id: request.project_id.clone(),\n            calculation_type: request.calculation_type.clone(),\n            results,\n            calculated_at,\n            valid_until: Some(self.calculate_validity_date()),\n        })\n    }\n\n    /// Calculate Vietnamese taxes for real estate project\n    async fn calculate_taxes(&self, input: &FinancialInputData) -> Result<TaxCalculationResults, FinancialCalculationError> {\n        let land_location = self.parse_land_location(&input.land_location)?;\n        let land_use_type = self.parse_land_use_type(&input.land_use_type)?;\n\n        let tax_input = TaxCalculationInput {\n            land_area: Decimal::from_f64_retain(input.land_area.unwrap_or(1000.0))\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid land area\".to_string()))?,\n            land_value_per_sqm: Decimal::from_f64_retain(50_000_000.0) // Default 50M VND per sqm\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid land value\".to_string()))?,\n            land_location,\n            land_use_type,\n            total_investment: Decimal::from_f64_retain(input.total_investment)\n                .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?,\n            construction_area: input.construction_area.and_then(|area| Decimal::from_f64_retain(area)),\n            project_duration_months: input.project_duration_months,\n            is_foreign_investor: false, // Default to domestic investor\n        };\n\n        let tax_result = self.tax_calculator.calculate_taxes(&tax_input)\n            .map_err(|e| FinancialCalculationError::TaxCalculationError(e.to_string()))?;\n\n        Ok(TaxCalculationResults {\n            vat_amount: self.decimal_to_string(tax_result.vat),\n            vat_rate: \"5%\".to_string(),\n            corporate_tax: self.decimal_to_string(tax_result.property_transfer_tax),\n            corporate_tax_rate: \"2%\".to_string(),\n            land_use_tax: Some(self.decimal_to_string(tax_result.land_use_fee)),\n            construction_permit_fees: Some(self.decimal_to_string(tax_result.registration_fee + tax_result.notarization_fee)),\n            total_tax_obligations: self.decimal_to_string(tax_result.total_taxes_and_fees),\n            tax_breakdown: vec![\n                TaxBreakdownItem {\n                    tax_type: \"Land Use Tax\".to_string(),\n                    amount: self.decimal_to_string(tax_result.land_use_fee),\n                    rate: \"Variable\".to_string(),\n                    description: \"Annual land use fee based on location and use type\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Property Transfer Tax\".to_string(),\n                    amount: self.decimal_to_string(tax_result.property_transfer_tax),\n                    rate: \"2%\".to_string(),\n                    description: \"Tax on property transfer value\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"VAT\".to_string(),\n                    amount: self.decimal_to_string(tax_result.vat),\n                    rate: \"5%\".to_string(),\n                    description: \"Value Added Tax on investment\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Registration Fees\".to_string(),\n                    amount: self.decimal_to_string(tax_result.registration_fee),\n                    rate: \"Fixed\".to_string(),\n                    description: \"Property registration and administrative fees\".to_string(),\n                },\n                TaxBreakdownItem {\n                    tax_type: \"Notarization Fees\".to_string(),\n                    amount: self.decimal_to_string(tax_result.notarization_fee),\n                    rate: \"Variable\".to_string(),\n                    description: \"Document notarization fees based on property value\".to_string(),\n                },\n            ],\n        })\n    }\n\n    /// Calculate ROI analysis\n    async fn calculate_roi_analysis(&self, input: &FinancialInputData) -> Result<RoiAnalysisResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let gross_profit = expected_revenue - total_investment;\n        let roi_percentage = if total_investment > Decimal::ZERO {\n            (gross_profit / total_investment) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        // Calculate payback period in months\n        let monthly_revenue = expected_revenue / Decimal::from(input.project_duration_months);\n        let payback_period_months = if monthly_revenue > Decimal::ZERO {\n            total_investment / monthly_revenue\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        // Calculate NPV with 10% discount rate\n        let discount_rate = \"0.10\".parse().unwrap();\n        let npv = self.calculate_npv(total_investment, expected_revenue, input.project_duration_months, discount_rate);\n\n        // Calculate IRR (simplified estimation)\n        let irr = self.estimate_irr(total_investment, expected_revenue, input.project_duration_months);\n\n        Ok(RoiAnalysisResults {\n            roi_percentage: self.decimal_to_string(roi_percentage),\n            payback_period_months: payback_period_months.to_u32().unwrap_or(0),\n            net_present_value: self.decimal_to_string(npv),\n            internal_rate_of_return: self.decimal_to_string(irr),\n            profitability_index: self.decimal_to_string(expected_revenue / total_investment),\n            risk_assessment: RiskAssessment {\n                risk_level: if roi_percentage >= Decimal::from(25) { RiskLevel::Low }\n                           else if roi_percentage >= Decimal::from(15) { RiskLevel::Medium }\n                           else { RiskLevel::High },\n                risk_factors: vec![\n                    \"Market volatility\".to_string(),\n                    \"Construction cost overruns\".to_string(),\n                    \"Regulatory changes\".to_string(),\n                ],\n                mitigation_suggestions: vec![\n                    \"Diversify investment portfolio\".to_string(),\n                    \"Secure fixed-price construction contracts\".to_string(),\n                    \"Monitor regulatory environment\".to_string(),\n                ],\n            },\n        })\n    }\n\n    /// Calculate comprehensive investment analysis\n    async fn calculate_investment_analysis(&self, input: &FinancialInputData) -> Result<InvestmentAnalysisResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n\n        // Cost breakdown estimation\n        let cost_breakdown = CostBreakdown {\n            land_acquisition: self.decimal_to_string(total_investment * \"0.40\".parse::<Decimal>().unwrap()),\n            construction_costs: self.decimal_to_string(total_investment * \"0.35\".parse::<Decimal>().unwrap()),\n            permits_and_fees: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n            financing_costs: self.decimal_to_string(total_investment * \"0.08\".parse::<Decimal>().unwrap()),\n            marketing_costs: self.decimal_to_string(total_investment * \"0.07\".parse::<Decimal>().unwrap()),\n            contingency: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n            total: self.decimal_to_string(total_investment),\n        };\n\n        // Revenue projections\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let revenue_projections = RevenueProjections {\n            sales_revenue: self.decimal_to_string(expected_revenue * \"0.80\".parse::<Decimal>().unwrap()),\n            rental_income: Some(self.decimal_to_string(expected_revenue * \"0.15\".parse::<Decimal>().unwrap())),\n            other_income: Some(self.decimal_to_string(expected_revenue * \"0.05\".parse::<Decimal>().unwrap())),\n            total_revenue: self.decimal_to_string(expected_revenue),\n            revenue_timeline: vec![\n                RevenueTimelineItem {\n                    period: \"Year 1\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.20\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue * \"0.20\".parse::<Decimal>().unwrap()),\n                },\n                RevenueTimelineItem {\n                    period: \"Year 2\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.40\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue * \"0.60\".parse::<Decimal>().unwrap()),\n                },\n                RevenueTimelineItem {\n                    period: \"Year 3\".to_string(),\n                    amount: self.decimal_to_string(expected_revenue * \"0.40\".parse::<Decimal>().unwrap()),\n                    cumulative: self.decimal_to_string(expected_revenue),\n                },\n            ],\n        };\n\n        // Sensitivity analysis\n        let sensitivity_analysis = SensitivityAnalysis {\n            variables: vec![\n                SensitivityResult {\n                    variable: \"Construction Cost\".to_string(),\n                    base_case: \"100%\".to_string(),\n                    optimistic: \"90%\".to_string(),\n                    pessimistic: \"120%\".to_string(),\n                    impact_on_roi: \"±15%\".to_string(),\n                },\n                SensitivityResult {\n                    variable: \"Sales Price\".to_string(),\n                    base_case: \"100%\".to_string(),\n                    optimistic: \"110%\".to_string(),\n                    pessimistic: \"85%\".to_string(),\n                    impact_on_roi: \"±25%\".to_string(),\n                },\n            ],\n        };\n\n        // Scenario analysis\n        let scenario_analysis = vec![\n            ScenarioResult {\n                scenario_name: \"Optimistic\".to_string(),\n                probability: \"25%\".to_string(),\n                roi: \"35%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.35\".parse::<Decimal>().unwrap()),\n                description: \"Best case scenario with favorable market conditions\".to_string(),\n            },\n            ScenarioResult {\n                scenario_name: \"Base Case\".to_string(),\n                probability: \"50%\".to_string(),\n                roi: \"20%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.20\".parse::<Decimal>().unwrap()),\n                description: \"Expected scenario based on current market conditions\".to_string(),\n            },\n            ScenarioResult {\n                scenario_name: \"Pessimistic\".to_string(),\n                probability: \"25%\".to_string(),\n                roi: \"5%\".to_string(),\n                npv: self.decimal_to_string(total_investment * \"0.05\".parse::<Decimal>().unwrap()),\n                description: \"Worst case scenario with market downturn\".to_string(),\n            },\n        ];\n\n        Ok(InvestmentAnalysisResults {\n            total_cost_breakdown: cost_breakdown,\n            revenue_projections,\n            sensitivity_analysis,\n            scenario_analysis,\n        })\n    }\n\n    /// Calculate cash flow projection\n    async fn calculate_cash_flow_projection(&self, input: &FinancialInputData) -> Result<CashFlowResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let project_duration = input.project_duration_months;\n        let mut monthly_cash_flow = Vec::new();\n        let mut cumulative_cash_flow = Vec::new();\n        let mut cumulative_amount = Decimal::ZERO;\n\n        // Generate monthly cash flow\n        for month in 1..=project_duration {\n            let month_progress = Decimal::from(month) / Decimal::from(project_duration);\n            \n            // Investment outflow (front-loaded)\n            let outflow = if month <= project_duration / 2 {\n                total_investment / Decimal::from(project_duration / 2)\n            } else {\n                Decimal::ZERO\n            };\n\n            // Revenue inflow (back-loaded)\n            let inflow = if month > project_duration / 2 {\n                expected_revenue / Decimal::from(project_duration / 2)\n            } else {\n                Decimal::ZERO\n            };\n\n            let net_flow = inflow - outflow;\n            cumulative_amount += net_flow;\n\n            monthly_cash_flow.push(CashFlowItem {\n                period: format!(\"Month {}\", month),\n                inflow: self.decimal_to_string(inflow),\n                outflow: self.decimal_to_string(outflow),\n                net_flow: self.decimal_to_string(net_flow),\n            });\n\n            cumulative_cash_flow.push(CashFlowItem {\n                period: format!(\"Month {}\", month),\n                inflow: self.decimal_to_string(inflow),\n                outflow: self.decimal_to_string(outflow),\n                net_flow: self.decimal_to_string(cumulative_amount),\n            });\n        }\n\n        // Find break-even month\n        let break_even_month = cumulative_cash_flow.iter()\n            .position(|item| {\n                Decimal::from_str(&item.net_flow).unwrap_or(Decimal::ZERO) >= Decimal::ZERO\n            })\n            .map(|pos| pos as u32 + 1);\n\n        let peak_funding_requirement = cumulative_cash_flow.iter()\n            .map(|item| Decimal::from_str(&item.net_flow).unwrap_or(Decimal::ZERO))\n            .min()\n            .unwrap_or(Decimal::ZERO)\n            .abs();\n\n        let cash_flow_summary = CashFlowSummary {\n            total_inflow: self.decimal_to_string(expected_revenue),\n            total_outflow: self.decimal_to_string(total_investment),\n            net_cash_flow: self.decimal_to_string(expected_revenue - total_investment),\n            break_even_month,\n        };\n\n        Ok(CashFlowResults {\n            monthly_cash_flow,\n            cumulative_cash_flow,\n            peak_funding_requirement: self.decimal_to_string(peak_funding_requirement),\n            cash_flow_summary,\n        })\n    }\n\n    /// Calculate break-even analysis\n    async fn calculate_break_even_analysis(&self, input: &FinancialInputData) -> Result<BreakEvenResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        // Estimate variable and fixed costs\n        let variable_cost_ratio = \"0.60\".parse().unwrap(); // 60% of revenue\n        let fixed_costs = total_investment * \"0.20\".parse::<Decimal>().unwrap(); // 20% of investment\n\n        let contribution_margin_ratio = Decimal::ONE - variable_cost_ratio;\n        let break_even_revenue = fixed_costs / contribution_margin_ratio;\n        \n        let break_even_timeline = if expected_revenue > Decimal::ZERO {\n            (break_even_revenue / expected_revenue) * Decimal::from(input.project_duration_months)\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        let margin_of_safety = if expected_revenue > break_even_revenue {\n            ((expected_revenue - break_even_revenue) / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        Ok(BreakEvenResults {\n            break_even_point: self.decimal_to_string(break_even_revenue),\n            break_even_timeline: format!(\"{} months\", self.decimal_to_string(break_even_timeline)),\n            margin_of_safety: format!(\"{}%\", self.decimal_to_string(margin_of_safety)),\n            contribution_margin: format!(\"{}%\", self.decimal_to_string(contribution_margin_ratio * Decimal::from(100))),\n        })\n    }\n\n    /// Calculate profitability analysis\n    async fn calculate_profitability_analysis(&self, input: &FinancialInputData) -> Result<ProfitabilityResults, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        // Cost estimations\n        let cost_of_goods_sold = total_investment * \"0.70\".parse::<Decimal>().unwrap();\n        let operating_expenses = total_investment * \"0.15\".parse::<Decimal>().unwrap();\n        let depreciation = total_investment * \"0.05\".parse::<Decimal>().unwrap();\n        let interest_expense = total_investment * \"0.03\".parse::<Decimal>().unwrap();\n\n        let gross_profit = expected_revenue - cost_of_goods_sold;\n        let gross_profit_margin = if expected_revenue > Decimal::ZERO {\n            (gross_profit / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let ebitda = gross_profit - operating_expenses;\n        let ebitda_margin = if expected_revenue > Decimal::ZERO {\n            (ebitda / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let net_profit = ebitda - depreciation - interest_expense;\n        let net_profit_margin = if expected_revenue > Decimal::ZERO {\n            (net_profit / expected_revenue) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        Ok(ProfitabilityResults {\n            gross_profit: self.decimal_to_string(gross_profit),\n            gross_profit_margin: format!(\"{}%\", self.decimal_to_string(gross_profit_margin)),\n            net_profit: self.decimal_to_string(net_profit),\n            net_profit_margin: format!(\"{}%\", self.decimal_to_string(net_profit_margin)),\n            ebitda: self.decimal_to_string(ebitda),\n            ebitda_margin: format!(\"{}%\", self.decimal_to_string(ebitda_margin)),\n        })\n    }\n\n    // Helper methods\n    fn parse_land_location(&self, location: &Option<String>) -> Result<LandLocationCategory, FinancialCalculationError> {\n        match location {\n            Some(loc) => LandLocationCategory::from_string(loc)\n                .map_err(|e| FinancialCalculationError::InvalidInput(e.to_string())),\n            None => Ok(LandLocationCategory::UrbanClass2), // Default\n        }\n    }\n\n    fn parse_land_use_type(&self, use_type: &Option<String>) -> Result<LandUseType, FinancialCalculationError> {\n        match use_type {\n            Some(ut) => LandUseType::from_string(ut)\n                .map_err(|e| FinancialCalculationError::InvalidInput(e.to_string())),\n            None => Ok(LandUseType::Mixed), // Default\n        }\n    }\n\n    fn decimal_to_string(&self, value: Decimal) -> String {\n        format!(\"{:.2}\", value)\n    }\n\n    fn calculate_npv(&self, investment: Decimal, revenue: Decimal, duration_months: u32, discount_rate: Decimal) -> Decimal {\n        let annual_discount_rate = discount_rate;\n        let monthly_discount_rate = annual_discount_rate / Decimal::from(12);\n        let monthly_cash_flow = revenue / Decimal::from(duration_months);\n        \n        let mut npv = -investment; // Initial investment as negative cash flow\n        \n        for month in 1..=duration_months {\n            let discount_factor = (Decimal::ONE + monthly_discount_rate).powi(-(month as i64));\n            npv += monthly_cash_flow * discount_factor;\n        }\n        \n        npv\n    }\n\n    fn estimate_irr(&self, investment: Decimal, revenue: Decimal, duration_months: u32) -> Decimal {\n        // Simplified IRR estimation using approximation\n        let total_return = revenue / investment;\n        let annual_periods = Decimal::from(12) / Decimal::from(duration_months);\n        \n        if total_return > Decimal::ONE {\n            (total_return - Decimal::ONE) * annual_periods * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        }\n    }\n\n    fn create_summary(&self, input: &FinancialInputData, calc_type: &FinancialCalculationType) -> Result<FinancialSummary, FinancialCalculationError> {\n        let total_investment = Decimal::from_f64_retain(input.total_investment)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid total investment\".to_string()))?;\n        let expected_revenue = Decimal::from_f64_retain(input.expected_revenue)\n            .ok_or_else(|| FinancialCalculationError::InvalidInput(\"Invalid expected revenue\".to_string()))?;\n\n        let estimated_profit = expected_revenue - total_investment;\n        let roi_percentage = if total_investment > Decimal::ZERO {\n            (estimated_profit / total_investment) * Decimal::from(100)\n        } else {\n            Decimal::ZERO\n        };\n\n        let payback_period = if expected_revenue > Decimal::ZERO {\n            (total_investment / (expected_revenue / Decimal::from(input.project_duration_months)))\n        } else {\n            Decimal::from(input.project_duration_months)\n        };\n\n        let recommendation = if roi_percentage >= Decimal::from(25) {\n            RecommendationType::StronglyRecommend\n        } else if roi_percentage >= Decimal::from(15) {\n            RecommendationType::Recommend\n        } else if roi_percentage >= Decimal::from(5) {\n            RecommendationType::Neutral\n        } else if roi_percentage >= Decimal::ZERO {\n            RecommendationType::Caution\n        } else {\n            RecommendationType::NotRecommended\n        };\n\n        Ok(FinancialSummary {\n            total_investment: self.decimal_to_string(total_investment),\n            expected_revenue: self.decimal_to_string(expected_revenue),\n            estimated_profit: self.decimal_to_string(estimated_profit),\n            roi_percentage: format!(\"{}%\", self.decimal_to_string(roi_percentage)),\n            payback_period: format!(\"{} months\", self.decimal_to_string(payback_period)),\n            recommendation: FinancialRecommendation {\n                recommendation,\n                confidence_level: \"Medium\".to_string(),\n                key_factors: vec![\n                    \"Market conditions\".to_string(),\n                    \"Construction costs\".to_string(),\n                    \"Regulatory environment\".to_string(),\n                ],\n                next_steps: vec![\n                    \"Conduct detailed market analysis\".to_string(),\n                    \"Obtain construction quotes\".to_string(),\n                    \"Review regulatory requirements\".to_string(),\n                ],\n            },\n        })\n    }\n\n    fn calculate_validity_date(&self) -> String {\n        let validity_duration = chrono::Duration::days(30); // Valid for 30 days\n        (Utc::now() + validity_duration).to_rfc3339()\n    }\n}\n\nimpl Default for FinancialCalculator {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n\n"}