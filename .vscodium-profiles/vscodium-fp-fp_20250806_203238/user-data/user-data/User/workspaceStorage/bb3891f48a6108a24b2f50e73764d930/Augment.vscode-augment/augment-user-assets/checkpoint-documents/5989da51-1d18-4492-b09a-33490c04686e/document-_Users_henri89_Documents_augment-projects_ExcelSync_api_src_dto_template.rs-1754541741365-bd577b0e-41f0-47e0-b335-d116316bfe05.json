{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/template.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Template creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: Option<String>,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_default: Option<bool>,\n}\n\n/// Template update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub version: Option<String>,\n    pub schema: Option<Value>,\n    pub business_rules: Option<Value>,\n    pub validation_rules: Option<Value>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template response DTO\n#[derive(Debug, Serialize)]\npub struct TemplateResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: String,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: String,\n    pub created_at: String,\n    pub updated_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Template creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: Option<String>,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_default: Option<bool>,\n}\n\n/// Template update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub version: Option<String>,\n    pub schema: Option<Value>,\n    pub business_rules: Option<Value>,\n    pub validation_rules: Option<Value>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct TemplateResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: String,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: String,\n    pub created_at: String,\n    pub updated_at: String,\n}\n"}