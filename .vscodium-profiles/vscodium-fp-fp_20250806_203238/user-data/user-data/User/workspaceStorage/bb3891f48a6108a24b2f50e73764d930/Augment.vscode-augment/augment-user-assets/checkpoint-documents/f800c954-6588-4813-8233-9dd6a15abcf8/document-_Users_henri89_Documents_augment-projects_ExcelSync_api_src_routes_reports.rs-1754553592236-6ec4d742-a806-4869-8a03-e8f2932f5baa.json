{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/reports.rs"}, "modifiedCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    response::{Json, Response},\n    routing::{get, post, delete},\n    Router,\n    http::{header, StatusCode},\n    body::Body,\n};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse tokio_util::io::ReaderStream;\nuse tokio::fs::File;\nuse validator::Validate;\n\nuse crate::{\n    dto::reports::*,\n    handlers::{json_response, ApiError},\n    services::ReportService,\n    AppState,\n};\nuse auth;\n\n/// Report generation query parameters\n#[derive(Debug, Deserialize)]\npub struct ReportGenerationQuery {\n    pub async_processing: Option<bool>,\n}\n\n/// Report routes\npub fn report_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/generate\", post(generate_report))\n        .route(\"/\", get(list_reports))\n        .route(\"/:report_id\", get(get_report_status))\n        .route(\"/:report_id\", delete(delete_report))\n        .route(\"/:report_id/download\", get(download_report))\n}\n\n/// Generate report endpoint\npub async fn generate_report(\n    State(state): State<AppState>,\n    Query(query): Query<ReportGenerationQuery>,\n    Json(request): Json<ReportGenerationRequest>,\n    req: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = req\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::BadRequest(\"Invalid user ID in token\".to_string()))?;\n\n    // Validate request\n    request.validate()\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid request: {}\", e)))?;\n\n    // Create report service\n    let report_service = ReportService::new(state.db.as_ref().clone())\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to initialize report service: {}\", e)))?;\n\n    // Check if async processing is requested\n    if query.async_processing.unwrap_or(false) {\n        // For now, we'll process synchronously but could implement async processing later\n        // This would typically involve queuing the job and returning a job ID\n        return Err(ApiError::BadRequest(\"Async processing not yet implemented\".to_string()));\n    }\n\n    // Generate report synchronously\n    let response = report_service\n        .generate_report(request, user_id)\n        .await\n        .map_err(|e| match e {\n            crate::dto::reports::ReportError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            crate::dto::reports::ReportError::InvalidConfiguration(msg) => ApiError::BadRequest(msg),\n            crate::dto::reports::ReportError::DataProcessingError(msg) => ApiError::InternalServerError(msg),\n            crate::dto::reports::ReportError::FileGenerationError(msg) => ApiError::InternalServerError(msg),\n            crate::dto::reports::ReportError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            _ => ApiError::InternalServerError(format!(\"Report generation failed: {}\", e)),\n        })?;\n\n    Ok(json_response(\"Report generated successfully\", response))\n}\n\n/// List reports endpoint\npub async fn list_reports(\n    State(state): State<AppState>,\n    Query(query): Query<ReportListRequest>,\n    req: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = req\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Create report service\n    let report_service = ReportService::new(state.db.as_ref().clone())\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to initialize report service: {}\", e)))?;\n\n    let response = report_service\n        .list_reports(query)\n        .await\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to list reports: {}\", e)))?;\n\n    Ok(json_response(\"Reports retrieved successfully\", response))\n}\n\n/// Get report status endpoint\npub async fn get_report_status(\n    State(state): State<AppState>,\n    Path(report_id): Path<String>,\n    req: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = req\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Create report service\n    let report_service = ReportService::new(state.db.as_ref().clone())\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to initialize report service: {}\", e)))?;\n\n    // Check if report exists by trying to get its path\n    match report_service.download_report(&report_id).await {\n        Ok((_, _)) => {\n            // Report exists and is ready\n            let response = ReportGenerationResponse {\n                report_id: report_id.clone(),\n                report_type: ReportType::CustomReport, // We don't store this info yet\n                format: ReportFormat::Excel, // We don't store this info yet\n                status: ReportStatus::Completed,\n                generated_at: chrono::Utc::now().to_rfc3339(),\n                generated_by: \"unknown\".to_string(), // We don't store this info yet\n                file_size: None,\n                download_url: Some(format!(\"/api/v1/reports/{}/download\", report_id)),\n                expires_at: None,\n                metadata: ReportMetadata {\n                    total_records: 0,\n                    processing_time_ms: 0,\n                    data_sources: vec![],\n                    filters_applied: vec![],\n                    columns: vec![],\n                },\n            };\n            Ok(json_response(\"Report status retrieved successfully\", response))\n        }\n        Err(crate::dto::reports::ReportError::ReportNotFound(_)) => {\n            Err(ApiError::NotFound(\"Report not found\".to_string()))\n        }\n        Err(e) => {\n            Err(ApiError::InternalServerError(format!(\"Failed to check report status: {}\", e)))\n        }\n    }\n}\n\n/// Download report endpoint\npub async fn download_report(\n    State(state): State<AppState>,\n    Path(report_id): Path<String>,\n    req: Request,\n) -> Result<Response<Body>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = req\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Create report service\n    let report_service = ReportService::new(state.db.as_ref().clone())\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to initialize report service: {}\", e)))?;\n\n    // Get report file path and mime type\n    let (file_path, mime_type) = report_service\n        .download_report(&report_id)\n        .await\n        .map_err(|e| match e {\n            crate::dto::reports::ReportError::ReportNotFound(_) => ApiError::NotFound(\"Report not found\".to_string()),\n            _ => ApiError::InternalServerError(format!(\"Failed to download report: {}\", e)),\n        })?;\n\n    // Open file for streaming\n    let file = File::open(&file_path).await\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to open report file: {}\", e)))?;\n\n    // Get file metadata for content length\n    let metadata = file.metadata().await\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to get file metadata: {}\", e)))?;\n\n    // Create file stream\n    let stream = ReaderStream::new(file);\n    let body = Body::from_stream(stream);\n\n    // Get filename from path\n    let filename = file_path\n        .file_name()\n        .and_then(|name| name.to_str())\n        .unwrap_or(&report_id);\n\n    // Build response with appropriate headers\n    let response = Response::builder()\n        .status(StatusCode::OK)\n        .header(header::CONTENT_TYPE, mime_type)\n        .header(header::CONTENT_LENGTH, metadata.len().to_string())\n        .header(\n            header::CONTENT_DISPOSITION,\n            format!(\"attachment; filename=\\\"{}\\\"\", filename),\n        )\n        .body(body)\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to build response: {}\", e)))?;\n\n    Ok(response)\n}\n\n/// Delete report endpoint\npub async fn delete_report(\n    State(state): State<AppState>,\n    Path(report_id): Path<String>,\n    req: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = req\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Create report service\n    let report_service = ReportService::new(state.db.as_ref().clone())\n        .map_err(|e| ApiError::InternalServerError(format!(\"Failed to initialize report service: {}\", e)))?;\n\n    // Delete the report\n    report_service\n        .delete_report(&report_id)\n        .await\n        .map_err(|e| match e {\n            crate::dto::reports::ReportError::ReportNotFound(_) => ApiError::NotFound(\"Report not found\".to_string()),\n            _ => ApiError::InternalServerError(format!(\"Failed to delete report: {}\", e)),\n        })?;\n\n    Ok(json_response(\"Report deleted successfully\", serde_json::json!({\"report_id\": report_id})))\n}\n"}