{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/validation.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Validation request\n#[derive(Debug, Deserialize, Validate)]\npub struct ValidationRequest {\n    pub validation_type: ValidationType,\n    pub data: Value,\n    pub context: Option<ValidationContext>,\n    pub rules: Option<Vec<ValidationRule>>,\n}\n\n/// Validation types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationType {\n    ProjectData,\n    TemplateSchema,\n    FinancialData,\n    UserInput,\n    FileUpload,\n    BusinessRules,\n    ComplianceCheck,\n}\n\n/// Validation context\n#[derive(Debug, Deserialize, Serialize)]\npub struct ValidationContext {\n    pub entity_type: String,\n    pub entity_id: Option<String>,\n    pub user_id: String,\n    pub organization_id: Option<String>,\n    pub locale: Option<String>,\n    pub business_rules_version: Option<String>,\n}\n\n/// Validation rule\n#[derive(Debug, Deserialize, Serialize)]\npub struct ValidationRule {\n    pub rule_id: String,\n    pub rule_type: ValidationRuleType,\n    pub field_path: String,\n    pub condition: ValidationCondition,\n    pub error_message: String,\n    pub severity: ValidationSeverity,\n    pub is_active: bool,\n}\n\n/// Validation rule types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationRuleType {\n    Required,\n    DataType,\n    Range,\n    Pattern,\n    Length,\n    Custom,\n    BusinessRule,\n    CrossField,\n    Conditional,\n}\n\n/// Validation condition\n#[derive(Debug, Deserialize, Serialize)]\npub struct ValidationCondition {\n    pub operator: ValidationOperator,\n    pub value: Option<Value>,\n    pub values: Option<Vec<Value>>,\n    pub expression: Option<String>,\n}\n\n/// Validation operators\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationOperator {\n    Equals,\n    NotEquals,\n    GreaterThan,\n    GreaterThanOrEqual,\n    LessThan,\n    LessThanOrEqual,\n    Contains,\n    NotContains,\n    StartsWith,\n    EndsWith,\n    Matches,\n    NotMatches,\n    In,\n    NotIn,\n    Between,\n    NotBetween,\n    IsNull,\n    IsNotNull,\n    IsEmpty,\n    IsNotEmpty,\n    Custom,\n}\n\n/// Validation severity\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationSeverity {\n    Info,\n    Warning,\n    Error,\n    Critical,\n}\n\n/// Validation response\n#[derive(Debug, Serialize)]\npub struct ValidationResponse {\n    pub validation_id: String,\n    pub is_valid: bool,\n    pub validation_type: ValidationType,\n    pub results: ValidationResults,\n    pub validated_at: String,\n    pub validation_duration_ms: u64,\n}\n\n/// Validation results\n#[derive(Debug, Serialize)]\npub struct ValidationResults {\n    pub errors: Vec<ValidationError>,\n    pub warnings: Vec<ValidationWarning>,\n    pub info: Vec<ValidationInfo>,\n    pub summary: ValidationSummary,\n    pub field_results: Vec<FieldValidationResult>,\n}\n\n/// Validation error\n#[derive(Debug, Serialize)]\npub struct ValidationError {\n    pub error_id: String,\n    pub field_path: String,\n    pub field_name: String,\n    pub error_code: String,\n    pub message: String,\n    pub severity: ValidationSeverity,\n    pub rule_id: Option<String>,\n    pub suggested_fix: Option<String>,\n    pub context: Option<Value>,\n}\n\n/// Validation warning\n#[derive(Debug, Serialize)]\npub struct ValidationWarning {\n    pub warning_id: String,\n    pub field_path: String,\n    pub field_name: String,\n    pub warning_code: String,\n    pub message: String,\n    pub suggestion: Option<String>,\n    pub can_ignore: bool,\n    pub context: Option<Value>,\n}\n\n/// Validation info\n#[derive(Debug, Serialize)]\npub struct ValidationInfo {\n    pub info_id: String,\n    pub field_path: String,\n    pub field_name: String,\n    pub info_code: String,\n    pub message: String,\n    pub additional_data: Option<Value>,\n}\n\n/// Validation summary\n#[derive(Debug, Serialize)]\npub struct ValidationSummary {\n    pub total_fields_validated: u32,\n    pub total_rules_applied: u32,\n    pub error_count: u32,\n    pub warning_count: u32,\n    pub info_count: u32,\n    pub validation_score: f64,\n    pub overall_status: ValidationStatus,\n}\n\n/// Validation status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationStatus {\n    Passed,\n    PassedWithWarnings,\n    Failed,\n    Critical,\n}\n\n/// Field validation result\n#[derive(Debug, Serialize)]\npub struct FieldValidationResult {\n    pub field_path: String,\n    pub field_name: String,\n    pub field_type: String,\n    pub is_valid: bool,\n    pub rules_applied: Vec<String>,\n    pub errors: Vec<ValidationError>,\n    pub warnings: Vec<ValidationWarning>,\n    pub normalized_value: Option<Value>,\n}\n\n/// Template validation request\n#[derive(Debug, Deserialize, Validate)]\npub struct TemplateValidationRequest {\n    pub template_id: String,\n    pub template_data: Value,\n    pub validation_level: ValidationLevel,\n    pub include_business_rules: Option<bool>,\n}\n\n/// Validation levels\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationLevel {\n    Basic,\n    Standard,\n    Strict,\n    Custom,\n}\n\n/// Template validation response\n#[derive(Debug, Serialize)]\npub struct TemplateValidationResponse {\n    pub template_id: String,\n    pub validation_level: ValidationLevel,\n    pub schema_validation: SchemaValidationResult,\n    pub business_rules_validation: Option<BusinessRulesValidationResult>,\n    pub data_quality_assessment: DataQualityAssessment,\n    pub recommendations: Vec<ValidationRecommendation>,\n}\n\n/// Schema validation result\n#[derive(Debug, Serialize)]\npub struct SchemaValidationResult {\n    pub is_schema_valid: bool,\n    pub schema_errors: Vec<SchemaValidationError>,\n    pub missing_required_fields: Vec<String>,\n    pub unexpected_fields: Vec<String>,\n    pub type_mismatches: Vec<TypeMismatch>,\n}\n\n/// Schema validation error\n#[derive(Debug, Serialize)]\npub struct SchemaValidationError {\n    pub field_path: String,\n    pub error_type: SchemaErrorType,\n    pub message: String,\n    pub expected: String,\n    pub actual: String,\n}\n\n/// Schema error types\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum SchemaErrorType {\n    MissingField,\n    InvalidType,\n    InvalidFormat,\n    ConstraintViolation,\n    UnknownField,\n}\n\n/// Type mismatch\n#[derive(Debug, Serialize)]\npub struct TypeMismatch {\n    pub field_path: String,\n    pub expected_type: String,\n    pub actual_type: String,\n    pub can_convert: bool,\n    pub conversion_suggestion: Option<String>,\n}\n\n/// Business rules validation result\n#[derive(Debug, Serialize)]\npub struct BusinessRulesValidationResult {\n    pub rules_passed: u32,\n    pub rules_failed: u32,\n    pub rule_violations: Vec<BusinessRuleViolation>,\n    pub calculated_values: Vec<CalculatedValue>,\n    pub compliance_status: ComplianceStatus,\n}\n\n/// Business rule violation\n#[derive(Debug, Serialize)]\npub struct BusinessRuleViolation {\n    pub rule_id: String,\n    pub rule_name: String,\n    pub violation_type: ViolationType,\n    pub affected_fields: Vec<String>,\n    pub message: String,\n    pub severity: ValidationSeverity,\n    pub remediation_steps: Vec<String>,\n}\n\n/// Violation types\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ViolationType {\n    ValueOutOfRange,\n    InvalidCombination,\n    MissingDependency,\n    ConflictingValues,\n    ComplianceViolation,\n    BusinessLogicError,\n}\n\n/// Calculated value\n#[derive(Debug, Serialize)]\npub struct CalculatedValue {\n    pub field_name: String,\n    pub calculated_value: Value,\n    pub formula: String,\n    pub dependencies: Vec<String>,\n    pub confidence_level: f64,\n}\n\n/// Compliance status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComplianceStatus {\n    Compliant,\n    PartiallyCompliant,\n    NonCompliant,\n    RequiresReview,\n}\n\n/// Data quality assessment\n#[derive(Debug, Serialize)]\npub struct DataQualityAssessment {\n    pub overall_score: f64,\n    pub completeness_score: f64,\n    pub accuracy_score: f64,\n    pub consistency_score: f64,\n    pub validity_score: f64,\n    pub quality_issues: Vec<DataQualityIssue>,\n    pub improvement_suggestions: Vec<String>,\n}\n\n/// Data quality issue\n#[derive(Debug, Serialize)]\npub struct DataQualityIssue {\n    pub issue_type: DataQualityIssueType,\n    pub affected_fields: Vec<String>,\n    pub description: String,\n    pub impact_level: ImpactLevel,\n    pub suggested_action: String,\n}\n\n/// Data quality issue types\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum DataQualityIssueType {\n    MissingData,\n    InconsistentFormat,\n    OutlierValues,\n    DuplicateData,\n    InvalidReferences,\n    StaleData,\n}\n\n/// Impact levels\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ImpactLevel {\n    Low,\n    Medium,\n    High,\n    Critical,\n}\n\n/// Validation recommendation\n#[derive(Debug, Serialize)]\npub struct ValidationRecommendation {\n    pub recommendation_id: String,\n    pub category: RecommendationCategory,\n    pub priority: RecommendationPriority,\n    pub title: String,\n    pub description: String,\n    pub implementation_steps: Vec<String>,\n    pub expected_benefit: String,\n    pub effort_estimate: String,\n}\n\n/// Recommendation categories\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum RecommendationCategory {\n    DataQuality,\n    Performance,\n    Security,\n    Compliance,\n    Usability,\n    Maintenance,\n}\n\n/// Recommendation priorities\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum RecommendationPriority {\n    Low,\n    Medium,\n    High,\n    Critical,\n}\n\n/// Batch validation request\n#[derive(Debug, Deserialize, Validate)]\npub struct BatchValidationRequest {\n    pub validation_type: ValidationType,\n    pub items: Vec<BatchValidationItem>,\n    pub validation_options: Option<ValidationOptions>,\n}\n\n/// Batch validation item\n#[derive(Debug, Deserialize)]\npub struct BatchValidationItem {\n    pub item_id: String,\n    pub data: Value,\n    pub context: Option<ValidationContext>,\n}\n\n/// Validation options\n#[derive(Debug, Deserialize)]\npub struct ValidationOptions {\n    pub fail_fast: Option<bool>,\n    pub include_warnings: Option<bool>,\n    pub validation_level: Option<ValidationLevel>,\n    pub parallel_processing: Option<bool>,\n    pub max_errors_per_item: Option<u32>,\n}\n\n/// Batch validation response\n#[derive(Debug, Serialize)]\npub struct BatchValidationResponse {\n    pub batch_id: String,\n    pub total_items: u32,\n    pub processed_items: u32,\n    pub valid_items: u32,\n    pub invalid_items: u32,\n    pub results: Vec<BatchValidationItemResult>,\n    pub summary: BatchValidationSummary,\n}\n\n/// Batch validation item result\n#[derive(Debug, Serialize)]\npub struct BatchValidationItemResult {\n    pub item_id: String,\n    pub is_valid: bool,\n    pub validation_result: ValidationResults,\n    pub processing_time_ms: u64,\n}\n\n/// Batch validation summary\n#[derive(Debug, Serialize)]\npub struct BatchValidationSummary {\n    pub success_rate: f64,\n    pub total_errors: u32,\n    pub total_warnings: u32,\n    pub most_common_errors: Vec<ErrorFrequency>,\n    pub processing_time_ms: u64,\n}\n\n/// Error frequency\n#[derive(Debug, Serialize)]\npub struct ErrorFrequency {\n    pub error_code: String,\n    pub count: u32,\n    pub percentage: f64,\n}\n"}