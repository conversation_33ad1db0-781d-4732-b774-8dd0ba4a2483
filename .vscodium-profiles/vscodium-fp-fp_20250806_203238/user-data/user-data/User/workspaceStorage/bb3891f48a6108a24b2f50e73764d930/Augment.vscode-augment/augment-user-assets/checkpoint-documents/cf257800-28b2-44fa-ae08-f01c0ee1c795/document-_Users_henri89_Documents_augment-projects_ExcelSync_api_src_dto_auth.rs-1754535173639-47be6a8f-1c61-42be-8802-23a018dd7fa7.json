{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/auth.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Login request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct LoginRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n}\n\n/// Login response DTO\n#[derive(Debug, Serialize)]\npub struct LoginResponse {\n    pub token: String,\n    pub expires_at: String,\n    pub user: UserInfo,\n}\n\n/// User info in auth response\n#[derive(Debug, Serialize)]\npub struct UserInfo {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// Token refresh request DTO\n#[derive(Debug, Deserialize)]\npub struct RefreshTokenRequest {\n    pub token: String,\n}\n\n/// Token refresh response DTO\n#[derive(Debug, Serialize)]\npub struct RefreshTokenResponse {\n    pub token: String,\n    pub expires_at: String,\n}\n"}