{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/lib.rs"}, "originalCode": "pub mod docs;\npub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\npub mod services;\n\npub use routes::create_routes;\npub use middleware::{\n    cors_layer, development_cors_layer, production_cors_layer,\n    auth_middleware, extract_user_claims, rate_limit_layer, RateLimitConfig,\n    input_validation_middleware, sql_injection_protection_middleware,\n    path_traversal_protection_middleware\n};\npub use handlers::{ApiError, ApiResponse, json_response};\npub use state::AppState;\n\nuse axum::Router;\nuse tower::ServiceBuilder;\nuse tower_http::{limit::RequestBodyLimitLayer, trace::TraceLayer};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    create_app_with_config(state, false)\n}\n\n/// Create the main application with environment-specific configuration\npub fn create_app_with_config(state: AppState, is_production: bool) -> Router {\n    let cors_layer = if is_production {\n        production_cors_layer()\n    } else {\n        development_cors_layer()\n    };\n\n    let rate_limit_config = RateLimitConfig::default();\n\n    // Create public routes\n    let public_routes = Router::new()\n        .route(\"/health\", axum::routing::get(routes::health_check))\n        .nest(\"/api/v1/auth\", routes::auth_routes());\n\n    // Create protected routes with auth middleware\n    let protected_routes = Router::new()\n        .nest(\"/api/v1/users\", routes::user_routes())\n        .nest(\"/api/v1/organizations\", routes::organization_routes())\n        .nest(\"/api/v1/projects\", routes::project_routes())\n        .nest(\"/api/v1/templates\", routes::template_routes())\n        .nest(\"/api/v1/validation\", routes::validation_routes())\n        .layer(axum::middleware::from_fn_with_state(state.clone(), auth_middleware));\n\n    public_routes\n        .merge(protected_routes)\n        .layer(\n            ServiceBuilder::new()\n                // Security middleware (outermost)\n                .layer(axum::middleware::from_fn(path_traversal_protection_middleware))\n                .layer(axum::middleware::from_fn(sql_injection_protection_middleware))\n                .layer(axum::middleware::from_fn(input_validation_middleware))\n\n                // Rate limiting\n                .layer(rate_limit_layer(rate_limit_config))\n\n                // Logging and tracing\n                .layer(TraceLayer::new_for_http())\n\n                // Request size limits\n                .layer(RequestBodyLimitLayer::new(10 * 1024 * 1024)) // 10MB limit\n\n                // CORS (innermost)\n                .layer(cors_layer)\n        )\n        .with_state(state)\n}\n\n\n", "modifiedCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\npub mod services;\n\npub use routes::create_routes;\npub use middleware::{\n    cors_layer, development_cors_layer, production_cors_layer,\n    auth_middleware, extract_user_claims, rate_limit_layer, RateLimitConfig,\n    input_validation_middleware, sql_injection_protection_middleware,\n    path_traversal_protection_middleware\n};\npub use handlers::{ApiError, ApiResponse, json_response};\npub use state::AppState;\n\nuse axum::Router;\nuse tower::ServiceBuilder;\nuse tower_http::{limit::RequestBodyLimitLayer, trace::TraceLayer};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    create_app_with_config(state, false)\n}\n\n/// Create the main application with environment-specific configuration\npub fn create_app_with_config(state: AppState, is_production: bool) -> Router {\n    let cors_layer = if is_production {\n        production_cors_layer()\n    } else {\n        development_cors_layer()\n    };\n\n    let rate_limit_config = RateLimitConfig::default();\n\n    // Create public routes\n    let public_routes = Router::new()\n        .route(\"/health\", axum::routing::get(routes::health_check))\n        .nest(\"/api/v1/auth\", routes::auth_routes());\n\n    // Create protected routes with auth middleware\n    let protected_routes = Router::new()\n        .nest(\"/api/v1/users\", routes::user_routes())\n        .nest(\"/api/v1/organizations\", routes::organization_routes())\n        .nest(\"/api/v1/projects\", routes::project_routes())\n        .nest(\"/api/v1/templates\", routes::template_routes())\n        .nest(\"/api/v1/validation\", routes::validation_routes())\n        .layer(axum::middleware::from_fn_with_state(state.clone(), auth_middleware));\n\n    public_routes\n        .merge(protected_routes)\n        .layer(\n            ServiceBuilder::new()\n                // Security middleware (outermost)\n                .layer(axum::middleware::from_fn(path_traversal_protection_middleware))\n                .layer(axum::middleware::from_fn(sql_injection_protection_middleware))\n                .layer(axum::middleware::from_fn(input_validation_middleware))\n\n                // Rate limiting\n                .layer(rate_limit_layer(rate_limit_config))\n\n                // Logging and tracing\n                .layer(TraceLayer::new_for_http())\n\n                // Request size limits\n                .layer(RequestBodyLimitLayer::new(10 * 1024 * 1024)) // 10MB limit\n\n                // CORS (innermost)\n                .layer(cors_layer)\n        )\n        .with_state(state)\n}\n\n\n"}