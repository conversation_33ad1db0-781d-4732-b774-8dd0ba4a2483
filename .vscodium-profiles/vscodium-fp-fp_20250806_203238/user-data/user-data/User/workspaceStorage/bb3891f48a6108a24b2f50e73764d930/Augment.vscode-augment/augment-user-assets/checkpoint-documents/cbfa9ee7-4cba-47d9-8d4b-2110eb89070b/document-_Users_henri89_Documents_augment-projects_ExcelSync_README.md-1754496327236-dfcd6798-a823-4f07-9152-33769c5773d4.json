{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "README.md"}, "originalCode": "# ExcelSync\nIntegrating a backend with an Excel plugin for automatic data synchronization and calculations\n", "modifiedCode": "# ExcelSync\n\n**Integrating a backend with an Excel plugin for automatic data synchronization and calculations**\n\n## Overview\n\nExcelSync is a comprehensive Excel add-in system designed for real estate project management with seamless backend integration. The system provides template-based data input, automatic synchronization, and robust reporting capabilities through a PostgreSQL database backend.\n\n## Architecture\n\n### System Components\n- **Excel Add-in Client**: VSTO-based Excel plugin with ribbon interface\n- **Backend API**: RESTful API services for data processing and synchronization\n- **PostgreSQL Database**: Centralized data storage with structured schema\n- **Authentication System**: User management and access control\n\n### Technology Stack\n- **Frontend**: Microsoft Excel VSTO Add-in\n- **Backend**: API services (technology to be determined)\n- **Database**: PostgreSQL\n- **Design Reference**: SAP Analytics Cloud patterns\n- **Language**: Vietnamese interface\n\n## Database Configuration\n\n### UAT Environment\n```\nHost: *************\nPort: 5432\nDatabase: postgres\nUsername: uat01\nPassword: 123456\n```\n\n### Database Schema\nThe system uses the `Addin` schema with the following core tables:\n- `ADDI_USER_TRAN` - User authentication and management\n- `ORGA_TRAN` - Organization data\n- `PROJ_DSET_TRAN` - Project dataset transactions\n- `PROJ_INDI_TRAN` - Project individual transactions\n- `PROJ_INFO_TYPE` - Project information types\n- `PROJ_LAND_INFO_TRAN` - Project land information\n- `PROJ_VERT_TRAN` - Project vertical transactions\n\n## Core Features\n\n### 1. Connection Management\n- **Sign-in**: Secure authentication with server credentials\n- **Sign-out**: Clean session termination\n\n### 2. Template System\n- **Input Data**: Template selection and data entry interface\n- **Load Template**: Drag-and-drop template deployment to Excel sheets\n- **Data Management**: Support for new entries and version updates\n- **Save Data**: API-based data synchronization to backend\n\n### 3. Reporting & Analytics\n- **Project Reports**: Comprehensive project cost and land reports\n- **Dashboard**: Real-time data visualization\n- **Data Refresh**: Live data synchronization\n\n### 4. User Interface\n- **Ribbon Integration**: Native Excel ribbon with organized menu structure\n- **Hierarchical Navigation**: Three-level menu system (Cấp 1, Cấp 2, Cấp 3)\n- **Icon Support**: Visual indicators for all functions\n- **Multi-language**: Vietnamese language support\n\n## Menu Structure\n\n### Level 1: Connect\n- Sign-in functionality with database connection\n- Sign-out with session management\n\n### Level 2: Template\n- **Project Land**: Land use cost calculations\n  - Project Information\n  - Project Design\n  - Project Assumptions\n- **Project Land Costing**: Detailed cost analysis\n  - Project Design\n  - Project Contract\n  - Project Cost Actual\n- **Tax Review**: Tax management\n  - Purchase Invoice List\n  - Sales Invoice List\n\n### Level 3: Report\n- **Project Land Cost**: Investment reports and estimates\n- **Dashboard**: Interactive data visualization\n- **Data Refresh**: Real-time synchronization\n\n### Level 4: Help\n- Help documentation\n- About information\n\n## API Integration\n\n### Authentication Endpoints\n- User login with email/password authentication\n- Session management and token handling\n\n### Data Operations\n- Template loading and deployment\n- Data validation and saving\n- Real-time synchronization\n- Version control and conflict resolution\n\n### Template Management\n- Dynamic template retrieval\n- Historical data loading\n- Data overwrite protection with user confirmation\n\n## Development Timeline\n\n### Design Phase (01/08/2025)\n- UI Mockup design (Reference: SAP Analytics Cloud)\n- Icon design and implementation\n- Database schema finalization\n\n### Implementation Phase (05/08/2025)\n- Core functionality development\n- API integration\n- Testing and validation\n\n## User Workflow\n\n### Data Input Process\n1. **Authentication**: User signs in with credentials\n2. **Template Selection**: Choose appropriate template from business area\n3. **Template Loading**: Drag template to active Excel sheet\n4. **Data Entry**: Fill template with project information\n5. **Data Validation**: System validates input data\n6. **Save Operation**: Data synchronized to backend database\n7. **Confirmation**: User receives save confirmation\n\n### Template Management\n- **First-time Use**: Complete template setup and data entry\n- **Updates**: Load existing data and create new versions\n- **Conflict Resolution**: Handle data overwrite scenarios with user prompts\n\n## Security Considerations\n\n### Authentication\n- Secure credential management\n- Session timeout handling\n- User access control\n\n### Data Protection\n- Input validation and sanitization\n- SQL injection prevention\n- Secure API communication\n\n## Performance Optimization\n\n### Data Handling\n- Efficient template loading\n- Optimized database queries\n- Minimal network overhead\n\n### User Experience\n- Responsive UI interactions\n- Progress indicators for long operations\n- Error handling and user feedback\n"}