{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/auth.rs"}, "modifiedCode": "use axum::{\n    extract::{Request, State},\n    http::{HeaderMap, StatusCode},\n    middleware::Next,\n    response::Response,\n};\nuse crate::AppState;\n\n/// JWT authentication middleware\npub async fn auth_middleware(\n    State(_state): State<AppState>,\n    headers: HeaderMap,\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Extract Authorization header\n    let auth_header = headers\n        .get(\"Authorization\")\n        .and_then(|header| header.to_str().ok())\n        .and_then(|header| header.strip_prefix(\"Bearer \"));\n\n    match auth_header {\n        Some(_token) => {\n            // TODO: Validate JWT token\n            Ok(next.run(request).await)\n        }\n        None => Err(StatusCode::UNAUTHORIZED),\n    }\n}\n"}