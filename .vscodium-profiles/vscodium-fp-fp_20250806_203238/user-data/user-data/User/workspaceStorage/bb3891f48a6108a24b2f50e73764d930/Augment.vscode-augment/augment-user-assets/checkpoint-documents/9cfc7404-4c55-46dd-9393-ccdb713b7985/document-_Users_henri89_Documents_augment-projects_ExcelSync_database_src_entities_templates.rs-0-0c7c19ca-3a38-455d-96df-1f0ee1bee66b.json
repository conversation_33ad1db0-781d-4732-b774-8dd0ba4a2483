{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/templates.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::TemplateType;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"templates\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: TemplateType,\n    pub version: String,\n    \n    // Template schema and validation rules\n    pub schema: Json,\n    pub business_rules: J<PERSON>,\n    pub validation_rules: Json,\n    \n    // Template metadata\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CreatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    Creator,\n    \n    #[sea_orm(has_many = \"super::project_data::Entity\")]\n    ProjectData,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Creator.def()\n    }\n}\n\nimpl Related<super::project_data::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::ProjectData.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            is_default: Set(false),\n            version: Set(\"1.0.0\".to_string()),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::TemplateType;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"templates\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: TemplateType,\n    pub version: String,\n    \n    // Template schema and validation rules\n    pub schema: Json,\n    pub business_rules: J<PERSON>,\n    pub validation_rules: Json,\n    \n    // Template metadata\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CreatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    Creator,\n    \n    #[sea_orm(has_many = \"super::project_data::Entity\")]\n    ProjectData,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Creator.def()\n    }\n}\n\nimpl Related<super::project_data::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::ProjectData.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            is_default: Set(false),\n            version: Set(\"1.0.0\".to_string()),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n"}