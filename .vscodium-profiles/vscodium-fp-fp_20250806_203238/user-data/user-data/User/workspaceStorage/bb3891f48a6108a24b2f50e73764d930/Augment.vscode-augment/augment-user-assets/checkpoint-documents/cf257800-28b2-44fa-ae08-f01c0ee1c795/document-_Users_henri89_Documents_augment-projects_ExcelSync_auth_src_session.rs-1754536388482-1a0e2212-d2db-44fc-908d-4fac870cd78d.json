{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/session.rs"}, "originalCode": "use anyhow::{Context, Result};\nuse chrono::{DateTime, Duration, Utc};\nuse redis::{AsyncCommands, Client};\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse uuid::Uuid;\n\nuse crate::AuthError;\n\n/// Session information\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Session {\n    pub id: Uuid,\n    pub user_id: Uuid,\n    pub session_token: String,\n    pub ip_address: String,\n    pub user_agent: String,\n    pub is_active: bool,\n    pub expires_at: DateTime<Utc>,\n    pub last_activity: DateTime<Utc>,\n    pub created_at: DateTime<Utc>,\n}\n\nimpl Session {\n    /// Create new session\n    pub fn new(\n        user_id: Uuid,\n        ip_address: String,\n        user_agent: String,\n        session_timeout_hours: i64,\n    ) -> Self {\n        let now = Utc::now();\n        let expires_at = now + Duration::hours(session_timeout_hours);\n        \n        Self {\n            id: Uuid::new_v4(),\n            user_id,\n            session_token: Self::generate_session_token(),\n            ip_address,\n            user_agent,\n            is_active: true,\n            expires_at,\n            last_activity: now,\n            created_at: now,\n        }\n    }\n\n    /// Generate secure session token\n    fn generate_session_token() -> String {\n        use base64::{engine::general_purpose, Engine as _};\n        use rand::RngCore;\n        \n        let mut bytes = [0u8; 32];\n        rand::thread_rng().fill_bytes(&mut bytes);\n        general_purpose::URL_SAFE_NO_PAD.encode(bytes)\n    }\n\n    /// Check if session is expired\n    pub fn is_expired(&self) -> bool {\n        Utc::now() > self.expires_at\n    }\n\n    /// Check if session is valid (active and not expired)\n    pub fn is_valid(&self) -> bool {\n        self.is_active && !self.is_expired()\n    }\n\n    /// Update last activity timestamp\n    pub fn update_activity(&mut self) {\n        self.last_activity = Utc::now();\n    }\n\n    /// Deactivate session\n    pub fn deactivate(&mut self) {\n        self.is_active = false;\n    }\n\n    /// Extend session expiration\n    pub fn extend_expiration(&mut self, hours: i64) {\n        self.expires_at = Utc::now() + Duration::hours(hours);\n        self.update_activity();\n    }\n}\n\n/// Session management service\n#[derive(Clone)]\npub struct SessionService {\n    default_timeout_hours: i64,\n    max_sessions_per_user: u32,\n}\n\nimpl SessionService {\n    /// Create new session service\n    pub fn new(default_timeout_hours: i64, max_sessions_per_user: u32) -> Self {\n        Self {\n            default_timeout_hours,\n            max_sessions_per_user,\n        }\n    }\n\n    /// Create new session for user\n    pub fn create_session(\n        &self,\n        user_id: Uuid,\n        ip_address: String,\n        user_agent: String,\n    ) -> Session {\n        Session::new(user_id, ip_address, user_agent, self.default_timeout_hours)\n    }\n\n    /// Validate session token format\n    pub fn validate_session_token(&self, token: &str) -> Result<(), AuthError> {\n        if token.is_empty() {\n            return Err(AuthError::InvalidToken);\n        }\n        \n        // Basic validation - should be base64 encoded\n        use base64::{engine::general_purpose, Engine as _};\n        general_purpose::URL_SAFE_NO_PAD\n            .decode(token)\n            .map_err(|_| AuthError::InvalidToken)?;\n        \n        Ok(())\n    }\n\n    /// Check if user has too many active sessions\n    pub fn check_session_limit(&self, active_session_count: u32) -> Result<(), AuthError> {\n        if active_session_count >= self.max_sessions_per_user {\n            return Err(AuthError::InvalidCredentials);\n        }\n        Ok(())\n    }\n\n    /// Generate session cleanup criteria\n    pub fn get_cleanup_criteria(&self) -> DateTime<Utc> {\n        Utc::now() - Duration::hours(24) // Clean up sessions older than 24 hours\n    }\n}\n\nimpl Default for SessionService {\n    fn default() -> Self {\n        Self::new(8, 5) // 8 hours timeout, max 5 sessions per user\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_session_creation() {\n        let user_id = Uuid::new_v4();\n        let session = Session::new(\n            user_id,\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        assert_eq!(session.user_id, user_id);\n        assert_eq!(session.ip_address, \"***********\");\n        assert_eq!(session.user_agent, \"Mozilla/5.0\");\n        assert!(session.is_active);\n        assert!(!session.is_expired());\n        assert!(session.is_valid());\n    }\n\n    #[test]\n    fn test_session_token_generation() {\n        let session1 = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        let session2 = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        // Tokens should be different\n        assert_ne!(session1.session_token, session2.session_token);\n        \n        // Tokens should be valid base64\n        use base64::{engine::general_purpose, Engine as _};\n        assert!(general_purpose::URL_SAFE_NO_PAD.decode(&session1.session_token).is_ok());\n        assert!(general_purpose::URL_SAFE_NO_PAD.decode(&session2.session_token).is_ok());\n    }\n\n    #[test]\n    fn test_session_expiration() {\n        let mut session = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            0, // Expires immediately\n        );\n        \n        // Should be expired\n        assert!(session.is_expired());\n        assert!(!session.is_valid());\n        \n        // Extend expiration\n        session.extend_expiration(1);\n        assert!(!session.is_expired());\n        assert!(session.is_valid());\n    }\n\n    #[test]\n    fn test_session_deactivation() {\n        let mut session = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        assert!(session.is_valid());\n        \n        session.deactivate();\n        assert!(!session.is_valid());\n    }\n\n    #[test]\n    fn test_session_service() {\n        let service = SessionService::new(8, 5);\n        let user_id = Uuid::new_v4();\n        \n        let session = service.create_session(\n            user_id,\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n        );\n        \n        assert_eq!(session.user_id, user_id);\n        assert!(session.is_valid());\n        \n        // Test session limit\n        assert!(service.check_session_limit(4).is_ok());\n        assert!(service.check_session_limit(5).is_err());\n        \n        // Test token validation\n        assert!(service.validate_session_token(&session.session_token).is_ok());\n        assert!(service.validate_session_token(\"\").is_err());\n        assert!(service.validate_session_token(\"invalid-token\").is_err());\n    }\n}\n\n/// Redis-based session manager for distributed session storage\n#[derive(Debug, Clone)]\npub struct RedisSessionManager {\n    client: Client,\n    default_ttl: Duration,\n    max_sessions_per_user: usize,\n}\n\nimpl RedisSessionManager {\n    /// Create a new Redis session manager\n    pub fn new(redis_url: &str, default_ttl_hours: i64, max_sessions_per_user: usize) -> Result<Self> {\n        let client = Client::open(redis_url)\n            .context(\"Failed to create Redis client\")?;\n\n        let default_ttl = Duration::hours(default_ttl_hours);\n\n        Ok(Self {\n            client,\n            default_ttl,\n            max_sessions_per_user,\n        })\n    }\n\n    /// Store session in Redis\n    pub async fn store_session(&self, session: &Session) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session.session_token);\n        let user_sessions_key = format!(\"user_sessions:{}\", session.user_id);\n        let session_json = serde_json::to_string(session)\n            .context(\"Failed to serialize session data\")?;\n\n        // Clean up old sessions for this user\n        self.cleanup_user_sessions(session.user_id).await?;\n\n        // Store session data with TTL\n        let ttl_seconds = (session.expires_at - Utc::now()).num_seconds() as usize;\n        conn.setex(&session_key, ttl_seconds, &session_json).await\n            .context(\"Failed to store session data\")?;\n\n        // Add to user's session list\n        conn.sadd(&user_sessions_key, &session.session_token).await\n            .context(\"Failed to add session to user list\")?;\n        conn.expire(&user_sessions_key, ttl_seconds).await\n            .context(\"Failed to set TTL on user sessions\")?;\n\n        Ok(())\n    }\n\n    /// Get session from Redis\n    pub async fn get_session(&self, session_token: &str) -> Result<Option<Session>> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session_token);\n        let session_json: Option<String> = conn.get(&session_key).await\n            .context(\"Failed to get session data\")?;\n\n        match session_json {\n            Some(json) => {\n                let mut session: Session = serde_json::from_str(&json)\n                    .context(\"Failed to deserialize session data\")?;\n\n                // Check if session is expired\n                if session.expires_at < Utc::now() {\n                    self.delete_session(session_token).await?;\n                    return Ok(None);\n                }\n\n                // Update last activity\n                session.last_activity = Utc::now();\n                let updated_json = serde_json::to_string(&session)\n                    .context(\"Failed to serialize updated session data\")?;\n\n                let ttl_seconds = (session.expires_at - Utc::now()).num_seconds() as usize;\n                conn.setex(&session_key, ttl_seconds, &updated_json).await\n                    .context(\"Failed to update session data\")?;\n\n                Ok(Some(session))\n            }\n            None => Ok(None),\n        }\n    }\n\n    /// Delete session from Redis\n    pub async fn delete_session(&self, session_token: &str) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session_token);\n\n        // Get session data to find user ID\n        if let Some(session) = self.get_session(session_token).await? {\n            let user_sessions_key = format!(\"user_sessions:{}\", session.user_id);\n            conn.srem(&user_sessions_key, session_token).await\n                .context(\"Failed to remove session from user list\")?;\n        }\n\n        conn.del(&session_key).await\n            .context(\"Failed to delete session\")?;\n\n        Ok(())\n    }\n\n    /// Delete all sessions for a user\n    pub async fn delete_user_sessions(&self, user_id: Uuid) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        for session_token in session_tokens {\n            let session_key = format!(\"session:{}\", session_token);\n            conn.del(&session_key).await\n                .context(\"Failed to delete session\")?;\n        }\n\n        conn.del(&user_sessions_key).await\n            .context(\"Failed to delete user sessions list\")?;\n\n        Ok(())\n    }\n\n    /// Clean up old sessions for a user (keep only the most recent ones)\n    async fn cleanup_user_sessions(&self, user_id: Uuid) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        if session_tokens.len() >= self.max_sessions_per_user {\n            // Get session data for all sessions to sort by creation time\n            let mut sessions_with_data = Vec::new();\n            for session_token in session_tokens {\n                if let Some(session) = self.get_session(&session_token).await? {\n                    sessions_with_data.push((session_token, session));\n                }\n            }\n\n            // Sort by creation time (oldest first)\n            sessions_with_data.sort_by(|a, b| a.1.created_at.cmp(&b.1.created_at));\n\n            // Delete oldest sessions to make room\n            let sessions_to_delete = sessions_with_data.len().saturating_sub(self.max_sessions_per_user - 1);\n            for (session_token, _) in sessions_with_data.into_iter().take(sessions_to_delete) {\n                self.delete_session(&session_token).await?;\n            }\n        }\n\n        Ok(())\n    }\n\n    /// Get all active sessions for a user\n    pub async fn get_user_sessions(&self, user_id: Uuid) -> Result<Vec<Session>> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        let mut sessions = Vec::new();\n        for session_token in session_tokens {\n            if let Some(session) = self.get_session(&session_token).await? {\n                sessions.push(session);\n            }\n        }\n\n        Ok(sessions)\n    }\n}\n", "modifiedCode": "use anyhow::{Context, Result};\nuse chrono::{DateTime, Duration, Utc};\nuse redis::{AsyncCommands, Client};\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse uuid::Uuid;\n\nuse crate::AuthError;\n\n/// Session information\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Session {\n    pub id: Uuid,\n    pub user_id: Uuid,\n    pub session_token: String,\n    pub ip_address: String,\n    pub user_agent: String,\n    pub is_active: bool,\n    pub expires_at: DateTime<Utc>,\n    pub last_activity: DateTime<Utc>,\n    pub created_at: DateTime<Utc>,\n}\n\nimpl Session {\n    /// Create new session\n    pub fn new(\n        user_id: Uuid,\n        ip_address: String,\n        user_agent: String,\n        session_timeout_hours: i64,\n    ) -> Self {\n        let now = Utc::now();\n        let expires_at = now + Duration::hours(session_timeout_hours);\n        \n        Self {\n            id: Uuid::new_v4(),\n            user_id,\n            session_token: Self::generate_session_token(),\n            ip_address,\n            user_agent,\n            is_active: true,\n            expires_at,\n            last_activity: now,\n            created_at: now,\n        }\n    }\n\n    /// Generate secure session token\n    fn generate_session_token() -> String {\n        use base64::{engine::general_purpose, Engine as _};\n        use rand::RngCore;\n        \n        let mut bytes = [0u8; 32];\n        rand::thread_rng().fill_bytes(&mut bytes);\n        general_purpose::URL_SAFE_NO_PAD.encode(bytes)\n    }\n\n    /// Check if session is expired\n    pub fn is_expired(&self) -> bool {\n        Utc::now() > self.expires_at\n    }\n\n    /// Check if session is valid (active and not expired)\n    pub fn is_valid(&self) -> bool {\n        self.is_active && !self.is_expired()\n    }\n\n    /// Update last activity timestamp\n    pub fn update_activity(&mut self) {\n        self.last_activity = Utc::now();\n    }\n\n    /// Deactivate session\n    pub fn deactivate(&mut self) {\n        self.is_active = false;\n    }\n\n    /// Extend session expiration\n    pub fn extend_expiration(&mut self, hours: i64) {\n        self.expires_at = Utc::now() + Duration::hours(hours);\n        self.update_activity();\n    }\n}\n\n/// Session management service\n#[derive(Clone)]\npub struct SessionService {\n    default_timeout_hours: i64,\n    max_sessions_per_user: u32,\n}\n\nimpl SessionService {\n    /// Create new session service\n    pub fn new(default_timeout_hours: i64, max_sessions_per_user: u32) -> Self {\n        Self {\n            default_timeout_hours,\n            max_sessions_per_user,\n        }\n    }\n\n    /// Create new session for user\n    pub fn create_session(\n        &self,\n        user_id: Uuid,\n        ip_address: String,\n        user_agent: String,\n    ) -> Session {\n        Session::new(user_id, ip_address, user_agent, self.default_timeout_hours)\n    }\n\n    /// Validate session token format\n    pub fn validate_session_token(&self, token: &str) -> Result<(), AuthError> {\n        if token.is_empty() {\n            return Err(AuthError::InvalidToken);\n        }\n        \n        // Basic validation - should be base64 encoded\n        use base64::{engine::general_purpose, Engine as _};\n        general_purpose::URL_SAFE_NO_PAD\n            .decode(token)\n            .map_err(|_| AuthError::InvalidToken)?;\n        \n        Ok(())\n    }\n\n    /// Check if user has too many active sessions\n    pub fn check_session_limit(&self, active_session_count: u32) -> Result<(), AuthError> {\n        if active_session_count >= self.max_sessions_per_user {\n            return Err(AuthError::InvalidCredentials);\n        }\n        Ok(())\n    }\n\n    /// Generate session cleanup criteria\n    pub fn get_cleanup_criteria(&self) -> DateTime<Utc> {\n        Utc::now() - Duration::hours(24) // Clean up sessions older than 24 hours\n    }\n}\n\nimpl Default for SessionService {\n    fn default() -> Self {\n        Self::new(8, 5) // 8 hours timeout, max 5 sessions per user\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_session_creation() {\n        let user_id = Uuid::new_v4();\n        let session = Session::new(\n            user_id,\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        assert_eq!(session.user_id, user_id);\n        assert_eq!(session.ip_address, \"***********\");\n        assert_eq!(session.user_agent, \"Mozilla/5.0\");\n        assert!(session.is_active);\n        assert!(!session.is_expired());\n        assert!(session.is_valid());\n    }\n\n    #[test]\n    fn test_session_token_generation() {\n        let session1 = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        let session2 = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        // Tokens should be different\n        assert_ne!(session1.session_token, session2.session_token);\n        \n        // Tokens should be valid base64\n        use base64::{engine::general_purpose, Engine as _};\n        assert!(general_purpose::URL_SAFE_NO_PAD.decode(&session1.session_token).is_ok());\n        assert!(general_purpose::URL_SAFE_NO_PAD.decode(&session2.session_token).is_ok());\n    }\n\n    #[test]\n    fn test_session_expiration() {\n        let mut session = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            0, // Expires immediately\n        );\n        \n        // Should be expired\n        assert!(session.is_expired());\n        assert!(!session.is_valid());\n        \n        // Extend expiration\n        session.extend_expiration(1);\n        assert!(!session.is_expired());\n        assert!(session.is_valid());\n    }\n\n    #[test]\n    fn test_session_deactivation() {\n        let mut session = Session::new(\n            Uuid::new_v4(),\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n            8,\n        );\n        \n        assert!(session.is_valid());\n        \n        session.deactivate();\n        assert!(!session.is_valid());\n    }\n\n    #[test]\n    fn test_session_service() {\n        let service = SessionService::new(8, 5);\n        let user_id = Uuid::new_v4();\n        \n        let session = service.create_session(\n            user_id,\n            \"***********\".to_string(),\n            \"Mozilla/5.0\".to_string(),\n        );\n        \n        assert_eq!(session.user_id, user_id);\n        assert!(session.is_valid());\n        \n        // Test session limit\n        assert!(service.check_session_limit(4).is_ok());\n        assert!(service.check_session_limit(5).is_err());\n        \n        // Test token validation\n        assert!(service.validate_session_token(&session.session_token).is_ok());\n        assert!(service.validate_session_token(\"\").is_err());\n        assert!(service.validate_session_token(\"invalid-token\").is_err());\n    }\n}\n\n/// Redis-based session manager for distributed session storage\n#[derive(Debug, Clone)]\npub struct RedisSessionManager {\n    client: Client,\n    default_ttl: Duration,\n    max_sessions_per_user: usize,\n}\n\nimpl RedisSessionManager {\n    /// Create a new Redis session manager\n    pub fn new(redis_url: &str, default_ttl_hours: i64, max_sessions_per_user: usize) -> Result<Self> {\n        let client = Client::open(redis_url)\n            .context(\"Failed to create Redis client\")?;\n\n        let default_ttl = Duration::hours(default_ttl_hours);\n\n        Ok(Self {\n            client,\n            default_ttl,\n            max_sessions_per_user,\n        })\n    }\n\n    /// Store session in Redis\n    pub async fn store_session(&self, session: &Session) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session.session_token);\n        let user_sessions_key = format!(\"user_sessions:{}\", session.user_id);\n        let session_json = serde_json::to_string(session)\n            .context(\"Failed to serialize session data\")?;\n\n        // Clean up old sessions for this user\n        self.cleanup_user_sessions(session.user_id).await?;\n\n        // Store session data with TTL\n        let ttl_seconds = (session.expires_at - Utc::now()).num_seconds() as i64;\n        conn.set_ex(&session_key, &session_json, ttl_seconds).await\n            .context(\"Failed to store session data\")?;\n\n        // Add to user's session list\n        conn.sadd::<_, _, ()>(&user_sessions_key, &session.session_token).await\n            .context(\"Failed to add session to user list\")?;\n        conn.expire::<_, ()>(&user_sessions_key, ttl_seconds).await\n            .context(\"Failed to set TTL on user sessions\")?;\n\n        Ok(())\n    }\n\n    /// Get session from Redis\n    pub async fn get_session(&self, session_token: &str) -> Result<Option<Session>> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session_token);\n        let session_json: Option<String> = conn.get(&session_key).await\n            .context(\"Failed to get session data\")?;\n\n        match session_json {\n            Some(json) => {\n                let mut session: Session = serde_json::from_str(&json)\n                    .context(\"Failed to deserialize session data\")?;\n\n                // Check if session is expired\n                if session.expires_at < Utc::now() {\n                    self.delete_session(session_token).await?;\n                    return Ok(None);\n                }\n\n                // Update last activity\n                session.last_activity = Utc::now();\n                let updated_json = serde_json::to_string(&session)\n                    .context(\"Failed to serialize updated session data\")?;\n\n                let ttl_seconds = (session.expires_at - Utc::now()).num_seconds() as usize;\n                conn.setex(&session_key, ttl_seconds, &updated_json).await\n                    .context(\"Failed to update session data\")?;\n\n                Ok(Some(session))\n            }\n            None => Ok(None),\n        }\n    }\n\n    /// Delete session from Redis\n    pub async fn delete_session(&self, session_token: &str) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let session_key = format!(\"session:{}\", session_token);\n\n        // Get session data to find user ID\n        if let Some(session) = self.get_session(session_token).await? {\n            let user_sessions_key = format!(\"user_sessions:{}\", session.user_id);\n            conn.srem(&user_sessions_key, session_token).await\n                .context(\"Failed to remove session from user list\")?;\n        }\n\n        conn.del(&session_key).await\n            .context(\"Failed to delete session\")?;\n\n        Ok(())\n    }\n\n    /// Delete all sessions for a user\n    pub async fn delete_user_sessions(&self, user_id: Uuid) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        for session_token in session_tokens {\n            let session_key = format!(\"session:{}\", session_token);\n            conn.del(&session_key).await\n                .context(\"Failed to delete session\")?;\n        }\n\n        conn.del(&user_sessions_key).await\n            .context(\"Failed to delete user sessions list\")?;\n\n        Ok(())\n    }\n\n    /// Clean up old sessions for a user (keep only the most recent ones)\n    async fn cleanup_user_sessions(&self, user_id: Uuid) -> Result<()> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        if session_tokens.len() >= self.max_sessions_per_user {\n            // Get session data for all sessions to sort by creation time\n            let mut sessions_with_data = Vec::new();\n            for session_token in session_tokens {\n                if let Some(session) = self.get_session(&session_token).await? {\n                    sessions_with_data.push((session_token, session));\n                }\n            }\n\n            // Sort by creation time (oldest first)\n            sessions_with_data.sort_by(|a, b| a.1.created_at.cmp(&b.1.created_at));\n\n            // Delete oldest sessions to make room\n            let sessions_to_delete = sessions_with_data.len().saturating_sub(self.max_sessions_per_user - 1);\n            for (session_token, _) in sessions_with_data.into_iter().take(sessions_to_delete) {\n                self.delete_session(&session_token).await?;\n            }\n        }\n\n        Ok(())\n    }\n\n    /// Get all active sessions for a user\n    pub async fn get_user_sessions(&self, user_id: Uuid) -> Result<Vec<Session>> {\n        let mut conn = self.client.get_async_connection().await\n            .context(\"Failed to get Redis connection\")?;\n\n        let user_sessions_key = format!(\"user_sessions:{}\", user_id);\n        let session_tokens: Vec<String> = conn.smembers(&user_sessions_key).await\n            .context(\"Failed to get user sessions\")?;\n\n        let mut sessions = Vec::new();\n        for session_token in session_tokens {\n            if let Some(session) = self.get_session(&session_token).await? {\n                sessions.push(session);\n            }\n        }\n\n        Ok(sessions)\n    }\n}\n"}