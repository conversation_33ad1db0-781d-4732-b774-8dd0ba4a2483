{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/lib.rs"}, "originalCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::create_routes;\npub use middleware::{cors_layer, logging_layer, auth_middleware};\npub use handlers::{ApiError, ApiResponse, json_response};\npub use state::AppState;\n\nuse axum::Router;\nuse tower::ServiceBuilder;\nuse tower_http::{limit::RequestBodyLimitLayer, trace::TraceLayer};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(TraceLayer::new_for_http())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n\n", "modifiedCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::create_routes;\npub use middleware::{cors_layer, auth_middleware};\npub use handlers::{ApiError, ApiResponse, json_response};\npub use state::AppState;\n\nuse axum::Router;\nuse tower::ServiceBuilder;\nuse tower_http::{limit::RequestBodyLimitLayer, trace::TraceLayer};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(TraceLayer::new_for_http())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n\n"}