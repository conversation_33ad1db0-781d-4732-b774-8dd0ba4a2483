{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "benches/performance_benchmarks.rs"}, "modifiedCode": "use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};\nuse std::time::Duration;\nuse tokio::runtime::Runtime;\nuse uuid::Uuid;\nuse std::collections::HashMap;\n\n// Import the modules we want to benchmark\nuse auth::{AuthService, password::PasswordService};\nuse database::entities::users::UserRole;\nuse api::services::financial_calculator::{FinancialCalculator, FinancialCalculationRequest, FinancialCalculationType};\n\nfn benchmark_password_hashing(c: &mut Criterion) {\n    let password_service = PasswordService::new();\n    \n    c.bench_function(\"password_hash\", |b| {\n        b.iter(|| {\n            let password = black_box(\"test_password_123\");\n            password_service.hash_password(password).unwrap()\n        })\n    });\n    \n    // Benchmark password verification\n    let password = \"test_password_123\";\n    let hash = password_service.hash_password(password).unwrap();\n    \n    c.bench_function(\"password_verify\", |b| {\n        b.iter(|| {\n            let password = black_box(\"test_password_123\");\n            let hash = black_box(&hash);\n            password_service.verify_password(password, hash).unwrap()\n        })\n    });\n}\n\nfn benchmark_jwt_operations(c: &mut Criterion) {\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\");\n    let user_id = Uuid::new_v4();\n    let org_id = Some(Uuid::new_v4());\n    let role = UserRole::Analyst;\n    \n    c.bench_function(\"jwt_generate\", |b| {\n        b.iter(|| {\n            let user_id = black_box(user_id);\n            let org_id = black_box(org_id);\n            let role = black_box(role.clone());\n            auth_service.generate_token(user_id, org_id, role).unwrap()\n        })\n    });\n    \n    // Generate a token for validation benchmarks\n    let token = auth_service.generate_token(user_id, org_id, role).unwrap();\n    \n    c.bench_function(\"jwt_validate\", |b| {\n        b.iter(|| {\n            let token = black_box(&token);\n            auth_service.validate_token(token).unwrap()\n        })\n    });\n}\n\nfn benchmark_financial_calculations(c: &mut Criterion) {\n    let rt = Runtime::new().unwrap();\n    let calculator = FinancialCalculator::new();\n    \n    // Create test input data\n    let mut input_data = HashMap::new();\n    input_data.insert(\"land_area\".to_string(), serde_json::json!(1000.0));\n    input_data.insert(\"land_price_per_sqm\".to_string(), serde_json::json!(50000000.0));\n    input_data.insert(\"construction_cost\".to_string(), serde_json::json!(20000000000.0));\n    input_data.insert(\"selling_price_per_sqm\".to_string(), serde_json::json!(80000000.0));\n    input_data.insert(\"total_sellable_area\".to_string(), serde_json::json!(800.0));\n    input_data.insert(\"project_duration_months\".to_string(), serde_json::json!(24));\n    input_data.insert(\"discount_rate\".to_string(), serde_json::json!(0.12));\n    \n    let request = FinancialCalculationRequest {\n        calculation_type: FinancialCalculationType::RoiAnalysis,\n        input_data: input_data.clone(),\n        options: None,\n    };\n    \n    c.bench_function(\"financial_roi_calculation\", |b| {\n        b.to_async(&rt).iter(|| async {\n            let request = black_box(&request);\n            calculator.calculate_financial_metrics(request).await.unwrap()\n        })\n    });\n    \n    // Benchmark different calculation types\n    let calculation_types = vec![\n        FinancialCalculationType::TaxCalculation,\n        FinancialCalculationType::RoiAnalysis,\n        FinancialCalculationType::InvestmentAnalysis,\n        FinancialCalculationType::CashFlowProjection,\n        FinancialCalculationType::BreakEvenAnalysis,\n        FinancialCalculationType::ProfitabilityAnalysis,\n    ];\n    \n    let mut group = c.benchmark_group(\"financial_calculations\");\n    for calc_type in calculation_types {\n        let request = FinancialCalculationRequest {\n            calculation_type: calc_type.clone(),\n            input_data: input_data.clone(),\n            options: None,\n        };\n        \n        group.bench_with_input(\n            BenchmarkId::new(\"calculation_type\", format!(\"{:?}\", calc_type)),\n            &request,\n            |b, request| {\n                b.to_async(&rt).iter(|| async {\n                    let request = black_box(request);\n                    calculator.calculate_financial_metrics(request).await.unwrap()\n                })\n            },\n        );\n    }\n    group.finish();\n}\n\nfn benchmark_data_processing(c: &mut Criterion) {\n    use serde_json::{json, Value};\n    \n    // Benchmark JSON serialization/deserialization\n    let test_data = json!({\n        \"project_id\": Uuid::new_v4(),\n        \"name\": \"Test Project\",\n        \"data\": {\n            \"financial_metrics\": {\n                \"revenue\": 1000000.0,\n                \"costs\": 750000.0,\n                \"profit\": 250000.0,\n                \"roi\": 0.33\n            },\n            \"timeline\": {\n                \"start_date\": \"2025-01-01\",\n                \"end_date\": \"2025-12-31\",\n                \"milestones\": [\n                    {\"name\": \"Phase 1\", \"date\": \"2025-03-31\"},\n                    {\"name\": \"Phase 2\", \"date\": \"2025-06-30\"},\n                    {\"name\": \"Phase 3\", \"date\": \"2025-09-30\"},\n                    {\"name\": \"Completion\", \"date\": \"2025-12-31\"}\n                ]\n            }\n        }\n    });\n    \n    c.bench_function(\"json_serialize\", |b| {\n        b.iter(|| {\n            let data = black_box(&test_data);\n            serde_json::to_string(data).unwrap()\n        })\n    });\n    \n    let json_string = serde_json::to_string(&test_data).unwrap();\n    \n    c.bench_function(\"json_deserialize\", |b| {\n        b.iter(|| {\n            let json_str = black_box(&json_string);\n            serde_json::from_str::<Value>(json_str).unwrap()\n        })\n    });\n}\n\nfn benchmark_uuid_operations(c: &mut Criterion) {\n    c.bench_function(\"uuid_generate\", |b| {\n        b.iter(|| {\n            black_box(Uuid::new_v4())\n        })\n    });\n    \n    let uuid = Uuid::new_v4();\n    let uuid_string = uuid.to_string();\n    \n    c.bench_function(\"uuid_to_string\", |b| {\n        b.iter(|| {\n            let uuid = black_box(uuid);\n            uuid.to_string()\n        })\n    });\n    \n    c.bench_function(\"uuid_from_string\", |b| {\n        b.iter(|| {\n            let uuid_str = black_box(&uuid_string);\n            Uuid::parse_str(uuid_str).unwrap()\n        })\n    });\n}\n\nfn benchmark_concurrent_operations(c: &mut Criterion) {\n    let rt = Runtime::new().unwrap();\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\");\n    \n    // Benchmark concurrent JWT generation\n    c.bench_function(\"concurrent_jwt_generation\", |b| {\n        b.to_async(&rt).iter(|| async {\n            let auth_service = black_box(&auth_service);\n            let tasks: Vec<_> = (0..10).map(|_| {\n                let user_id = Uuid::new_v4();\n                let org_id = Some(Uuid::new_v4());\n                let role = UserRole::Analyst;\n                tokio::spawn(async move {\n                    auth_service.generate_token(user_id, org_id, role).unwrap()\n                })\n            }).collect();\n            \n            for task in tasks {\n                task.await.unwrap();\n            }\n        })\n    });\n}\n\nfn benchmark_memory_operations(c: &mut Criterion) {\n    // Benchmark vector operations that might be used in data processing\n    c.bench_function(\"vector_allocation_1000\", |b| {\n        b.iter(|| {\n            let size = black_box(1000);\n            let mut vec: Vec<u64> = Vec::with_capacity(size);\n            for i in 0..size {\n                vec.push(i as u64);\n            }\n            vec\n        })\n    });\n    \n    c.bench_function(\"hashmap_operations_1000\", |b| {\n        b.iter(|| {\n            let size = black_box(1000);\n            let mut map: HashMap<String, u64> = HashMap::with_capacity(size);\n            for i in 0..size {\n                map.insert(format!(\"key_{}\", i), i as u64);\n            }\n            \n            // Perform some lookups\n            for i in 0..100 {\n                let _ = map.get(&format!(\"key_{}\", i));\n            }\n            map\n        })\n    });\n}\n\n// Configure benchmark groups\ncriterion_group!(\n    name = benches;\n    config = Criterion::default()\n        .measurement_time(Duration::from_secs(10))\n        .sample_size(100);\n    targets = \n        benchmark_password_hashing,\n        benchmark_jwt_operations,\n        benchmark_financial_calculations,\n        benchmark_data_processing,\n        benchmark_uuid_operations,\n        benchmark_concurrent_operations,\n        benchmark_memory_operations\n);\n\ncriterion_main!(benches);\n"}