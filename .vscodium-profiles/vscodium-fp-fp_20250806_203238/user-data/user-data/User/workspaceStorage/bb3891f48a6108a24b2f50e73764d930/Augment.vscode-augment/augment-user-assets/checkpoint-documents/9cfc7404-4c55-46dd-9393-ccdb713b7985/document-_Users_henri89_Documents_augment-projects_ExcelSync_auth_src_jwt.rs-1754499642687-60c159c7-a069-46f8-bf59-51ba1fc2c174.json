{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/jwt.rs"}, "modifiedCode": "use anyhow::Result;\nuse chrono::{Duration, Utc};\nuse jsonwebtoken::{decode, encode, Algorithm, Decod<PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation};\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\nuse crate::{AuthError, Claims};\n\n/// JWT service for token generation and validation\n#[derive(Clone)]\npub struct JwtService {\n    encoding_key: EncodingKey,\n    decoding_key: Decoding<PERSON><PERSON>,\n    validation: Validation,\n}\n\nimpl JwtService {\n    /// Create new JWT service with secret key\n    pub fn new(secret: String) -> Self {\n        let encoding_key = EncodingKey::from_secret(secret.as_bytes());\n        let decoding_key = DecodingKey::from_secret(secret.as_bytes());\n        \n        let mut validation = Validation::new(Algorithm::HS256);\n        validation.validate_exp = true;\n        validation.validate_nbf = false;\n        validation.validate_aud = false;\n        \n        Self {\n            encoding_key,\n            decoding_key,\n            validation,\n        }\n    }\n\n    /// Generate JWT token for user\n    pub fn generate_token(\n        &self,\n        user_id: Uuid,\n        email: String,\n        role: String,\n        org_id: Option<Uuid>,\n    ) -> Result<String, AuthError> {\n        let now = Utc::now();\n        let exp = now + Duration::hours(8); // 8 hour expiration\n        \n        let claims = Claims {\n            sub: user_id.to_string(),\n            email,\n            role,\n            org_id: org_id.map(|id| id.to_string()),\n            exp: exp.timestamp(),\n            iat: now.timestamp(),\n            jti: Uuid::new_v4().to_string(), // JWT ID for revocation\n        };\n\n        encode(&Header::default(), &claims, &self.encoding_key)\n            .map_err(|e| AuthError::JwtError(format!(\"Token generation failed: {}\", e)))\n    }\n\n    /// Validate JWT token and extract claims\n    pub fn validate_token(&self, token: &str) -> Result<Claims, AuthError> {\n        decode::<Claims>(token, &self.decoding_key, &self.validation)\n            .map(|token_data| token_data.claims)\n            .map_err(|e| match e.kind() {\n                jsonwebtoken::errors::ErrorKind::ExpiredSignature => {\n                    AuthError::InvalidToken\n                }\n                jsonwebtoken::errors::ErrorKind::InvalidToken => {\n                    AuthError::InvalidToken\n                }\n                _ => AuthError::JwtError(format!(\"Token validation failed: {}\", e)),\n            })\n    }\n\n    /// Check if token is expired\n    pub fn is_token_expired(&self, token: &str) -> bool {\n        match self.validate_token(token) {\n            Ok(_) => false,\n            Err(AuthError::InvalidToken) => true,\n            Err(_) => true,\n        }\n    }\n\n    /// Extract user ID from token without full validation (for logging/debugging)\n    pub fn extract_user_id(&self, token: &str) -> Option<Uuid> {\n        decode::<Claims>(\n            token,\n            &self.decoding_key,\n            &Validation::new(Algorithm::HS256),\n        )\n        .ok()\n        .and_then(|token_data| Uuid::parse_str(&token_data.claims.sub).ok())\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    fn create_test_service() -> JwtService {\n        JwtService::new(\"test-secret-key-32-characters-long\".to_string())\n    }\n\n    #[test]\n    fn test_token_generation_and_validation() {\n        let service = create_test_service();\n        let user_id = Uuid::new_v4();\n        \n        let token = service\n            .generate_token(\n                user_id,\n                \"<EMAIL>\".to_string(),\n                \"admin\".to_string(),\n                None,\n            )\n            .unwrap();\n        \n        let claims = service.validate_token(&token).unwrap();\n        assert_eq!(claims.email, \"<EMAIL>\");\n        assert_eq!(claims.role, \"admin\");\n        assert_eq!(claims.sub, user_id.to_string());\n    }\n\n    #[test]\n    fn test_invalid_token() {\n        let service = create_test_service();\n        let result = service.validate_token(\"invalid.token.here\");\n        assert!(result.is_err());\n    }\n\n    #[test]\n    fn test_extract_user_id() {\n        let service = create_test_service();\n        let user_id = Uuid::new_v4();\n        \n        let token = service\n            .generate_token(\n                user_id,\n                \"<EMAIL>\".to_string(),\n                \"admin\".to_string(),\n                None,\n            )\n            .unwrap();\n        \n        let extracted_id = service.extract_user_id(&token).unwrap();\n        assert_eq!(extracted_id, user_id);\n    }\n\n    #[test]\n    fn test_token_with_organization() {\n        let service = create_test_service();\n        let user_id = Uuid::new_v4();\n        let org_id = Uuid::new_v4();\n        \n        let token = service\n            .generate_token(\n                user_id,\n                \"<EMAIL>\".to_string(),\n                \"admin\".to_string(),\n                Some(org_id),\n            )\n            .unwrap();\n        \n        let claims = service.validate_token(&token).unwrap();\n        assert_eq!(claims.org_id, Some(org_id.to_string()));\n    }\n}\n"}