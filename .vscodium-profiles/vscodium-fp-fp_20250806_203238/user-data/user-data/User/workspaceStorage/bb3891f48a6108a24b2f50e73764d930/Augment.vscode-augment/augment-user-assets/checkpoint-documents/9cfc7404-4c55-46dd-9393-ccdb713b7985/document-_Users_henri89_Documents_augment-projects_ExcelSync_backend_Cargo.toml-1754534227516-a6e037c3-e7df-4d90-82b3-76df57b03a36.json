{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/Cargo.toml"}, "originalCode": "[package]\nname = \"backend\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Backend - Rust API server for Excel add-in integration\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\naxum = { workspace = true }\ntokio = { workspace = true }\ntower = { workspace = true }\ntower-http = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\ntracing = { workspace = true }\ntracing-subscriber = { workspace = true }\nconfig = { workspace = true }\ndotenvy = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nchrono = { workspace = true }\n\n# Local workspace crates\nauth = { path = \"../auth\" }\ndatabase = { path = \"../database\" }\napi = { path = \"../api\" }\ncore = { path = \"../core\" }\n\n[[bin]]\nname = \"backend\"\npath = \"src/main.rs\"\n\n[[bin]]\nname = \"data_fetcher\"\npath = \"src/bin/data_fetcher.rs\"\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n", "modifiedCode": "[package]\nname = \"backend\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Backend - Rust API server for Excel add-in integration\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\naxum = { workspace = true }\ntokio = { workspace = true }\ntower = { workspace = true }\ntower-http = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\ntracing = { workspace = true }\ntracing-subscriber = { workspace = true }\nconfig = { workspace = true }\ndotenvy = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nchrono = { workspace = true }\n\n# Local workspace crates\nauth = { path = \"../auth\" }\ndatabase = { path = \"../database\" }\ncore = { path = \"../core\" }\n\n[[bin]]\nname = \"backend\"\npath = \"src/main.rs\"\n\n[[bin]]\nname = \"data_fetcher\"\npath = \"src/bin/data_fetcher.rs\"\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n"}