{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/versioning.rs"}, "originalCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    response::Json,\n    routing::{get, post},\n    Router,\n};\nuse serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse uuid::Uuid;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::versioning_service::{VersioningService, VersioningError},\n    AppState,\n};\nuse database::entities::data_versions::{MergeStrategy, VersionComparison};\n\n/// Query parameters for version history\n#[derive(Debug, Deserialize)]\npub struct VersionHistoryQuery {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n}\n\n/// Request to create a new version\n#[derive(Debug, Deserialize)]\npub struct CreateVersionRequest {\n    pub entity_type: String,\n    pub entity_id: Uuid,\n    pub data: Value,\n    pub change_summary: String,\n    pub parent_version_id: Option<Uuid>,\n}\n\n/// Request to merge versions\n#[derive(Debug, Deserialize)]\npub struct MergeVersionsRequest {\n    pub base_version_id: Uuid,\n    pub source_version_id: Uuid,\n    pub target_version_id: Uuid,\n    pub merge_strategy: MergeStrategy,\n    pub manual_resolutions: Option<Value>,\n}\n\n/// Response for version operations\n#[derive(Debug, Serialize)]\npub struct VersionResponse {\n    pub version_id: Uuid,\n    pub version_number: i32,\n    pub entity_type: String,\n    pub entity_id: Uuid,\n    pub data_snapshot: Value,\n    pub change_summary: String,\n    pub created_by: Uuid,\n    pub created_at: String,\n    pub is_current: bool,\n    pub is_merged: bool,\n}\n\n/// Response for version history\n#[derive(Debug, Serialize)]\npub struct VersionHistoryResponse {\n    pub versions: Vec<VersionResponse>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n}\n\n/// Response for version comparison\n#[derive(Debug, Serialize)]\npub struct ComparisonResponse {\n    pub comparison: VersionComparison,\n}\n\n/// Versioning routes\npub fn versioning_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/projects/:project_id/versions\", post(create_version))\n        .route(\"/projects/:project_id/entities/:entity_type/:entity_id/versions\", get(get_version_history))\n        .route(\"/projects/:project_id/entities/:entity_type/:entity_id/current\", get(get_current_version))\n        .route(\"/versions/:version_id\", get(get_version))\n        .route(\"/versions/compare/:from_version/:to_version\", get(compare_versions))\n        .route(\"/versions/merge\", post(merge_versions))\n        .route(\"/versions/:version_id/revert\", post(revert_version))\n}\n\n/// Create a new version endpoint\npub async fn create_version(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    // Extract JSON body\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateVersionRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let version_id = versioning_service\n        .create_version(\n            project_id,\n            &create_request.entity_type,\n            create_request.entity_id,\n            &create_request.data,\n            &create_request.change_summary,\n            user_id,\n            create_request.parent_version_id,\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Version created successfully\", json!({ \"version_id\": version_id })))\n}\n\n/// Get version history endpoint\npub async fn get_version_history(\n    State(state): State<AppState>,\n    Path((project_id, entity_type, entity_id)): Path<(Uuid, String, Uuid)>,\n    Query(query): Query<VersionHistoryQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let (versions, total) = versioning_service\n        .get_version_history(\n            project_id,\n            &entity_type,\n            entity_id,\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    let version_responses: Vec<VersionResponse> = versions\n        .into_iter()\n        .map(|v| VersionResponse {\n            version_id: v.id,\n            version_number: v.version_number,\n            entity_type: v.entity_type,\n            entity_id: v.entity_id,\n            data_snapshot: v.data_snapshot,\n            change_summary: v.change_summary,\n            created_by: v.created_by,\n            created_at: v.created_at.to_rfc3339(),\n            is_current: v.is_current,\n            is_merged: v.is_merged,\n        })\n        .collect();\n\n    let response = VersionHistoryResponse {\n        versions: version_responses,\n        total,\n        page: query.page.unwrap_or(1),\n        per_page: query.per_page.unwrap_or(20),\n    };\n\n    Ok(json_response(\"Version history retrieved successfully\", response))\n}\n\n/// Get current version endpoint\npub async fn get_current_version(\n    State(state): State<AppState>,\n    Path((project_id, entity_type, entity_id)): Path<(Uuid, String, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let version = versioning_service\n        .get_current_version(project_id, &entity_type, entity_id)\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    match version {\n        Some(v) => {\n            let response = VersionResponse {\n                version_id: v.id,\n                version_number: v.version_number,\n                entity_type: v.entity_type,\n                entity_id: v.entity_id,\n                data_snapshot: v.data_snapshot,\n                change_summary: v.change_summary,\n                created_by: v.created_by,\n                created_at: v.created_at.to_rfc3339(),\n                is_current: v.is_current,\n                is_merged: v.is_merged,\n            };\n            Ok(json_response(\"Current version retrieved successfully\", response))\n        }\n        None => Err(ApiError::NotFound(\"No current version found\".to_string())),\n    }\n}\n\n/// Get specific version endpoint\npub async fn get_version(\n    State(state): State<AppState>,\n    Path(version_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    // This would need to be implemented in the service\n    // For now, return a placeholder error\n    Err(ApiError::NotImplemented(\"Get specific version not yet implemented\".to_string()))\n}\n\n/// Compare versions endpoint\npub async fn compare_versions(\n    State(state): State<AppState>,\n    Path((from_version, to_version)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let comparison = versioning_service\n        .compare_versions(from_version, to_version)\n        .await\n        .map_err(|e| match e {\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    let response = ComparisonResponse { comparison };\n    Ok(json_response(\"Version comparison completed successfully\", response))\n}\n\n/// Merge versions endpoint\npub async fn merge_versions(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    // Extract JSON body\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let merge_request: MergeVersionsRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let merged_version_id = versioning_service\n        .merge_versions(\n            merge_request.base_version_id,\n            merge_request.source_version_id,\n            merge_request.target_version_id,\n            merge_request.merge_strategy,\n            merge_request.manual_resolutions,\n            user_id,\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::ConflictDetected(msg) => ApiError::Conflict(msg),\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Versions merged successfully\", json!({ \"merged_version_id\": merged_version_id })))\n}\n\n/// Revert to version endpoint\npub async fn revert_version(\n    State(state): State<AppState>,\n    Path(version_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let new_version_id = versioning_service\n        .revert_to_version(version_id, user_id)\n        .await\n        .map_err(|e| match e {\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Version reverted successfully\", json!({ \"new_version_id\": new_version_id })))\n}\n", "modifiedCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    response::Json,\n    routing::{get, post},\n    Router,\n};\nuse serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse uuid::Uuid;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::versioning_service::{VersioningService, VersioningError},\n    AppState,\n};\nuse auth;\nuse database::entities::data_versions::{MergeStrategy, VersionComparison};\n\n/// Query parameters for version history\n#[derive(Debug, Deserialize)]\npub struct VersionHistoryQuery {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n}\n\n/// Request to create a new version\n#[derive(Debug, Deserialize)]\npub struct CreateVersionRequest {\n    pub entity_type: String,\n    pub entity_id: Uuid,\n    pub data: Value,\n    pub change_summary: String,\n    pub parent_version_id: Option<Uuid>,\n}\n\n/// Request to merge versions\n#[derive(Debug, Deserialize)]\npub struct MergeVersionsRequest {\n    pub base_version_id: Uuid,\n    pub source_version_id: Uuid,\n    pub target_version_id: Uuid,\n    pub merge_strategy: MergeStrategy,\n    pub manual_resolutions: Option<Value>,\n}\n\n/// Response for version operations\n#[derive(Debug, Serialize)]\npub struct VersionResponse {\n    pub version_id: Uuid,\n    pub version_number: i32,\n    pub entity_type: String,\n    pub entity_id: Uuid,\n    pub data_snapshot: Value,\n    pub change_summary: String,\n    pub created_by: Uuid,\n    pub created_at: String,\n    pub is_current: bool,\n    pub is_merged: bool,\n}\n\n/// Response for version history\n#[derive(Debug, Serialize)]\npub struct VersionHistoryResponse {\n    pub versions: Vec<VersionResponse>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n}\n\n/// Response for version comparison\n#[derive(Debug, Serialize)]\npub struct ComparisonResponse {\n    pub comparison: VersionComparison,\n}\n\n/// Versioning routes\npub fn versioning_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/projects/:project_id/versions\", post(create_version))\n        .route(\"/projects/:project_id/entities/:entity_type/:entity_id/versions\", get(get_version_history))\n        .route(\"/projects/:project_id/entities/:entity_type/:entity_id/current\", get(get_current_version))\n        .route(\"/versions/:version_id\", get(get_version))\n        .route(\"/versions/compare/:from_version/:to_version\", get(compare_versions))\n        .route(\"/versions/merge\", post(merge_versions))\n        .route(\"/versions/:version_id/revert\", post(revert_version))\n}\n\n/// Create a new version endpoint\npub async fn create_version(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    // Extract JSON body\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateVersionRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let version_id = versioning_service\n        .create_version(\n            project_id,\n            &create_request.entity_type,\n            create_request.entity_id,\n            &create_request.data,\n            &create_request.change_summary,\n            user_id,\n            create_request.parent_version_id,\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Version created successfully\", json!({ \"version_id\": version_id })))\n}\n\n/// Get version history endpoint\npub async fn get_version_history(\n    State(state): State<AppState>,\n    Path((project_id, entity_type, entity_id)): Path<(Uuid, String, Uuid)>,\n    Query(query): Query<VersionHistoryQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let (versions, total) = versioning_service\n        .get_version_history(\n            project_id,\n            &entity_type,\n            entity_id,\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    let version_responses: Vec<VersionResponse> = versions\n        .into_iter()\n        .map(|v| VersionResponse {\n            version_id: v.id,\n            version_number: v.version_number,\n            entity_type: v.entity_type,\n            entity_id: v.entity_id,\n            data_snapshot: v.data_snapshot,\n            change_summary: v.change_summary,\n            created_by: v.created_by,\n            created_at: v.created_at.to_rfc3339(),\n            is_current: v.is_current,\n            is_merged: v.is_merged,\n        })\n        .collect();\n\n    let response = VersionHistoryResponse {\n        versions: version_responses,\n        total,\n        page: query.page.unwrap_or(1),\n        per_page: query.per_page.unwrap_or(20),\n    };\n\n    Ok(json_response(\"Version history retrieved successfully\", response))\n}\n\n/// Get current version endpoint\npub async fn get_current_version(\n    State(state): State<AppState>,\n    Path((project_id, entity_type, entity_id)): Path<(Uuid, String, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let version = versioning_service\n        .get_current_version(project_id, &entity_type, entity_id)\n        .await\n        .map_err(|e| match e {\n            VersioningError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    match version {\n        Some(v) => {\n            let response = VersionResponse {\n                version_id: v.id,\n                version_number: v.version_number,\n                entity_type: v.entity_type,\n                entity_id: v.entity_id,\n                data_snapshot: v.data_snapshot,\n                change_summary: v.change_summary,\n                created_by: v.created_by,\n                created_at: v.created_at.to_rfc3339(),\n                is_current: v.is_current,\n                is_merged: v.is_merged,\n            };\n            Ok(json_response(\"Current version retrieved successfully\", response))\n        }\n        None => Err(ApiError::NotFound(\"No current version found\".to_string())),\n    }\n}\n\n/// Get specific version endpoint\npub async fn get_version(\n    State(state): State<AppState>,\n    Path(version_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    // This would need to be implemented in the service\n    // For now, return a placeholder error\n    Err(ApiError::NotImplemented(\"Get specific version not yet implemented\".to_string()))\n}\n\n/// Compare versions endpoint\npub async fn compare_versions(\n    State(state): State<AppState>,\n    Path((from_version, to_version)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let _claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let comparison = versioning_service\n        .compare_versions(from_version, to_version)\n        .await\n        .map_err(|e| match e {\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    let response = ComparisonResponse { comparison };\n    Ok(json_response(\"Version comparison completed successfully\", response))\n}\n\n/// Merge versions endpoint\npub async fn merge_versions(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    // Extract JSON body\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let merge_request: MergeVersionsRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let merged_version_id = versioning_service\n        .merge_versions(\n            merge_request.base_version_id,\n            merge_request.source_version_id,\n            merge_request.target_version_id,\n            merge_request.merge_strategy,\n            merge_request.manual_resolutions,\n            user_id,\n        )\n        .await\n        .map_err(|e| match e {\n            VersioningError::ConflictDetected(msg) => ApiError::Conflict(msg),\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Versions merged successfully\", json!({ \"merged_version_id\": merged_version_id })))\n}\n\n/// Revert to version endpoint\npub async fn revert_version(\n    State(state): State<AppState>,\n    Path(version_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let user_id = uuid::Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::Unauthorized(\"Invalid user ID in token\".to_string()))?;\n\n    let versioning_service = VersioningService::new(state.db.get_connection().clone());\n    \n    let new_version_id = versioning_service\n        .revert_to_version(version_id, user_id)\n        .await\n        .map_err(|e| match e {\n            VersioningError::VersionNotFound(msg) => ApiError::NotFound(msg),\n            VersioningError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            _ => ApiError::InternalServerError(e.to_string()),\n        })?;\n\n    Ok(json_response(\"Version reverted successfully\", json!({ \"new_version_id\": new_version_id })))\n}\n"}