{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/users.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::UserRole;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"users\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    #[sea_orm(unique)]\n    pub email: String,\n    \n    pub password_hash: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<DateTime<Utc>>,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n    \n    #[sea_orm(has_many = \"super::sessions::Entity\")]\n    Sessions,\n    \n    #[sea_orm(has_many = \"super::audit_logs::Entity\")]\n    AuditLogs,\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl Related<super::sessions::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Sessions.def()\n    }\n}\n\nimpl Related<super::audit_logs::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::AuditLogs.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::UserRole;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"users\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    #[sea_orm(unique)]\n    pub email: String,\n    \n    pub password_hash: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<DateTime<Utc>>,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n    \n    #[sea_orm(has_many = \"super::sessions::Entity\")]\n    Sessions,\n    \n    #[sea_orm(has_many = \"super::audit_logs::Entity\")]\n    AuditLogs,\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl Related<super::sessions::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Sessions.def()\n    }\n}\n\nimpl Related<super::audit_logs::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::AuditLogs.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n"}