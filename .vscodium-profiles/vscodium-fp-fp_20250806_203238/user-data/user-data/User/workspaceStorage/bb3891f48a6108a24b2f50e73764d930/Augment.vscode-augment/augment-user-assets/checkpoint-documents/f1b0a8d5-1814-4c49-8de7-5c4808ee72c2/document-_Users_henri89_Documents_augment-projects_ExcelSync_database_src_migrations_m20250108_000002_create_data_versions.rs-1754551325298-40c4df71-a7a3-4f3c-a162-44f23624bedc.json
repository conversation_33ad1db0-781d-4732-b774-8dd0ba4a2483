{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250108_000002_create_data_versions.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Create data_versions table\n        manager\n            .create_table(\n                Table::create()\n                    .table(DataVersions::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(DataVersions::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::ProjectId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::EntityType)\n                            .string()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::EntityId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::VersionNumber)\n                            .integer()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::ParentVersionId)\n                            .uuid()\n                            .null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::DataSnapshot)\n                            .json()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::Changes)\n                            .json()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::ChangeSummary)\n                            .text()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::CreatedBy)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::IsCurrent)\n                            .boolean()\n                            .not_null()\n                            .default(false),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::IsMerged)\n                            .boolean()\n                            .not_null()\n                            .default(false),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::ConflictResolutionStrategy)\n                            .string()\n                            .null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::ConflictMetadata)\n                            .json()\n                            .null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::Tags)\n                            .json()\n                            .null(),\n                    )\n                    .col(\n                        ColumnDef::new(DataVersions::Notes)\n                            .text()\n                            .null(),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_data_versions_project_id\")\n                            .from(DataVersions::Table, DataVersions::ProjectId)\n                            .to(Projects::Table, Projects::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_data_versions_created_by\")\n                            .from(DataVersions::Table, DataVersions::CreatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_data_versions_parent_version\")\n                            .from(DataVersions::Table, DataVersions::ParentVersionId)\n                            .to(DataVersions::Table, DataVersions::Id)\n                            .on_delete(ForeignKeyAction::SetNull),\n                    )\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create version_branches table for managing parallel development\n        manager\n            .create_table(\n                Table::create()\n                    .table(VersionBranches::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(VersionBranches::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::ProjectId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::BranchName)\n                            .string()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::BaseVersionId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::HeadVersionId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::CreatedBy)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(VersionBranches::IsActive)\n                            .boolean()\n                            .not_null()\n                            .default(true),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_version_branches_project_id\")\n                            .from(VersionBranches::Table, VersionBranches::ProjectId)\n                            .to(Projects::Table, Projects::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_version_branches_base_version\")\n                            .from(VersionBranches::Table, VersionBranches::BaseVersionId)\n                            .to(DataVersions::Table, DataVersions::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_version_branches_head_version\")\n                            .from(VersionBranches::Table, VersionBranches::HeadVersionId)\n                            .to(DataVersions::Table, DataVersions::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_version_branches_created_by\")\n                            .from(VersionBranches::Table, VersionBranches::CreatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create indexes for better query performance\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_data_versions_project_entity\")\n                    .table(DataVersions::Table)\n                    .col(DataVersions::ProjectId)\n                    .col(DataVersions::EntityType)\n                    .col(DataVersions::EntityId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_data_versions_current\")\n                    .table(DataVersions::Table)\n                    .col(DataVersions::ProjectId)\n                    .col(DataVersions::EntityType)\n                    .col(DataVersions::EntityId)\n                    .col(DataVersions::IsCurrent)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_data_versions_created_at\")\n                    .table(DataVersions::Table)\n                    .col(DataVersions::CreatedAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_version_branches_project_name\")\n                    .table(VersionBranches::Table)\n                    .col(VersionBranches::ProjectId)\n                    .col(VersionBranches::BranchName)\n                    .unique()\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create unique constraint for current versions\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_data_versions_unique_current\")\n                    .table(DataVersions::Table)\n                    .col(DataVersions::ProjectId)\n                    .col(DataVersions::EntityType)\n                    .col(DataVersions::EntityId)\n                    .unique()\n                    .partial_filter(Expr::col(DataVersions::IsCurrent).eq(true))\n                    .to_owned(),\n            )\n            .await?;\n\n        Ok(())\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(VersionBranches::Table).to_owned())\n            .await?;\n        \n        manager\n            .drop_table(Table::drop().table(DataVersions::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(DeriveIden)]\nenum DataVersions {\n    Table,\n    Id,\n    ProjectId,\n    EntityType,\n    EntityId,\n    VersionNumber,\n    ParentVersionId,\n    DataSnapshot,\n    Changes,\n    ChangeSummary,\n    CreatedBy,\n    CreatedAt,\n    IsCurrent,\n    IsMerged,\n    ConflictResolutionStrategy,\n    ConflictMetadata,\n    Tags,\n    Notes,\n}\n\n#[derive(DeriveIden)]\nenum VersionBranches {\n    Table,\n    Id,\n    ProjectId,\n    BranchName,\n    BaseVersionId,\n    HeadVersionId,\n    CreatedBy,\n    CreatedAt,\n    IsActive,\n}\n\n#[derive(DeriveIden)]\nenum Projects {\n    Table,\n    Id,\n}\n\n#[derive(DeriveIden)]\nenum Users {\n    Table,\n    Id,\n}\n"}