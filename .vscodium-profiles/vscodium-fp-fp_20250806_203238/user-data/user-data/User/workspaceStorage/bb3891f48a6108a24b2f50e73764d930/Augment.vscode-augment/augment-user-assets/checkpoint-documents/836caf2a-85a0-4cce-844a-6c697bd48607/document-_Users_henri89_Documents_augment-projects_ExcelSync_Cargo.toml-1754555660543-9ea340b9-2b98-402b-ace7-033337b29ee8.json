{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Cargo.toml"}, "originalCode": "[workspace]\nmembers = [\n    \"backend\",\n    \"auth\",\n    \"database\",\n    \"api\",\n    \"core\",\n]\nresolver = \"2\"\n\n[workspace.dependencies]\n# Web Framework\naxum = { version = \"0.7\", features = [\"macros\", \"ws\", \"multipart\"] }\ntokio = { version = \"1.0\", features = [\"full\"] }\ntower = \"0.4\"\ntower-http = { version = \"0.5\", features = [\"cors\", \"trace\", \"limit\", \"auth\"] }\n\n# Database\nsqlx = { version = \"0.7\", features = [\"runtime-tokio-rustls\", \"postgres\", \"chrono\", \"uuid\", \"migrate\"] }\nsea-orm = { version = \"0.12\", features = [\"sqlx-postgres\", \"runtime-tokio-rustls\", \"macros\"] }\nsea-orm-migration = \"0.12\"\nrust_decimal = { version = \"1.32\", features = [\"serde\"] }\n\n# Serialization\nserde = { version = \"1.0\", features = [\"derive\"] }\nserde_json = \"1.0\"\n\n# Authentication & Security\njsonwebtoken = \"9.0\"\nargon2 = \"0.5\"\nuuid = { version = \"1.0\", features = [\"v4\", \"serde\"] }\n\n# Validation\nvalidator = { version = \"0.16\", features = [\"derive\"] }\n\n# API Documentation\nutoipa = { version = \"4.0\", features = [\"axum_extras\", \"chrono\", \"uuid\"] }\nutoipa-swagger-ui = { version = \"4.0\", features = [\"axum\"] }\n\n# Logging & Monitoring\ntracing = \"0.1\"\ntracing-subscriber = { version = \"0.3\", features = [\"env-filter\", \"json\"] }\nlog = \"0.4\"\n\n# Configuration\nconfig = \"0.13\"\ndotenvy = \"0.15\"\n\n# Error Handling\nanyhow = \"1.0\"\nthiserror = \"1.0\"\n\n# Async Runtime\nfutures = \"0.3\"\n\n# Caching\nredis = { version = \"0.23\", features = [\"tokio-comp\", \"connection-manager\"] }\n\n# Encryption\naes-gcm = \"0.10\"\nrand = \"0.8\"\npbkdf2 = \"0.12\"\nsha2 = \"0.10\"\n\n# Time\nchrono = { version = \"0.4\", features = [\"serde\"] }\n\n# Excel Processing\ncalamine = \"0.22\"\nxlsxwriter = \"0.6\"\n\n# Additional Math and Financial\nnum-traits = \"0.2\"\nstatrs = \"0.16\"\n\n# HTTP Client\nreqwest = { version = \"0.11\", features = [\"json\"] }\n\n# Testing\nmockall = \"0.11\"\ntokio-test = \"0.4\"\n\n[profile.dev]\nopt-level = 0\ndebug = true\nsplit-debuginfo = \"unpacked\"\ndebug-assertions = true\noverflow-checks = true\nlto = false\npanic = \"unwind\"\nincremental = true\ncodegen-units = 256\nrpath = false\n\n[profile.release]\nopt-level = 3\ndebug = false\nsplit-debuginfo = \"packed\"\ndebug-assertions = false\noverflow-checks = false\nlto = true\npanic = \"abort\"\nincremental = false\ncodegen-units = 1\nrpath = false\n\n[profile.test]\nopt-level = 0\ndebug = 2\ndebug-assertions = true\noverflow-checks = true\nlto = false\npanic = \"unwind\"\nincremental = true\ncodegen-units = 256\n\n[profile.bench]\nopt-level = 3\ndebug = false\ndebug-assertions = false\noverflow-checks = false\nlto = true\npanic = \"abort\"\nincremental = false\ncodegen-units = 1\n", "modifiedCode": "[workspace]\nmembers = [\n    \"backend\",\n    \"auth\",\n    \"database\",\n    \"api\",\n    \"core\",\n]\nresolver = \"2\"\n\n[workspace.dependencies]\n# Web Framework\naxum = { version = \"0.7\", features = [\"macros\", \"ws\", \"multipart\"] }\ntokio = { version = \"1.0\", features = [\"full\"] }\ntower = \"0.4\"\ntower-http = { version = \"0.5\", features = [\"cors\", \"trace\", \"limit\", \"auth\"] }\n\n# Database\nsqlx = { version = \"0.7\", features = [\"runtime-tokio-rustls\", \"postgres\", \"chrono\", \"uuid\", \"migrate\"] }\nsea-orm = { version = \"0.12\", features = [\"sqlx-postgres\", \"runtime-tokio-rustls\", \"macros\"] }\nsea-orm-migration = \"0.12\"\nrust_decimal = { version = \"1.32\", features = [\"serde\"] }\n\n# Serialization\nserde = { version = \"1.0\", features = [\"derive\"] }\nserde_json = \"1.0\"\n\n# Authentication & Security\njsonwebtoken = \"9.0\"\nargon2 = \"0.5\"\nuuid = { version = \"1.0\", features = [\"v4\", \"serde\"] }\n\n# Validation\nvalidator = { version = \"0.16\", features = [\"derive\"] }\n\n# API Documentation\nutoipa = { version = \"4.0\", features = [\"axum_extras\", \"chrono\", \"uuid\"] }\nutoipa-swagger-ui = { version = \"4.0\", features = [\"axum\"] }\n\n# Logging & Monitoring\ntracing = \"0.1\"\ntracing-subscriber = { version = \"0.3\", features = [\"env-filter\", \"json\"] }\nlog = \"0.4\"\n\n# Configuration\nconfig = \"0.13\"\ndotenvy = \"0.15\"\n\n# Error Handling\nanyhow = \"1.0\"\nthiserror = \"1.0\"\n\n# Async Runtime\nfutures = \"0.3\"\n\n# Caching\nredis = { version = \"0.23\", features = [\"tokio-comp\", \"connection-manager\"] }\n\n# Encryption\naes-gcm = \"0.10\"\nrand = \"0.8\"\npbkdf2 = \"0.12\"\nsha2 = \"0.10\"\n\n# Time\nchrono = { version = \"0.4\", features = [\"serde\"] }\n\n# Excel Processing\ncalamine = \"0.22\"\nxlsxwriter = \"0.6\"\n\n# Additional Math and Financial\nnum-traits = \"0.2\"\nstatrs = \"0.16\"\n\n# HTTP Client\nreqwest = { version = \"0.11\", features = [\"json\"] }\n\n# Testing\nmockall = \"0.11\"\ntokio-test = \"0.4\"\ncriterion = { version = \"0.5\", features = [\"html_reports\"] }\ncargo-nextest = \"0.9\"\n\n[profile.dev]\nopt-level = 0\ndebug = true\nsplit-debuginfo = \"unpacked\"\ndebug-assertions = true\noverflow-checks = true\nlto = false\npanic = \"unwind\"\nincremental = true\ncodegen-units = 256\nrpath = false\n\n[profile.release]\nopt-level = 3\ndebug = false\nsplit-debuginfo = \"packed\"\ndebug-assertions = false\noverflow-checks = false\nlto = true\npanic = \"abort\"\nincremental = false\ncodegen-units = 1\nrpath = false\n\n[profile.test]\nopt-level = 0\ndebug = 2\ndebug-assertions = true\noverflow-checks = true\nlto = false\npanic = \"unwind\"\nincremental = true\ncodegen-units = 256\n\n[profile.bench]\nopt-level = 3\ndebug = false\ndebug-assertions = false\noverflow-checks = false\nlto = true\npanic = \"abort\"\nincremental = false\ncodegen-units = 1\n"}