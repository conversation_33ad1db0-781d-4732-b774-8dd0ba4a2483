{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/audit.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Audit log entry\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditLogEntry {\n    pub id: String,\n    pub event_type: AuditEventType,\n    pub entity_type: String,\n    pub entity_id: String,\n    pub user_id: String,\n    pub user_email: String,\n    pub organization_id: Option<String>,\n    pub action: AuditAction,\n    pub changes: Option<AuditChanges>,\n    pub metadata: Option<Value>,\n    pub ip_address: Option<String>,\n    pub user_agent: Option<String>,\n    pub session_id: Option<String>,\n    pub timestamp: String,\n    pub severity: AuditSeverity,\n}\n\n/// Audit event types\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditEventType {\n    Authentication,\n    Authorization,\n    DataAccess,\n    DataModification,\n    SystemEvent,\n    SecurityEvent,\n    ComplianceEvent,\n    UserAction,\n    AdminAction,\n    ApiCall,\n}\n\n/// Audit actions\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditAction {\n    Create,\n    Read,\n    Update,\n    Delete,\n    Login,\n    Logout,\n    PasswordChange,\n    PermissionChange,\n    Export,\n    Import,\n    Download,\n    Upload,\n    Share,\n    Archive,\n    Restore,\n    Approve,\n    Reject,\n    Submit,\n    Cancel,\n    Configure,\n    Backup,\n    Restore,\n    Migrate,\n}\n\n/// Audit changes\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditChanges {\n    pub fields_changed: Vec<FieldChange>,\n    pub before_snapshot: Option<Value>,\n    pub after_snapshot: Option<Value>,\n    pub change_summary: String,\n}\n\n/// Field change\n#[derive(Debug, Serialize, Deserialize)]\npub struct FieldChange {\n    pub field_name: String,\n    pub field_path: String,\n    pub old_value: Option<Value>,\n    pub new_value: Option<Value>,\n    pub change_type: FieldChangeType,\n    pub data_type: String,\n}\n\n/// Field change types\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum FieldChangeType {\n    Added,\n    Modified,\n    Deleted,\n    Encrypted,\n    Decrypted,\n}\n\n/// Audit severity levels\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditSeverity {\n    Info,\n    Warning,\n    Error,\n    Critical,\n}\n\n/// Audit query request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditQueryRequest {\n    pub event_type: Option<AuditEventType>,\n    pub entity_type: Option<String>,\n    pub entity_id: Option<String>,\n    pub user_id: Option<String>,\n    pub organization_id: Option<String>,\n    pub action: Option<AuditAction>,\n    pub severity: Option<AuditSeverity>,\n    \n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub search_term: Option<String>,\n    \n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n    \n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n    \n    pub sort_by: Option<AuditSortField>,\n    pub sort_order: Option<SortOrder>,\n    \n    pub include_metadata: Option<bool>,\n    pub include_changes: Option<bool>,\n}\n\n/// Audit sort fields\n#[derive(Debug, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditSortField {\n    Timestamp,\n    EventType,\n    Action,\n    UserId,\n    EntityType,\n    Severity,\n}\n\n/// Sort order\n#[derive(Debug, Deserialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum SortOrder {\n    Asc,\n    Desc,\n}\n\n/// Audit query response\n#[derive(Debug, Serialize)]\npub struct AuditQueryResponse {\n    pub entries: Vec<AuditLogEntry>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n    pub query_metadata: AuditQueryMetadata,\n}\n\n/// Audit query metadata\n#[derive(Debug, Serialize)]\npub struct AuditQueryMetadata {\n    pub query_duration_ms: u64,\n    pub filters_applied: Vec<String>,\n    pub result_summary: AuditResultSummary,\n}\n\n/// Audit result summary\n#[derive(Debug, Serialize)]\npub struct AuditResultSummary {\n    pub event_type_counts: Vec<EventTypeCount>,\n    pub action_counts: Vec<ActionCount>,\n    pub severity_counts: Vec<SeverityCount>,\n    pub top_users: Vec<UserActivityCount>,\n    pub date_range: DateRange,\n}\n\n/// Event type count\n#[derive(Debug, Serialize)]\npub struct EventTypeCount {\n    pub event_type: AuditEventType,\n    pub count: u64,\n}\n\n/// Action count\n#[derive(Debug, Serialize)]\npub struct ActionCount {\n    pub action: AuditAction,\n    pub count: u64,\n}\n\n/// Severity count\n#[derive(Debug, Serialize)]\npub struct SeverityCount {\n    pub severity: AuditSeverity,\n    pub count: u64,\n}\n\n/// User activity count\n#[derive(Debug, Serialize)]\npub struct UserActivityCount {\n    pub user_id: String,\n    pub user_email: String,\n    pub activity_count: u64,\n}\n\n/// Date range\n#[derive(Debug, Serialize)]\npub struct DateRange {\n    pub start_date: String,\n    pub end_date: String,\n}\n\n/// Audit report request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditReportRequest {\n    pub report_type: AuditReportType,\n    pub start_date: String,\n    pub end_date: String,\n    pub organization_id: Option<String>,\n    pub entity_types: Option<Vec<String>>,\n    pub include_details: Option<bool>,\n    pub format: Option<ReportFormat>,\n}\n\n/// Audit report types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditReportType {\n    ComplianceReport,\n    SecurityReport,\n    UserActivityReport,\n    DataAccessReport,\n    SystemEventReport,\n    CustomReport,\n}\n\n/// Report formats\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum ReportFormat {\n    Json,\n    Csv,\n    Pdf,\n    Excel,\n}\n\n/// Audit report response\n#[derive(Debug, Serialize)]\npub struct AuditReportResponse {\n    pub report_id: String,\n    pub report_type: AuditReportType,\n    pub generated_at: String,\n    pub generated_by: String,\n    pub date_range: DateRange,\n    pub summary: AuditReportSummary,\n    pub download_url: Option<String>,\n    pub expires_at: Option<String>,\n}\n\n/// Audit report summary\n#[derive(Debug, Serialize)]\npub struct AuditReportSummary {\n    pub total_events: u64,\n    pub unique_users: u64,\n    pub entities_affected: u64,\n    pub security_events: u64,\n    pub compliance_violations: u64,\n    pub key_findings: Vec<String>,\n    pub recommendations: Vec<String>,\n}\n\n/// Audit trail request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditTrailRequest {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub include_related_entities: Option<bool>,\n}\n\n/// Audit trail response\n#[derive(Debug, Serialize)]\npub struct AuditTrailResponse {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub trail: Vec<AuditLogEntry>,\n    pub related_trails: Option<Vec<RelatedAuditTrail>>,\n    pub trail_summary: AuditTrailSummary,\n}\n\n/// Related audit trail\n#[derive(Debug, Serialize)]\npub struct RelatedAuditTrail {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub relationship: String,\n    pub entries: Vec<AuditLogEntry>,\n}\n\n/// Audit trail summary\n#[derive(Debug, Serialize)]\npub struct AuditTrailSummary {\n    pub total_events: u64,\n    pub first_event: String,\n    pub last_event: String,\n    pub unique_users: u64,\n    pub most_common_action: AuditAction,\n    pub lifecycle_stages: Vec<LifecycleStage>,\n}\n\n/// Lifecycle stage\n#[derive(Debug, Serialize)]\npub struct LifecycleStage {\n    pub stage: String,\n    pub timestamp: String,\n    pub user: String,\n    pub action: AuditAction,\n}\n\n/// Audit retention policy\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditRetentionPolicy {\n    pub policy_id: String,\n    pub entity_type: Option<String>,\n    pub event_type: Option<AuditEventType>,\n    pub retention_days: u32,\n    pub archive_after_days: Option<u32>,\n    pub delete_after_days: Option<u32>,\n    pub compliance_requirements: Vec<String>,\n}\n\n/// Audit compliance check request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditComplianceCheckRequest {\n    pub compliance_framework: ComplianceFramework,\n    pub start_date: String,\n    pub end_date: String,\n    pub organization_id: Option<String>,\n}\n\n/// Compliance frameworks\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComplianceFramework {\n    Gdpr,\n    Sox,\n    Hipaa,\n    Iso27001,\n    Vietnamese,\n    Custom,\n}\n\n/// Audit compliance check response\n#[derive(Debug, Serialize)]\npub struct AuditComplianceCheckResponse {\n    pub compliance_framework: ComplianceFramework,\n    pub overall_score: f64,\n    pub compliance_status: ComplianceStatus,\n    pub violations: Vec<ComplianceViolation>,\n    pub recommendations: Vec<ComplianceRecommendation>,\n    pub next_review_date: String,\n}\n\n/// Compliance status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComplianceStatus {\n    Compliant,\n    MinorIssues,\n    MajorIssues,\n    NonCompliant,\n}\n\n/// Compliance violation\n#[derive(Debug, Serialize)]\npub struct ComplianceViolation {\n    pub violation_type: String,\n    pub severity: AuditSeverity,\n    pub description: String,\n    pub affected_records: u64,\n    pub remediation_required: bool,\n    pub deadline: Option<String>,\n}\n\n/// Compliance recommendation\n#[derive(Debug, Serialize)]\npub struct ComplianceRecommendation {\n    pub category: String,\n    pub priority: String,\n    pub description: String,\n    pub implementation_effort: String,\n    pub expected_impact: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Audit log entry\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditLogEntry {\n    pub id: String,\n    pub event_type: AuditEventType,\n    pub entity_type: String,\n    pub entity_id: String,\n    pub user_id: String,\n    pub user_email: String,\n    pub organization_id: Option<String>,\n    pub action: AuditAction,\n    pub changes: Option<AuditChanges>,\n    pub metadata: Option<Value>,\n    pub ip_address: Option<String>,\n    pub user_agent: Option<String>,\n    pub session_id: Option<String>,\n    pub timestamp: String,\n    pub severity: AuditSeverity,\n}\n\n/// Audit event types\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditEventType {\n    Authentication,\n    Authorization,\n    DataAccess,\n    DataModification,\n    SystemEvent,\n    SecurityEvent,\n    ComplianceEvent,\n    UserAction,\n    AdminAction,\n    ApiCall,\n}\n\n/// Audit actions\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditAction {\n    Create,\n    Read,\n    Update,\n    Delete,\n    Login,\n    Logout,\n    PasswordChange,\n    PermissionChange,\n    Export,\n    Import,\n    Download,\n    Upload,\n    Share,\n    Archive,\n    Restore,\n    Approve,\n    Reject,\n    Submit,\n    Cancel,\n    Configure,\n    Backup,\n    Migrate,\n}\n\n/// Audit changes\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditChanges {\n    pub fields_changed: Vec<FieldChange>,\n    pub before_snapshot: Option<Value>,\n    pub after_snapshot: Option<Value>,\n    pub change_summary: String,\n}\n\n/// Field change\n#[derive(Debug, Serialize, Deserialize)]\npub struct FieldChange {\n    pub field_name: String,\n    pub field_path: String,\n    pub old_value: Option<Value>,\n    pub new_value: Option<Value>,\n    pub change_type: FieldChangeType,\n    pub data_type: String,\n}\n\n/// Field change types\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum FieldChangeType {\n    Added,\n    Modified,\n    Deleted,\n    Encrypted,\n    Decrypted,\n}\n\n/// Audit severity levels\n#[derive(Debug, Serialize, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditSeverity {\n    Info,\n    Warning,\n    Error,\n    Critical,\n}\n\n/// Audit query request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditQueryRequest {\n    pub event_type: Option<AuditEventType>,\n    pub entity_type: Option<String>,\n    pub entity_id: Option<String>,\n    pub user_id: Option<String>,\n    pub organization_id: Option<String>,\n    pub action: Option<AuditAction>,\n    pub severity: Option<AuditSeverity>,\n    \n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub search_term: Option<String>,\n    \n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n    \n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n    \n    pub sort_by: Option<AuditSortField>,\n    pub sort_order: Option<SortOrder>,\n    \n    pub include_metadata: Option<bool>,\n    pub include_changes: Option<bool>,\n}\n\n/// Audit sort fields\n#[derive(Debug, Deserialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditSortField {\n    Timestamp,\n    EventType,\n    Action,\n    UserId,\n    EntityType,\n    Severity,\n}\n\n/// Sort order\n#[derive(Debug, Deserialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum SortOrder {\n    Asc,\n    Desc,\n}\n\n/// Audit query response\n#[derive(Debug, Serialize)]\npub struct AuditQueryResponse {\n    pub entries: Vec<AuditLogEntry>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n    pub query_metadata: AuditQueryMetadata,\n}\n\n/// Audit query metadata\n#[derive(Debug, Serialize)]\npub struct AuditQueryMetadata {\n    pub query_duration_ms: u64,\n    pub filters_applied: Vec<String>,\n    pub result_summary: AuditResultSummary,\n}\n\n/// Audit result summary\n#[derive(Debug, Serialize)]\npub struct AuditResultSummary {\n    pub event_type_counts: Vec<EventTypeCount>,\n    pub action_counts: Vec<ActionCount>,\n    pub severity_counts: Vec<SeverityCount>,\n    pub top_users: Vec<UserActivityCount>,\n    pub date_range: DateRange,\n}\n\n/// Event type count\n#[derive(Debug, Serialize)]\npub struct EventTypeCount {\n    pub event_type: AuditEventType,\n    pub count: u64,\n}\n\n/// Action count\n#[derive(Debug, Serialize)]\npub struct ActionCount {\n    pub action: AuditAction,\n    pub count: u64,\n}\n\n/// Severity count\n#[derive(Debug, Serialize)]\npub struct SeverityCount {\n    pub severity: AuditSeverity,\n    pub count: u64,\n}\n\n/// User activity count\n#[derive(Debug, Serialize)]\npub struct UserActivityCount {\n    pub user_id: String,\n    pub user_email: String,\n    pub activity_count: u64,\n}\n\n/// Date range\n#[derive(Debug, Serialize)]\npub struct DateRange {\n    pub start_date: String,\n    pub end_date: String,\n}\n\n/// Audit report request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditReportRequest {\n    pub report_type: AuditReportType,\n    pub start_date: String,\n    pub end_date: String,\n    pub organization_id: Option<String>,\n    pub entity_types: Option<Vec<String>>,\n    pub include_details: Option<bool>,\n    pub format: Option<ReportFormat>,\n}\n\n/// Audit report types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum AuditReportType {\n    ComplianceReport,\n    SecurityReport,\n    UserActivityReport,\n    DataAccessReport,\n    SystemEventReport,\n    CustomReport,\n}\n\n/// Report formats\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum ReportFormat {\n    Json,\n    Csv,\n    Pdf,\n    Excel,\n}\n\n/// Audit report response\n#[derive(Debug, Serialize)]\npub struct AuditReportResponse {\n    pub report_id: String,\n    pub report_type: AuditReportType,\n    pub generated_at: String,\n    pub generated_by: String,\n    pub date_range: DateRange,\n    pub summary: AuditReportSummary,\n    pub download_url: Option<String>,\n    pub expires_at: Option<String>,\n}\n\n/// Audit report summary\n#[derive(Debug, Serialize)]\npub struct AuditReportSummary {\n    pub total_events: u64,\n    pub unique_users: u64,\n    pub entities_affected: u64,\n    pub security_events: u64,\n    pub compliance_violations: u64,\n    pub key_findings: Vec<String>,\n    pub recommendations: Vec<String>,\n}\n\n/// Audit trail request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditTrailRequest {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub include_related_entities: Option<bool>,\n}\n\n/// Audit trail response\n#[derive(Debug, Serialize)]\npub struct AuditTrailResponse {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub trail: Vec<AuditLogEntry>,\n    pub related_trails: Option<Vec<RelatedAuditTrail>>,\n    pub trail_summary: AuditTrailSummary,\n}\n\n/// Related audit trail\n#[derive(Debug, Serialize)]\npub struct RelatedAuditTrail {\n    pub entity_type: String,\n    pub entity_id: String,\n    pub relationship: String,\n    pub entries: Vec<AuditLogEntry>,\n}\n\n/// Audit trail summary\n#[derive(Debug, Serialize)]\npub struct AuditTrailSummary {\n    pub total_events: u64,\n    pub first_event: String,\n    pub last_event: String,\n    pub unique_users: u64,\n    pub most_common_action: AuditAction,\n    pub lifecycle_stages: Vec<LifecycleStage>,\n}\n\n/// Lifecycle stage\n#[derive(Debug, Serialize)]\npub struct LifecycleStage {\n    pub stage: String,\n    pub timestamp: String,\n    pub user: String,\n    pub action: AuditAction,\n}\n\n/// Audit retention policy\n#[derive(Debug, Serialize, Deserialize)]\npub struct AuditRetentionPolicy {\n    pub policy_id: String,\n    pub entity_type: Option<String>,\n    pub event_type: Option<AuditEventType>,\n    pub retention_days: u32,\n    pub archive_after_days: Option<u32>,\n    pub delete_after_days: Option<u32>,\n    pub compliance_requirements: Vec<String>,\n}\n\n/// Audit compliance check request\n#[derive(Debug, Deserialize, Validate)]\npub struct AuditComplianceCheckRequest {\n    pub compliance_framework: ComplianceFramework,\n    pub start_date: String,\n    pub end_date: String,\n    pub organization_id: Option<String>,\n}\n\n/// Compliance frameworks\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComplianceFramework {\n    Gdpr,\n    Sox,\n    Hipaa,\n    Iso27001,\n    Vietnamese,\n    Custom,\n}\n\n/// Audit compliance check response\n#[derive(Debug, Serialize)]\npub struct AuditComplianceCheckResponse {\n    pub compliance_framework: ComplianceFramework,\n    pub overall_score: f64,\n    pub compliance_status: ComplianceStatus,\n    pub violations: Vec<ComplianceViolation>,\n    pub recommendations: Vec<ComplianceRecommendation>,\n    pub next_review_date: String,\n}\n\n/// Compliance status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComplianceStatus {\n    Compliant,\n    MinorIssues,\n    MajorIssues,\n    NonCompliant,\n}\n\n/// Compliance violation\n#[derive(Debug, Serialize)]\npub struct ComplianceViolation {\n    pub violation_type: String,\n    pub severity: AuditSeverity,\n    pub description: String,\n    pub affected_records: u64,\n    pub remediation_required: bool,\n    pub deadline: Option<String>,\n}\n\n/// Compliance recommendation\n#[derive(Debug, Serialize)]\npub struct ComplianceRecommendation {\n    pub category: String,\n    pub priority: String,\n    pub description: String,\n    pub implementation_effort: String,\n    pub expected_impact: String,\n}\n"}