{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/docs/mod.rs"}, "originalCode": "use utoipa::OpenApi;\nuse utoipa_swagger_ui::SwaggerUi;\nuse axum::Router;\n\nuse crate::{\n    dto::{auth::*, user::*, organization::*, project::*, template::*, common::*},\n    handlers::error::*,\n    AppState,\n};\n\n/// OpenAPI documentation configuration\n#[derive(OpenApi)]\n#[openapi(\n    info(\n        title = \"ExcelSync API\",\n        version = \"1.0.0\",\n        description = \"Real Estate Project Management API with Excel Integration\",\n        contact(\n            name = \"ExcelSync Team\",\n            email = \"<EMAIL>\",\n            url = \"https://excelsync.com\"\n        ),\n        license(\n            name = \"MIT\",\n            url = \"https://opensource.org/licenses/MIT\"\n        )\n    ),\n    servers(\n        (url = \"http://localhost:3000\", description = \"Development server\"),\n        (url = \"https://api.excelsync.com\", description = \"Production server\")\n    ),\n    paths(\n        // Note: Paths will be automatically discovered from route handlers with #[utoipa::path] annotations\n    ),\n    components(\n        schemas(\n            // Common DTOs\n            PaginatedResponse<UserResponse>,\n            PaginationInfo,\n            PaginationQuery,\n            IdParam,\n            SearchQuery,\n            SortOrder,\n            DateRangeFilter,\n            StatusFilter,\n            FileMetadata,\n            BulkOperationRequest,\n            BulkOperation,\n            BulkOperationResponse,\n            BulkOperationError,\n            \n            // Authentication DTOs\n            LoginRequest,\n            LoginResponse,\n            RegisterRequest,\n            RefreshTokenRequest,\n            TokenResponse,\n            \n            // User DTOs\n            UserResponse,\n            CreateUserRequest,\n            UpdateUserRequest,\n            ChangePasswordRequest,\n            UpdateUserProfileRequest,\n            UserPreferences,\n            NotificationPreferences,\n            UserActivitySummary,\n            \n            // Organization DTOs\n            OrganizationResponse,\n            CreateOrganizationRequest,\n            UpdateOrganizationRequest,\n            \n            // Project DTOs\n            ProjectResponse,\n            CreateProjectRequest,\n            UpdateProjectRequest,\n            ProjectSummary,\n            ProjectStatistics,\n            ProjectTypeCount,\n            ProjectStatusCount,\n            ProjectFilterRequest,\n            ProjectExportRequest,\n            ExportFormat,\n            \n            // Template DTOs\n            TemplateResponse,\n            CreateTemplateRequest,\n            UpdateTemplateRequest,\n            TemplateDataProcessingRequest,\n            ProcessingOptions,\n            ValidationLevel,\n            TemplateDataProcessingResponse,\n            CalculatedField,\n            ProcessingSummary,\n            ProcessingWarning,\n            ProcessingError,\n            ErrorSeverity,\n            TemplateImportRequest,\n            ImportOptions,\n            TemplateImportResponse,\n            ImportStatus,\n            ImportSummary,\n            TemplateExportRequest,\n            TemplateUsageStatistics,\n            \n            // Financial DTOs\n            FinancialCalculationRequest,\n            FinancialCalculationType,\n            FinancialInputData,\n            FinancialCalculationResponse,\n            FinancialResults,\n            TaxCalculationResults,\n            TaxBreakdownItem,\n            RoiAnalysisResults,\n            RiskAssessment,\n            RiskLevel,\n            InvestmentAnalysisResults,\n            CostBreakdown,\n            RevenueProjections,\n            RevenueTimelineItem,\n            SensitivityAnalysis,\n            SensitivityResult,\n            ScenarioResult,\n            CashFlowResults,\n            CashFlowItem,\n            CashFlowSummary,\n            BreakEvenResults,\n            ProfitabilityResults,\n            FinancialSummary,\n            FinancialRecommendation,\n            RecommendationType,\n            FinancialHistoryRequest,\n            FinancialHistoryResponse,\n            FinancialCalculationSummary,\n            \n            // Versioning DTOs\n            VersionInfo,\n            CreateVersionRequest,\n            VersionableEntityType,\n            VersionComparisonRequest,\n            ComparisonType,\n            VersionComparisonResponse,\n            FieldDifference,\n            ChangeType,\n            ComparisonSummary,\n            CompatibilityStatus,\n            VersionMergeRequest,\n            MergeStrategy,\n            ConflictResolution,\n            ResolutionType,\n            VersionMergeResponse,\n            MergeConflict,\n            ConflictType,\n            MergeStatus,\n            MergeSummary,\n            VersionHistoryRequest,\n            VersionHistoryResponse,\n            VersionHistoryItem,\n            VersionRollbackRequest,\n            VersionRollbackResponse,\n            RollbackSummary,\n            VersionBranchRequest,\n            VersionBranchResponse,\n            VersionTagRequest,\n            VersionStatistics,\n            VersionFrequency,\n            \n            // Audit DTOs\n            AuditLogEntry,\n            AuditEventType,\n            AuditAction,\n            AuditChanges,\n            FieldChange,\n            FieldChangeType,\n            AuditSeverity,\n            AuditQueryRequest,\n            AuditSortField,\n            AuditQueryResponse,\n            AuditQueryMetadata,\n            AuditResultSummary,\n            EventTypeCount,\n            ActionCount,\n            SeverityCount,\n            UserActivityCount,\n            DateRange,\n            AuditReportRequest,\n            AuditReportType,\n            ReportFormat,\n            AuditReportResponse,\n            AuditReportSummary,\n            AuditTrailRequest,\n            AuditTrailResponse,\n            RelatedAuditTrail,\n            AuditTrailSummary,\n            LifecycleStage,\n            AuditRetentionPolicy,\n            AuditComplianceCheckRequest,\n            ComplianceFramework,\n            AuditComplianceCheckResponse,\n            ComplianceStatus,\n            ComplianceViolation,\n            ComplianceRecommendation,\n            \n            // Validation DTOs\n            ValidationRequest,\n            ValidationType,\n            ValidationContext,\n            ValidationRule,\n            ValidationRuleType,\n            ValidationCondition,\n            ValidationOperator,\n            ValidationSeverity,\n            ValidationResponse,\n            ValidationResults,\n            ValidationError,\n            ValidationWarning,\n            ValidationInfo,\n            ValidationSummary,\n            ValidationStatus,\n            FieldValidationResult,\n            TemplateValidationRequest,\n            TemplateValidationResponse,\n            SchemaValidationResult,\n            SchemaValidationError,\n            SchemaErrorType,\n            TypeMismatch,\n            BusinessRulesValidationResult,\n            BusinessRuleViolation,\n            ViolationType,\n            CalculatedValue,\n            DataQualityAssessment,\n            DataQualityIssue,\n            DataQualityIssueType,\n            ImpactLevel,\n            ValidationRecommendation,\n            RecommendationCategory,\n            RecommendationPriority,\n            BatchValidationRequest,\n            BatchValidationItem,\n            BatchValidationResponse,\n            BatchValidationItemResult,\n            BatchValidationSummary,\n            ErrorFrequency,\n            \n            // Error DTOs\n            ErrorResponse,\n            ErrorInfo,\n            ValidationErrorDetails,\n            FieldError,\n            BusinessRuleError,\n            RateLimitError,\n            FileError,\n            ExternalServiceError,\n            ConflictError,\n            TimeoutError,\n            PaymentError,\n            UnprocessableEntityError,\n        )\n    ),\n    tags(\n        (name = \"Authentication\", description = \"User authentication and authorization\"),\n        (name = \"Users\", description = \"User management operations\"),\n        (name = \"Organizations\", description = \"Organization management operations\"),\n        (name = \"Projects\", description = \"Project management operations\"),\n        (name = \"Templates\", description = \"Template management operations\"),\n        (name = \"Financial\", description = \"Financial calculations and analysis\"),\n        (name = \"Versioning\", description = \"Data versioning and conflict resolution\"),\n        (name = \"Audit\", description = \"Audit logging and compliance\"),\n        (name = \"Validation\", description = \"Data validation and quality checks\"),\n    )\n)]\npub struct ApiDoc;\n\n/// Create Swagger UI router\npub fn create_swagger_ui() -> SwaggerUi {\n    SwaggerUi::new(\"/swagger-ui\")\n        .url(\"/api-docs/openapi.json\", ApiDoc::openapi())\n}\n\n/// Create documentation router\npub fn create_docs_router() -> Router<AppState> {\n    Router::new()\n        .merge(create_swagger_ui())\n        .route(\"/api-docs/openapi.json\", axum::routing::get(|| async {\n            axum::Json(ApiDoc::openapi())\n        }))\n}\n", "modifiedCode": "use utoipa::OpenApi;\nuse utoipa_swagger_ui::SwaggerUi;\nuse axum::Router;\n\nuse crate::{\n    dto::{auth::*, user::*, organization::*, project::*, template::*, common::*},\n    handlers::error::*,\n    AppState,\n};\n\n/// OpenAPI documentation configuration\n#[derive(OpenApi)]\n#[openapi(\n    info(\n        title = \"ExcelSync API\",\n        version = \"1.0.0\",\n        description = \"Real Estate Project Management API with Excel Integration\",\n        contact(\n            name = \"ExcelSync Team\",\n            email = \"<EMAIL>\",\n            url = \"https://excelsync.com\"\n        ),\n        license(\n            name = \"MIT\",\n            url = \"https://opensource.org/licenses/MIT\"\n        )\n    ),\n    servers(\n        (url = \"http://localhost:3000\", description = \"Development server\"),\n        (url = \"https://api.excelsync.com\", description = \"Production server\")\n    ),\n    paths(\n        // Note: Paths will be automatically discovered from route handlers with #[utoipa::path] annotations\n    ),\n    components(\n        schemas(\n            // Core DTOs\n            LoginRequest,\n            LoginResponse,\n            RegisterRequest,\n            RefreshTokenRequest,\n            TokenResponse,\n            UserResponse,\n            CreateUserRequest,\n            UpdateUserRequest,\n            OrganizationResponse,\n            CreateOrganizationRequest,\n            UpdateOrganizationRequest,\n            ProjectResponse,\n            CreateProjectRequest,\n            UpdateProjectRequest,\n            TemplateResponse,\n            CreateTemplateRequest,\n            UpdateTemplateRequest,\n\n            // Error DTOs\n            ErrorResponse,\n            ErrorInfo,\n        )\n    ),\n    tags(\n        (name = \"Authentication\", description = \"User authentication and authorization\"),\n        (name = \"Users\", description = \"User management operations\"),\n        (name = \"Organizations\", description = \"Organization management operations\"),\n        (name = \"Projects\", description = \"Project management operations\"),\n        (name = \"Templates\", description = \"Template management operations\"),\n        (name = \"Financial\", description = \"Financial calculations and analysis\"),\n        (name = \"Versioning\", description = \"Data versioning and conflict resolution\"),\n        (name = \"Audit\", description = \"Audit logging and compliance\"),\n        (name = \"Validation\", description = \"Data validation and quality checks\"),\n    )\n)]\npub struct ApiDoc;\n\n/// Create Swagger UI router\npub fn create_swagger_ui() -> SwaggerUi {\n    SwaggerUi::new(\"/swagger-ui\")\n        .url(\"/api-docs/openapi.json\", ApiDoc::openapi())\n}\n\n/// Create documentation router\npub fn create_docs_router() -> Router<AppState> {\n    Router::new()\n        .merge(create_swagger_ui())\n        .route(\"/api-docs/openapi.json\", axum::routing::get(|| async {\n            axum::Json(ApiDoc::openapi())\n        }))\n}\n"}