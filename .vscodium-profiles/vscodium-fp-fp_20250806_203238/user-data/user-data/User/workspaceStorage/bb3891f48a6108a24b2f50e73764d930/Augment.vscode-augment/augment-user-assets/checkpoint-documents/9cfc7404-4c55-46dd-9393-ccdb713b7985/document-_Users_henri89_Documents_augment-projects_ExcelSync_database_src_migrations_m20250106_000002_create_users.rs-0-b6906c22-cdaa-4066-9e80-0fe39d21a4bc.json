{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000002_create_users.rs"}, "originalCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(UserRole::Table)\n                    .values([\n                        UserRole::Admin,\n                        UserRole::ProjectManager,\n                        UserRole::Analyst,\n                        UserRole::Viewer,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_table(\n                Table::create()\n                    .table(Users::Table)\n                    .if_not_exists()\n                    .col(ColumnDef::new(Users::Id).uuid().not_null().primary_key())\n                    .col(\n                        ColumnDef::new(Users::Email)\n                            .string()\n                            .not_null()\n                            .unique_key(),\n                    )\n                    .col(ColumnDef::new(Users::PasswordHash).string().not_null())\n                    .col(ColumnDef::new(Users::FullName).string().not_null())\n                    .col(ColumnDef::new(Users::OrganizationId).uuid())\n                    .col(\n                        ColumnDef::new(Users::Role)\n                            .custom(UserRole::Table)\n                            .not_null()\n                            .default(\"viewer\"),\n                    )\n                    .col(\n                        ColumnDef::new(Users::IsActive)\n                            .boolean()\n                            .not_null()\n                            .default(true),\n                    )\n                    .col(ColumnDef::new(Users::LastLogin).timestamp_with_time_zone())\n                    .col(\n                        ColumnDef::new(Users::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(Users::UpdatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_users_organization_id\")\n                            .from(Users::Table, Users::OrganizationId)\n                            .to(Organizations::Table, Organizations::Id)\n                            .on_delete(ForeignKeyAction::SetNull),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Users::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(UserRole::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n    Email,\n    PasswordHash,\n    FullName,\n    OrganizationId,\n    Role,\n    IsActive,\n    LastLogin,\n    CreatedAt,\n    UpdatedAt,\n}\n\n#[derive(Iden)]\nenum UserRole {\n    Table,\n    Admin,\n    ProjectManager,\n    Analyst,\n    Viewer,\n}\n\n#[derive(Iden)]\nenum Organizations {\n    Table,\n    Id,\n}\n", "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(UserRole::Table)\n                    .values([\n                        UserRole::Admin,\n                        UserRole::ProjectManager,\n                        UserRole::Analyst,\n                        UserRole::Viewer,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_table(\n                Table::create()\n                    .table(Users::Table)\n                    .if_not_exists()\n                    .col(ColumnDef::new(Users::Id).uuid().not_null().primary_key())\n                    .col(\n                        ColumnDef::new(Users::Email)\n                            .string()\n                            .not_null()\n                            .unique_key(),\n                    )\n                    .col(ColumnDef::new(Users::PasswordHash).string().not_null())\n                    .col(ColumnDef::new(Users::FullName).string().not_null())\n                    .col(ColumnDef::new(Users::OrganizationId).uuid())\n                    .col(\n                        ColumnDef::new(Users::Role)\n                            .custom(UserRole::Table)\n                            .not_null()\n                            .default(\"viewer\"),\n                    )\n                    .col(\n                        ColumnDef::new(Users::IsActive)\n                            .boolean()\n                            .not_null()\n                            .default(true),\n                    )\n                    .col(ColumnDef::new(Users::LastLogin).timestamp_with_time_zone())\n                    .col(\n                        ColumnDef::new(Users::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(Users::UpdatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_users_organization_id\")\n                            .from(Users::Table, Users::OrganizationId)\n                            .to(Organizations::Table, Organizations::Id)\n                            .on_delete(ForeignKeyAction::SetNull),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Users::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(UserRole::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n    Email,\n    PasswordHash,\n    FullName,\n    OrganizationId,\n    Role,\n    IsActive,\n    LastLogin,\n    CreatedAt,\n    UpdatedAt,\n}\n\n#[derive(Iden)]\nenum UserRole {\n    Table,\n    Admin,\n    ProjectManager,\n    Analyst,\n    Viewer,\n}\n\n#[derive(Iden)]\nenum Organizations {\n    Table,\n    Id,\n}\n"}