{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/users.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::UserRole;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"users\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    #[sea_orm(unique)]\n    pub email: String,\n    \n    pub password_hash: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<DateTime<Utc>>,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n    \n    #[sea_orm(has_many = \"super::sessions::Entity\")]\n    Sessions,\n    \n    #[sea_orm(has_many = \"super::audit_logs::Entity\")]\n    AuditLogs,\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl Related<super::sessions::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Sessions.def()\n    }\n}\n\nimpl Related<super::audit_logs::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::AuditLogs.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n\n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use chrono::Utc;\n\n    #[test]\n    fn test_user_model_creation() {\n        let user_id = Uuid::new_v4();\n        let now = Utc::now();\n\n        let user = Model {\n            id: user_id,\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"$argon2id$v=19$m=65536,t=3,p=4$test\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n            is_active: true,\n            last_login: None,\n            created_at: now,\n            updated_at: now,\n        };\n\n        assert_eq!(user.id, user_id);\n        assert_eq!(user.email, \"<EMAIL>\");\n        assert_eq!(user.full_name, \"Test User\");\n        assert_eq!(user.role, UserRole::Analyst);\n        assert!(user.is_active);\n        assert_eq!(user.organization_id, None);\n        assert_eq!(user.last_login, None);\n    }\n\n    #[test]\n    fn test_active_model_new() {\n        let active_model = ActiveModel::new();\n\n        // Check that default values are set\n        assert!(matches!(active_model.id, Set(_)));\n        assert!(matches!(active_model.created_at, Set(_)));\n        assert!(matches!(active_model.updated_at, Set(_)));\n        assert_eq!(active_model.is_active, Set(true));\n    }\n\n    #[test]\n    fn test_user_serialization() {\n        let user_id = Uuid::new_v4();\n        let now = Utc::now();\n\n        let user = Model {\n            id: user_id,\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"$argon2id$v=19$m=65536,t=3,p=4$test\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n            is_active: true,\n            last_login: None,\n            created_at: now,\n            updated_at: now,\n        };\n\n        // Test serialization\n        let serialized = serde_json::to_string(&user);\n        assert!(serialized.is_ok());\n\n        // Test deserialization\n        let deserialized: Result<Model, _> = serde_json::from_str(&serialized.unwrap());\n        assert!(deserialized.is_ok());\n\n        let deserialized_user = deserialized.unwrap();\n        assert_eq!(deserialized_user.id, user.id);\n        assert_eq!(deserialized_user.email, user.email);\n        assert_eq!(deserialized_user.role, user.role);\n    }\n\n    #[test]\n    fn test_user_role_variants() {\n        let admin_user = Model {\n            id: Uuid::new_v4(),\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"hash\".to_string(),\n            full_name: \"Admin User\".to_string(),\n            organization_id: None,\n            role: UserRole::Admin,\n            is_active: true,\n            last_login: None,\n            created_at: Utc::now(),\n            updated_at: Utc::now(),\n        };\n\n        assert_eq!(admin_user.role, UserRole::Admin);\n\n        let pm_user = Model {\n            role: UserRole::ProjectManager,\n            ..admin_user.clone()\n        };\n\n        assert_eq!(pm_user.role, UserRole::ProjectManager);\n\n        let analyst_user = Model {\n            role: UserRole::Analyst,\n            ..admin_user.clone()\n        };\n\n        assert_eq!(analyst_user.role, UserRole::Analyst);\n\n        let viewer_user = Model {\n            role: UserRole::Viewer,\n            ..admin_user\n        };\n\n        assert_eq!(viewer_user.role, UserRole::Viewer);\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::UserRole;\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"users\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    #[sea_orm(unique)]\n    pub email: String,\n    \n    pub password_hash: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<DateTime<Utc>>,\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::projects::Entity\")]\n    Projects,\n    \n    #[sea_orm(has_many = \"super::sessions::Entity\")]\n    Sessions,\n    \n    #[sea_orm(has_many = \"super::audit_logs::Entity\")]\n    AuditLogs,\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Projects.def()\n    }\n}\n\nimpl Related<super::sessions::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Sessions.def()\n    }\n}\n\nimpl Related<super::audit_logs::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::AuditLogs.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            is_active: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n\n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use chrono::Utc;\n    use sea_orm::ActiveValue::Set;\n\n    #[test]\n    fn test_user_model_creation() {\n        let user_id = Uuid::new_v4();\n        let now = Utc::now();\n\n        let user = Model {\n            id: user_id,\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"$argon2id$v=19$m=65536,t=3,p=4$test\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n            is_active: true,\n            last_login: None,\n            created_at: now,\n            updated_at: now,\n        };\n\n        assert_eq!(user.id, user_id);\n        assert_eq!(user.email, \"<EMAIL>\");\n        assert_eq!(user.full_name, \"Test User\");\n        assert_eq!(user.role, UserRole::Analyst);\n        assert!(user.is_active);\n        assert_eq!(user.organization_id, None);\n        assert_eq!(user.last_login, None);\n    }\n\n    #[test]\n    fn test_active_model_new() {\n        let active_model = ActiveModel::new();\n\n        // Check that default values are set\n        assert!(matches!(active_model.id, Set(_)));\n        assert!(matches!(active_model.created_at, Set(_)));\n        assert!(matches!(active_model.updated_at, Set(_)));\n        assert_eq!(active_model.is_active, Set(true));\n    }\n\n    #[test]\n    fn test_user_serialization() {\n        let user_id = Uuid::new_v4();\n        let now = Utc::now();\n\n        let user = Model {\n            id: user_id,\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"$argon2id$v=19$m=65536,t=3,p=4$test\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n            is_active: true,\n            last_login: None,\n            created_at: now,\n            updated_at: now,\n        };\n\n        // Test serialization\n        let serialized = serde_json::to_string(&user);\n        assert!(serialized.is_ok());\n\n        // Test deserialization\n        let deserialized: Result<Model, _> = serde_json::from_str(&serialized.unwrap());\n        assert!(deserialized.is_ok());\n\n        let deserialized_user = deserialized.unwrap();\n        assert_eq!(deserialized_user.id, user.id);\n        assert_eq!(deserialized_user.email, user.email);\n        assert_eq!(deserialized_user.role, user.role);\n    }\n\n    #[test]\n    fn test_user_role_variants() {\n        let admin_user = Model {\n            id: Uuid::new_v4(),\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"hash\".to_string(),\n            full_name: \"Admin User\".to_string(),\n            organization_id: None,\n            role: UserRole::Admin,\n            is_active: true,\n            last_login: None,\n            created_at: Utc::now(),\n            updated_at: Utc::now(),\n        };\n\n        assert_eq!(admin_user.role, UserRole::Admin);\n\n        let pm_user = Model {\n            role: UserRole::ProjectManager,\n            ..admin_user.clone()\n        };\n\n        assert_eq!(pm_user.role, UserRole::ProjectManager);\n\n        let analyst_user = Model {\n            role: UserRole::Analyst,\n            ..admin_user.clone()\n        };\n\n        assert_eq!(analyst_user.role, UserRole::Analyst);\n\n        let viewer_user = Model {\n            role: UserRole::Viewer,\n            ..admin_user\n        };\n\n        assert_eq!(viewer_user.role, UserRole::Viewer);\n    }\n}\n"}