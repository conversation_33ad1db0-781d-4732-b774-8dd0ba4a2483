{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/templates.rs"}, "modifiedCode": "use axum::{routing::get, Router};\nuse crate::AppState;\n\n/// Template management routes\npub fn template_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_templates))\n        .route(\"/:id\", get(get_template))\n}\n\n/// List templates endpoint (placeholder)\npub async fn list_templates() -> &'static str {\n    \"List templates endpoint - TODO: implement\"\n}\n\n/// Get template endpoint (placeholder)\npub async fn get_template() -> &'static str {\n    \"Get template endpoint - TODO: implement\"\n}\n"}