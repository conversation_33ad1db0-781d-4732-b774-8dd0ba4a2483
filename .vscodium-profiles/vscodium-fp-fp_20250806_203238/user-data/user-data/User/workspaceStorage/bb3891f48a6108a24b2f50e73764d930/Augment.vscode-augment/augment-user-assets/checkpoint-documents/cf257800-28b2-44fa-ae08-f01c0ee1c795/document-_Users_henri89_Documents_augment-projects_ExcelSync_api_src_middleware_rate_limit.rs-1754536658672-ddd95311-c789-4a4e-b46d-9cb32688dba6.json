{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/rate_limit.rs"}, "modifiedCode": "use axum::{\n    extract::{ConnectInfo, Request},\n    http::StatusCode,\n    middleware::Next,\n    response::Response,\n};\nuse governor::{\n    clock::{DefaultClock, QuantaClock},\n    state::{InMemoryState, NotKeyed},\n    Quota, RateLimiter,\n};\nuse std::{net::SocketAddr, num::NonZeroU32, sync::Arc, time::Duration};\nuse tower_governor::{governor::Governor<PERSON><PERSON><PERSON>g<PERSON><PERSON>er, Governor<PERSON><PERSON><PERSON>};\n\n/// Rate limiter configuration\n#[derive(Debug, Clone)]\npub struct RateLimitConfig {\n    pub requests_per_minute: u32,\n    pub burst_size: u32,\n}\n\nimpl Default for RateLimitConfig {\n    fn default() -> Self {\n        Self {\n            requests_per_minute: 100,\n            burst_size: 10,\n        }\n    }\n}\n\n/// Create rate limiting layer using tower-governor\npub fn rate_limit_layer(config: RateLimitConfig) -> GovernorLayer<\n    governor::middleware::NoOpMiddleware<SocketAddr>,\n    InMemoryState,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n    governor::middleware::NoOpMiddleware<SocketAddr>,\n> {\n    let governor_conf = Box::new(\n        GovernorConfigBuilder::default()\n            .per_minute(config.requests_per_minute)\n            .burst_size(config.burst_size)\n            .finish()\n            .unwrap(),\n    );\n\n    GovernorLayer {\n        config: Arc::new(governor_conf),\n    }\n}\n\n/// Custom rate limiting middleware for more control\npub async fn custom_rate_limit_middleware(\n    ConnectInfo(addr): ConnectInfo<SocketAddr>,\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Create a simple in-memory rate limiter\n    static RATE_LIMITER: std::sync::OnceLock<\n        RateLimiter<NotKeyed, InMemoryState, QuantaClock>,\n    > = std::sync::OnceLock::new();\n\n    let limiter = RATE_LIMITER.get_or_init(|| {\n        RateLimiter::direct(\n            Quota::per_minute(NonZeroU32::new(100).unwrap())\n                .allow_burst(NonZeroU32::new(10).unwrap()),\n        )\n    });\n\n    // Check rate limit\n    match limiter.check() {\n        Ok(_) => {\n            // Request allowed, proceed\n            Ok(next.run(request).await)\n        }\n        Err(_) => {\n            // Rate limit exceeded\n            Err(StatusCode::TOO_MANY_REQUESTS)\n        }\n    }\n}\n\n/// Per-IP rate limiting middleware\npub struct IpRateLimiter {\n    limiters: std::sync::RwLock<std::collections::HashMap<std::net::IpAddr, Arc<RateLimiter<NotKeyed, InMemoryState, QuantaClock>>>>,\n    quota: Quota,\n}\n\nimpl IpRateLimiter {\n    pub fn new(requests_per_minute: u32, burst_size: u32) -> Self {\n        let quota = Quota::per_minute(NonZeroU32::new(requests_per_minute).unwrap())\n            .allow_burst(NonZeroU32::new(burst_size).unwrap());\n\n        Self {\n            limiters: std::sync::RwLock::new(std::collections::HashMap::new()),\n            quota,\n        }\n    }\n\n    pub async fn check_rate_limit(&self, ip: std::net::IpAddr) -> Result<(), ()> {\n        // Try to get existing limiter\n        {\n            let limiters = self.limiters.read().unwrap();\n            if let Some(limiter) = limiters.get(&ip) {\n                return limiter.check().map_err(|_| ());\n            }\n        }\n\n        // Create new limiter for this IP\n        let limiter = Arc::new(RateLimiter::direct(self.quota));\n        {\n            let mut limiters = self.limiters.write().unwrap();\n            limiters.insert(ip, limiter.clone());\n        }\n\n        limiter.check().map_err(|_| ())\n    }\n}\n\n/// Per-IP rate limiting middleware function\npub async fn ip_rate_limit_middleware(\n    ConnectInfo(addr): ConnectInfo<SocketAddr>,\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    static IP_LIMITER: std::sync::OnceLock<IpRateLimiter> = std::sync::OnceLock::new();\n    \n    let limiter = IP_LIMITER.get_or_init(|| IpRateLimiter::new(100, 10));\n\n    match limiter.check_rate_limit(addr.ip()).await {\n        Ok(_) => Ok(next.run(request).await),\n        Err(_) => Err(StatusCode::TOO_MANY_REQUESTS),\n    }\n}\n\n/// Rate limiting configuration for different endpoints\n#[derive(Debug, Clone)]\npub struct EndpointRateLimit {\n    pub path: String,\n    pub requests_per_minute: u32,\n    pub burst_size: u32,\n}\n\n/// Create endpoint-specific rate limiting\npub fn create_endpoint_rate_limits() -> Vec<EndpointRateLimit> {\n    vec![\n        EndpointRateLimit {\n            path: \"/api/v1/auth/login\".to_string(),\n            requests_per_minute: 5, // Stricter for login attempts\n            burst_size: 2,\n        },\n        EndpointRateLimit {\n            path: \"/api/v1/auth/refresh\".to_string(),\n            requests_per_minute: 20,\n            burst_size: 5,\n        },\n        EndpointRateLimit {\n            path: \"/health\".to_string(),\n            requests_per_minute: 200, // More lenient for health checks\n            burst_size: 50,\n        },\n    ]\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[tokio::test]\n    async fn test_ip_rate_limiter() {\n        let limiter = IpRateLimiter::new(2, 1); // Very restrictive for testing\n        let test_ip = \"127.0.0.1\".parse().unwrap();\n\n        // First request should succeed\n        assert!(limiter.check_rate_limit(test_ip).await.is_ok());\n        \n        // Second request should succeed (within burst)\n        assert!(limiter.check_rate_limit(test_ip).await.is_ok());\n        \n        // Third request should fail (rate limited)\n        assert!(limiter.check_rate_limit(test_ip).await.is_err());\n    }\n\n    #[test]\n    fn test_rate_limit_config() {\n        let config = RateLimitConfig::default();\n        assert_eq!(config.requests_per_minute, 100);\n        assert_eq!(config.burst_size, 10);\n    }\n}\n"}