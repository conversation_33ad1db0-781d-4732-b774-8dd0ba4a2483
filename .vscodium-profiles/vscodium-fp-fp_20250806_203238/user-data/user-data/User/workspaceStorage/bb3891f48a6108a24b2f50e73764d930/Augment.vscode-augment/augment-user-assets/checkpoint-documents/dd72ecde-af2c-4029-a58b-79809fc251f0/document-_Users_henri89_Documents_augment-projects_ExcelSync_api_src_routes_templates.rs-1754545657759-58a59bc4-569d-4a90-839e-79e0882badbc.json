{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/templates.rs"}, "originalCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    http::StatusCode,\n    response::Json,\n    routing::{get, post, put, delete},\n    Router,\n};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\n\nuse crate::{\n    dto::template::{CreateTemplateRequest, UpdateTemplateRequest},\n    handlers::{json_response, ApiError},\n    services::TemplateService,\n    AppState,\n};\n\n/// Query parameters for listing templates\n#[derive(Debug, Deserialize)]\npub struct ListTemplatesQuery {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n    pub template_type: Option<String>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template management routes\npub fn template_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_templates).post(create_template))\n        .route(\"/:id\", get(get_template).put(update_template).delete(delete_template))\n        .route(\"/:id/clone\", post(clone_template))\n        .route(\"/default/:template_type\", get(get_default_template))\n}\n\n/// List templates endpoint\npub async fn list_templates(\n    State(state): State<AppState>,\n    Query(query): Query<ListTemplatesQuery>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n\n    let templates = template_service\n        .list_templates(\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n            query.template_type,\n            query.is_active,\n            query.is_default,\n        )\n        .await?;\n\n    Ok(json_response(\"Templates retrieved successfully\", templates))\n}\n\n/// Get template endpoint\npub async fn get_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.get_template(template_id).await?;\n\n    Ok(json_response(\"Template retrieved successfully\", template))\n}\n\n/// Create template endpoint\npub async fn create_template(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateTemplateRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.create_template(create_request, &claims).await?;\n\n    Ok(json_response(\"Template created successfully\", template))\n}\n\n/// Update template endpoint\npub async fn update_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateTemplateRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.update_template(template_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Template updated successfully\", template))\n}\n\n/// Delete template endpoint\npub async fn delete_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<StatusCode, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    template_service.delete_template(template_id, &claims).await?;\n\n    Ok(StatusCode::NO_CONTENT)\n}\n\n/// Get default template for a specific type\npub async fn get_default_template(\n    State(state): State<AppState>,\n    Path(template_type): Path<String>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.get_default_template(template_type).await?;\n\n    match template {\n        Some(template) => Ok(json_response(\"Default template retrieved successfully\", template)),\n        None => Err(ApiError::NotFound(\"No default template found for this type\".to_string())),\n    }\n}\n\n// TODO: Implement template cloning when the service method is ready\n/*\n/// Clone template endpoint\npub async fn clone_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually for clone options\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let new_name = clone_request.get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(\"Copy of Template\");\n\n    let template_service = TemplateService::new(state.db.clone());\n    let cloned_template = template_service.clone_template(template_id, new_name.to_string(), &claims).await?;\n\n    Ok(json_response(\"Template cloned successfully\", cloned_template))\n}\n*/\n", "modifiedCode": "use axum::{\n    extract::{Path, Query, State, Request},\n    http::StatusCode,\n    response::Json,\n    routing::{get, post, put, delete},\n    Router,\n};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\n\nuse crate::{\n    dto::template::{CreateTemplateRequest, UpdateTemplateRequest},\n    handlers::{json_response, ApiError},\n    services::TemplateService,\n    AppState,\n};\n\n/// Query parameters for listing templates\n#[derive(Debug, Deserialize)]\npub struct ListTemplatesQuery {\n    pub page: Option<u64>,\n    pub per_page: Option<u64>,\n    pub template_type: Option<String>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template management routes\npub fn template_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_templates).post(create_template))\n        .route(\"/:id\", get(get_template).put(update_template).delete(delete_template))\n        // .route(\"/:id/clone\", post(clone_template)) // TODO: Implement when service method is ready\n        .route(\"/default/:template_type\", get(get_default_template))\n}\n\n/// List templates endpoint\npub async fn list_templates(\n    State(state): State<AppState>,\n    Query(query): Query<ListTemplatesQuery>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n\n    let templates = template_service\n        .list_templates(\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n            query.template_type,\n            query.is_active,\n            query.is_default,\n        )\n        .await?;\n\n    Ok(json_response(\"Templates retrieved successfully\", templates))\n}\n\n/// Get template endpoint\npub async fn get_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.get_template(template_id).await?;\n\n    Ok(json_response(\"Template retrieved successfully\", template))\n}\n\n/// Create template endpoint\npub async fn create_template(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateTemplateRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.create_template(create_request, &claims).await?;\n\n    Ok(json_response(\"Template created successfully\", template))\n}\n\n/// Update template endpoint\npub async fn update_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateTemplateRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.update_template(template_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Template updated successfully\", template))\n}\n\n/// Delete template endpoint\npub async fn delete_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<StatusCode, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let template_service = TemplateService::new(state.db.clone());\n    template_service.delete_template(template_id, &claims).await?;\n\n    Ok(StatusCode::NO_CONTENT)\n}\n\n/// Get default template for a specific type\npub async fn get_default_template(\n    State(state): State<AppState>,\n    Path(template_type): Path<String>,\n) -> Result<Json<Value>, ApiError> {\n    let template_service = TemplateService::new(state.db.clone());\n    let template = template_service.get_default_template(template_type).await?;\n\n    match template {\n        Some(template) => Ok(json_response(\"Default template retrieved successfully\", template)),\n        None => Err(ApiError::NotFound(\"No default template found for this type\".to_string())),\n    }\n}\n\n// TODO: Implement template cloning when the service method is ready\n/*\n/// Clone template endpoint\npub async fn clone_template(\n    State(state): State<AppState>,\n    Path(template_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually for clone options\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let new_name = clone_request.get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(\"Copy of Template\");\n\n    let template_service = TemplateService::new(state.db.clone());\n    let cloned_template = template_service.clone_template(template_id, new_name.to_string(), &claims).await?;\n\n    Ok(json_response(\"Template cloned successfully\", cloned_template))\n}\n*/\n"}