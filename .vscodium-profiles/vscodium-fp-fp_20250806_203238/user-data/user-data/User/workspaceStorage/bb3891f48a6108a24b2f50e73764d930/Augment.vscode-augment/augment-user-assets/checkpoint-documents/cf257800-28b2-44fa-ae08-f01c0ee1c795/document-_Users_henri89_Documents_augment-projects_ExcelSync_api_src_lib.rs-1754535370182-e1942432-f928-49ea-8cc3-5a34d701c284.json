{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/lib.rs"}, "originalCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::*;\npub use middleware::*;\npub use handlers::*;\npub use dto::*;\npub use state::*;\n\nuse axum::{\n    routing::{get, post},\n    Router,\n};\nuse tower::ServiceBuilder;\nuse tower_http::{\n    cors::CorsLayer,\n    trace::TraceLayer,\n    limit::RequestBodyLimitLayer,\n};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(logging_layer())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n/// Create API v1 routes\nfn create_api_routes() -> Router<AppState> {\n    Router::new()\n        // Authentication routes\n        .nest(\"/auth\", create_auth_routes())\n        \n        // User management routes\n        .nest(\"/users\", create_user_routes())\n        \n        // Project management routes\n        .nest(\"/projects\", create_project_routes())\n        \n        // Template management routes\n        .nest(\"/templates\", create_template_routes())\n}\n\n/// Create authentication routes\nfn create_auth_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/signin\", post(handlers::auth::signin))\n        .route(\"/signout\", post(handlers::auth::signout))\n        .route(\"/refresh\", post(handlers::auth::refresh))\n}\n\n/// Create user management routes\nfn create_user_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(handlers::users::list_users))\n        .route(\"/\", post(handlers::users::create_user))\n        .route(\"/:id\", get(handlers::users::get_user))\n        .route(\"/:id\", post(handlers::users::update_user))\n        .route(\"/:id\", post(handlers::users::delete_user))\n}\n\n/// Create project management routes\nfn create_project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(handlers::projects::list_projects))\n        .route(\"/\", post(handlers::projects::create_project))\n        .route(\"/:id\", get(handlers::projects::get_project))\n        .route(\"/:id\", post(handlers::projects::update_project))\n        .route(\"/:id\", post(handlers::projects::delete_project))\n        .route(\"/:id/data\", post(handlers::projects::save_project_data))\n        .route(\"/:id/data/:version\", get(handlers::projects::get_project_data))\n}\n\n/// Create template management routes\nfn create_template_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(handlers::templates::list_templates))\n        .route(\"/:id\", get(handlers::templates::get_template))\n        .route(\"/:id/schema\", get(handlers::templates::get_template_schema))\n}\n", "modifiedCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::*;\npub use middleware::*;\npub use handlers::*;\npub use dto::*;\npub use state::*;\n\nuse axum::{\n    routing::{get, post},\n    Router,\n};\nuse tower::ServiceBuilder;\nuse tower_http::{\n    cors::CorsLayer,\n    trace::TraceLayer,\n    limit::RequestBodyLimitLayer,\n};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(logging_layer())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n\n"}