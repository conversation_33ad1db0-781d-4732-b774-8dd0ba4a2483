{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/main.rs"}, "originalCode": "use anyhow::Result;\nuse dotenvy::dotenv;\nuse excelsync_api::create_app;\nuse excelsync_auth::AuthService;\nuse excelsync_core::config::Config;\nuse excelsync_database::DatabaseConnection;\nuse std::sync::Arc;\nuse tokio::net::TcpListener;\nuse tracing::{info, warn};\nuse tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};\n\n#[tokio::main]\nasync fn main() -> Result<()> {\n    // Load environment variables\n    if let Err(e) = dotenv() {\n        warn!(\"Failed to load .env file: {}\", e);\n    }\n\n    // Initialize tracing\n    tracing_subscriber::registry()\n        .with(\n            tracing_subscriber::EnvFilter::try_from_default_env()\n                .unwrap_or_else(|_| \"excelsync_backend=debug,tower_http=debug\".into()),\n        )\n        .with(tracing_subscriber::fmt::layer())\n        .init();\n\n    // Load configuration\n    let config = Config::load()?;\n    config.validate()?;\n\n    info!(\"Starting ExcelSync Backend Server\");\n    info!(\"Configuration loaded successfully\");\n\n    // Initialize database connection\n    let db = DatabaseConnection::new(&config.database.url).await?;\n    info!(\"Database connection established\");\n\n    // Initialize authentication service\n    let auth_service = Arc::new(AuthService::new(config.security.jwt_secret.clone()));\n    info!(\"Authentication service initialized\");\n\n    // Create application state\n    let app_state = excelsync_api::AppState {\n        auth_service,\n        db: Arc::new(db),\n        config: Arc::new(config.clone()),\n    };\n\n    // Create application with routes\n    let app = create_app(app_state);\n\n    // Start server\n    let addr = format!(\"{}:{}\", config.server.host, config.server.port);\n    let listener = TcpListener::bind(&addr).await?;\n    \n    info!(\"ExcelSync Backend Server listening on {}\", addr);\n    info!(\"Health check available at: http://{}/health\", addr);\n    info!(\"API documentation available at: http://{}/docs\", addr);\n\n    axum::serve(listener, app).await?;\n\n    Ok(())\n}\n", "modifiedCode": "use anyhow::Result;\nuse dotenvy::dotenv;\nuse api::create_app;\nuse auth::AuthService;\nuse core::config::Config;\nuse database::DatabaseConnection;\nuse std::sync::Arc;\nuse tokio::net::TcpListener;\nuse tracing::{info, warn};\nuse tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};\n\n#[tokio::main]\nasync fn main() -> Result<()> {\n    // Load environment variables\n    if let Err(e) = dotenv() {\n        warn!(\"Failed to load .env file: {}\", e);\n    }\n\n    // Initialize tracing\n    tracing_subscriber::registry()\n        .with(\n            tracing_subscriber::EnvFilter::try_from_default_env()\n                .unwrap_or_else(|_| \"excelsync_backend=debug,tower_http=debug\".into()),\n        )\n        .with(tracing_subscriber::fmt::layer())\n        .init();\n\n    // Load configuration\n    let config = Config::load()?;\n    config.validate()?;\n\n    info!(\"Starting ExcelSync Backend Server\");\n    info!(\"Configuration loaded successfully\");\n\n    // Initialize database connection\n    let db = DatabaseConnection::new(&config.database.url).await?;\n    info!(\"Database connection established\");\n\n    // Initialize authentication service\n    let auth_service = Arc::new(AuthService::new(config.security.jwt_secret.clone()));\n    info!(\"Authentication service initialized\");\n\n    // Create application state\n    let app_state = excelsync_api::AppState {\n        auth_service,\n        db: Arc::new(db),\n        config: Arc::new(config.clone()),\n    };\n\n    // Create application with routes\n    let app = create_app(app_state);\n\n    // Start server\n    let addr = format!(\"{}:{}\", config.server.host, config.server.port);\n    let listener = TcpListener::bind(&addr).await?;\n    \n    info!(\"ExcelSync Backend Server listening on {}\", addr);\n    info!(\"Health check available at: http://{}/health\", addr);\n    info!(\"API documentation available at: http://{}/docs\", addr);\n\n    axum::serve(listener, app).await?;\n\n    Ok(())\n}\n"}