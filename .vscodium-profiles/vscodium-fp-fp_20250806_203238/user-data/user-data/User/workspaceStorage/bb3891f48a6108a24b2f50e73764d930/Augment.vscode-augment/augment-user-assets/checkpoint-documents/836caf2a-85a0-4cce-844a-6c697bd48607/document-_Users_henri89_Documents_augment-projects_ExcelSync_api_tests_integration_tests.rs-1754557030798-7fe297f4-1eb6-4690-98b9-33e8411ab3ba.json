{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/tests/integration_tests.rs"}, "originalCode": "use axum::{\n    body::Body,\n    http::{Request, StatusCode},\n    Router,\n};\nuse serde_json::{json, Value};\nuse std::sync::Arc;\nuse tower::ServiceExt;\nuse uuid::Uuid;\n\n// Import the API modules\nuse api::{create_app, AppState};\nuse auth::AuthService;\nuse database::{DatabaseConnection, UserRole};\nuse core::config::Config;\nuse core::encryption::EncryptionService;\n\n/// Helper function to create a test app with mock dependencies\nasync fn create_test_app() -> Router {\n    // For now, we'll skip the actual app creation since it requires database setup\n    // In a real test environment, you would set up a test database\n    Router::new()\n}\n\n/// Helper function to create a test JWT token\nfn create_test_jwt_token() -> String {\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\".to_string());\n    let user_id = Uuid::new_v4();\n\n    auth_service.generate_token(user_id, \"<EMAIL>\".to_string(), UserRole::Admin, Some(Uuid::new_v4()))\n        .expect(\"Failed to generate test token\")\n}\n\n/// Helper function to make authenticated requests\nasync fn make_authenticated_request(\n    app: &Router,\n    method: &str,\n    path: &str,\n    body: Option<Value>,\n) -> (StatusCode, Value) {\n    let token = create_test_jwt_token();\n    \n    let request_builder = Request::builder()\n        .method(method)\n        .uri(path)\n        .header(\"Authorization\", format!(\"Bearer {}\", token))\n        .header(\"Content-Type\", \"application/json\");\n    \n    let request = if let Some(body) = body {\n        request_builder.body(Body::from(body.to_string())).unwrap()\n    } else {\n        request_builder.body(Body::empty()).unwrap()\n    };\n    \n    let response = app.clone().oneshot(request).await.unwrap();\n    let status = response.status();\n    \n    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();\n    let body_str = String::from_utf8(body_bytes.to_vec()).unwrap_or_default();\n    let body_json: Value = serde_json::from_str(&body_str).unwrap_or(json!({}));\n    \n    (status, body_json)\n}\n\n#[tokio::test]\nasync fn test_integration_framework() {\n    // Simple test to verify the integration test framework is working\n    let app = create_test_app().await;\n\n    // Test that we can create a router\n    assert!(true);\n}\n\n#[tokio::test]\nasync fn test_jwt_token_creation() {\n    // Test JWT token creation functionality\n    let token = create_test_jwt_token();\n    assert!(!token.is_empty());\n    assert!(token.contains('.'));  // JWT tokens have dots\n}\n\n#[tokio::test]\nasync fn test_auth_service_functionality() {\n    // Test that the auth service can be created and used\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\".to_string());\n    let user_id = Uuid::new_v4();\n\n    // Test token generation\n    let token = auth_service.generate_token(\n        user_id,\n        \"<EMAIL>\".to_string(),\n        UserRole::Admin,\n        Some(Uuid::new_v4())\n    );\n\n    assert!(token.is_ok());\n    let token = token.unwrap();\n    assert!(!token.is_empty());\n\n    // Test token validation\n    let validation_result = auth_service.validate_token(&token);\n    assert!(validation_result.is_ok());\n}\n", "modifiedCode": "use axum::{\n    body::Body,\n    http::{Request, StatusCode},\n    Router,\n};\nuse serde_json::{json, Value};\nuse std::sync::Arc;\nuse tower::ServiceExt;\nuse uuid::Uuid;\n\n// Import the API modules\nuse auth::AuthService;\nuse database::UserRole;\n\n/// Helper function to create a test app with mock dependencies\nasync fn create_test_app() -> Router {\n    // For now, we'll skip the actual app creation since it requires database setup\n    // In a real test environment, you would set up a test database\n    Router::new()\n}\n\n/// Helper function to create a test JWT token\nfn create_test_jwt_token() -> String {\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\".to_string());\n    let user_id = Uuid::new_v4();\n\n    auth_service.generate_token(user_id, \"<EMAIL>\".to_string(), UserRole::Admin, Some(Uuid::new_v4()))\n        .expect(\"Failed to generate test token\")\n}\n\n/// Helper function to make authenticated requests\nasync fn make_authenticated_request(\n    app: &Router,\n    method: &str,\n    path: &str,\n    body: Option<Value>,\n) -> (StatusCode, Value) {\n    let token = create_test_jwt_token();\n    \n    let request_builder = Request::builder()\n        .method(method)\n        .uri(path)\n        .header(\"Authorization\", format!(\"Bearer {}\", token))\n        .header(\"Content-Type\", \"application/json\");\n    \n    let request = if let Some(body) = body {\n        request_builder.body(Body::from(body.to_string())).unwrap()\n    } else {\n        request_builder.body(Body::empty()).unwrap()\n    };\n    \n    let response = app.clone().oneshot(request).await.unwrap();\n    let status = response.status();\n    \n    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();\n    let body_str = String::from_utf8(body_bytes.to_vec()).unwrap_or_default();\n    let body_json: Value = serde_json::from_str(&body_str).unwrap_or(json!({}));\n    \n    (status, body_json)\n}\n\n#[tokio::test]\nasync fn test_integration_framework() {\n    // Simple test to verify the integration test framework is working\n    let app = create_test_app().await;\n\n    // Test that we can create a router\n    assert!(true);\n}\n\n#[tokio::test]\nasync fn test_jwt_token_creation() {\n    // Test JWT token creation functionality\n    let token = create_test_jwt_token();\n    assert!(!token.is_empty());\n    assert!(token.contains('.'));  // JWT tokens have dots\n}\n\n#[tokio::test]\nasync fn test_auth_service_functionality() {\n    // Test that the auth service can be created and used\n    let auth_service = AuthService::new(\"test-secret-key-32-chars-long!!\".to_string());\n    let user_id = Uuid::new_v4();\n\n    // Test token generation\n    let token = auth_service.generate_token(\n        user_id,\n        \"<EMAIL>\".to_string(),\n        UserRole::Admin,\n        Some(Uuid::new_v4())\n    );\n\n    assert!(token.is_ok());\n    let token = token.unwrap();\n    assert!(!token.is_empty());\n\n    // Test token validation\n    let validation_result = auth_service.validate_token(&token);\n    assert!(validation_result.is_ok());\n}\n"}