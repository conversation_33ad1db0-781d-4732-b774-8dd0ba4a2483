{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/validation.rs"}, "originalCode": "use axum::{\n    extract::Request,\n    http::{HeaderMap, StatusCode},\n    middleware::Next,\n    response::Response,\n};\nuse std::collections::HashSet;\n\n/// Input validation middleware\npub async fn input_validation_middleware(\n    headers: HeaderMap,\n    mut request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Validate Content-Type for POST/PUT requests\n    if let Some(method) = request.method().as_str().get(0..4) {\n        if method == \"POST\" || method == \"PUT \" {\n            if let Some(content_type) = headers.get(\"content-type\") {\n                let content_type_str = content_type.to_str().unwrap_or(\"\");\n                if !is_valid_content_type(content_type_str) {\n                    return Err(StatusCode::UNSUPPORTED_MEDIA_TYPE);\n                }\n            } else {\n                return Err(StatusCode::BAD_REQUEST);\n            }\n        }\n    }\n\n    // Validate Content-Length\n    if let Some(content_length) = headers.get(\"content-length\") {\n        if let Ok(length_str) = content_length.to_str() {\n            if let Ok(length) = length_str.parse::<usize>() {\n                if length > 10_000_000 { // 10MB limit\n                    return Err(StatusCode::PAYLOAD_TOO_LARGE);\n                }\n            }\n        }\n    }\n\n    // Validate User-Agent (basic bot detection)\n    if let Some(user_agent) = headers.get(\"user-agent\") {\n        let user_agent_str = user_agent.to_str().unwrap_or(\"\");\n        if is_suspicious_user_agent(user_agent_str) {\n            return Err(StatusCode::FORBIDDEN);\n        }\n    }\n\n    // Validate common headers for security\n    validate_security_headers(&headers)?;\n\n    Ok(next.run(request).await)\n}\n\n/// Check if content type is valid\nfn is_valid_content_type(content_type: &str) -> bool {\n    let valid_types = [\n        \"application/json\",\n        \"application/x-www-form-urlencoded\",\n        \"multipart/form-data\",\n        \"text/plain\",\n    ];\n\n    valid_types.iter().any(|&valid_type| content_type.starts_with(valid_type))\n}\n\n/// Check for suspicious user agents\nfn is_suspicious_user_agent(user_agent: &str) -> bool {\n    let suspicious_patterns = [\n        \"bot\",\n        \"crawler\",\n        \"spider\",\n        \"scraper\",\n        \"curl\",\n        \"wget\",\n        \"python-requests\",\n        \"postman\",\n    ];\n\n    let user_agent_lower = user_agent.to_lowercase();\n    \n    // Allow legitimate browsers and tools\n    if user_agent_lower.contains(\"mozilla\") || \n       user_agent_lower.contains(\"chrome\") || \n       user_agent_lower.contains(\"safari\") ||\n       user_agent_lower.contains(\"firefox\") ||\n       user_agent_lower.contains(\"edge\") {\n        return false;\n    }\n\n    suspicious_patterns.iter().any(|&pattern| user_agent_lower.contains(pattern))\n}\n\n/// Validate security headers\nfn validate_security_headers(headers: &HeaderMap) -> Result<(), StatusCode> {\n    // Check for potential XSS attempts in headers\n    for (name, value) in headers.iter() {\n        let value_str = value.to_str().unwrap_or(\"\");\n        if contains_xss_patterns(value_str) {\n            return Err(StatusCode::BAD_REQUEST);\n        }\n    }\n\n    // Validate Origin header if present\n    if let Some(origin) = headers.get(\"origin\") {\n        let origin_str = origin.to_str().unwrap_or(\"\");\n        if !is_valid_origin(origin_str) {\n            return Err(StatusCode::FORBIDDEN);\n        }\n    }\n\n    Ok(())\n}\n\n/// Check for XSS patterns in header values\nfn contains_xss_patterns(value: &str) -> bool {\n    let xss_patterns = [\n        \"<script\",\n        \"javascript:\",\n        \"vbscript:\",\n        \"onload=\",\n        \"onerror=\",\n        \"onclick=\",\n        \"eval(\",\n        \"expression(\",\n    ];\n\n    let value_lower = value.to_lowercase();\n    xss_patterns.iter().any(|&pattern| value_lower.contains(pattern))\n}\n\n/// Validate origin header\nfn is_valid_origin(origin: &str) -> bool {\n    // Allow localhost for development\n    if origin.starts_with(\"http://localhost\") || origin.starts_with(\"https://localhost\") {\n        return true;\n    }\n\n    // Add your allowed origins here\n    let allowed_origins = [\n        \"https://excelsync.com\",\n        \"https://app.excelsync.com\",\n        \"https://admin.excelsync.com\",\n    ];\n\n    allowed_origins.iter().any(|&allowed| origin == allowed)\n}\n\n/// SQL injection detection middleware\npub async fn sql_injection_protection_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Extract query parameters and check for SQL injection patterns\n    let query = request.uri().query().unwrap_or(\"\");\n    \n    if contains_sql_injection_patterns(query) {\n        return Err(StatusCode::BAD_REQUEST);\n    }\n\n    Ok(next.run(request).await)\n}\n\n/// Check for SQL injection patterns\nfn contains_sql_injection_patterns(input: &str) -> bool {\n    let sql_patterns = [\n        \"union select\",\n        \"drop table\",\n        \"delete from\",\n        \"insert into\",\n        \"update set\",\n        \"exec(\",\n        \"execute(\",\n        \"sp_\",\n        \"xp_\",\n        \"--\",\n        \"/*\",\n        \"*/\",\n        \"char(\",\n        \"ascii(\",\n        \"substring(\",\n        \"waitfor delay\",\n    ];\n\n    let input_lower = input.to_lowercase();\n    sql_patterns.iter().any(|&pattern| input_lower.contains(pattern))\n}\n\n/// Path traversal protection middleware\npub async fn path_traversal_protection_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    let path = request.uri().path();\n    \n    if contains_path_traversal_patterns(path) {\n        return Err(StatusCode::BAD_REQUEST);\n    }\n\n    Ok(next.run(request).await)\n}\n\n/// Check for path traversal patterns\nfn contains_path_traversal_patterns(path: &str) -> bool {\n    let traversal_patterns = [\n        \"../\",\n        \"..\\\\\",\n        \"%2e%2e%2f\",\n        \"%2e%2e%5c\",\n        \"..%2f\",\n        \"..%5c\",\n        \"%252e%252e%252f\",\n    ];\n\n    let path_lower = path.to_lowercase();\n    traversal_patterns.iter().any(|&pattern| path_lower.contains(pattern))\n}\n\n/// Request size validation middleware\npub async fn request_size_validation_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // This is handled by tower-http RequestBodyLimitLayer in most cases\n    // But we can add additional custom logic here if needed\n    \n    Ok(next.run(request).await)\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_valid_content_types() {\n        assert!(is_valid_content_type(\"application/json\"));\n        assert!(is_valid_content_type(\"application/json; charset=utf-8\"));\n        assert!(is_valid_content_type(\"multipart/form-data; boundary=something\"));\n        assert!(!is_valid_content_type(\"application/xml\"));\n        assert!(!is_valid_content_type(\"text/html\"));\n    }\n\n    #[test]\n    fn test_suspicious_user_agents() {\n        assert!(is_suspicious_user_agent(\"python-requests/2.25.1\"));\n        assert!(is_suspicious_user_agent(\"curl/7.68.0\"));\n        assert!(is_suspicious_user_agent(\"Googlebot/2.1\"));\n        assert!(!is_suspicious_user_agent(\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"));\n        assert!(!is_suspicious_user_agent(\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"));\n    }\n\n    #[test]\n    fn test_xss_detection() {\n        assert!(contains_xss_patterns(\"<script>alert('xss')</script>\"));\n        assert!(contains_xss_patterns(\"javascript:alert(1)\"));\n        assert!(contains_xss_patterns(\"onload=alert(1)\"));\n        assert!(!contains_xss_patterns(\"normal text\"));\n        assert!(!contains_xss_patterns(\"<EMAIL>\"));\n    }\n\n    #[test]\n    fn test_sql_injection_detection() {\n        assert!(contains_sql_injection_patterns(\"1' UNION SELECT * FROM users--\"));\n        assert!(contains_sql_injection_patterns(\"'; DROP TABLE users; --\"));\n        assert!(contains_sql_injection_patterns(\"1 OR 1=1\"));\n        assert!(!contains_sql_injection_patterns(\"normal query\"));\n        assert!(!contains_sql_injection_patterns(\"<EMAIL>\"));\n    }\n\n    #[test]\n    fn test_path_traversal_detection() {\n        assert!(contains_path_traversal_patterns(\"../../../etc/passwd\"));\n        assert!(contains_path_traversal_patterns(\"..\\\\..\\\\windows\\\\system32\"));\n        assert!(contains_path_traversal_patterns(\"%2e%2e%2f\"));\n        assert!(!contains_path_traversal_patterns(\"/api/v1/users\"));\n        assert!(!contains_path_traversal_detection(\"/normal/path\"));\n    }\n}\n", "modifiedCode": "use axum::{\n    extract::Request,\n    http::{HeaderMap, StatusCode},\n    middleware::Next,\n    response::Response,\n};\nuse std::collections::HashSet;\n\n/// Input validation middleware\npub async fn input_validation_middleware(\n    headers: HeaderMap,\n    mut request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Validate Content-Type for POST/PUT requests\n    if let Some(method) = request.method().as_str().get(0..4) {\n        if method == \"POST\" || method == \"PUT \" {\n            if let Some(content_type) = headers.get(\"content-type\") {\n                let content_type_str = content_type.to_str().unwrap_or(\"\");\n                if !is_valid_content_type(content_type_str) {\n                    return Err(StatusCode::UNSUPPORTED_MEDIA_TYPE);\n                }\n            } else {\n                return Err(StatusCode::BAD_REQUEST);\n            }\n        }\n    }\n\n    // Validate Content-Length\n    if let Some(content_length) = headers.get(\"content-length\") {\n        if let Ok(length_str) = content_length.to_str() {\n            if let Ok(length) = length_str.parse::<usize>() {\n                if length > 10_000_000 { // 10MB limit\n                    return Err(StatusCode::PAYLOAD_TOO_LARGE);\n                }\n            }\n        }\n    }\n\n    // Validate User-Agent (basic bot detection)\n    if let Some(user_agent) = headers.get(\"user-agent\") {\n        let user_agent_str = user_agent.to_str().unwrap_or(\"\");\n        if is_suspicious_user_agent(user_agent_str) {\n            return Err(StatusCode::FORBIDDEN);\n        }\n    }\n\n    // Validate common headers for security\n    validate_security_headers(&headers)?;\n\n    Ok(next.run(request).await)\n}\n\n/// Check if content type is valid\nfn is_valid_content_type(content_type: &str) -> bool {\n    let valid_types = [\n        \"application/json\",\n        \"application/x-www-form-urlencoded\",\n        \"multipart/form-data\",\n        \"text/plain\",\n    ];\n\n    valid_types.iter().any(|&valid_type| content_type.starts_with(valid_type))\n}\n\n/// Check for suspicious user agents\nfn is_suspicious_user_agent(user_agent: &str) -> bool {\n    let suspicious_patterns = [\n        \"bot\",\n        \"crawler\",\n        \"spider\",\n        \"scraper\",\n        \"curl\",\n        \"wget\",\n        \"python-requests\",\n        \"postman\",\n    ];\n\n    let user_agent_lower = user_agent.to_lowercase();\n    \n    // Allow legitimate browsers and tools\n    if user_agent_lower.contains(\"mozilla\") || \n       user_agent_lower.contains(\"chrome\") || \n       user_agent_lower.contains(\"safari\") ||\n       user_agent_lower.contains(\"firefox\") ||\n       user_agent_lower.contains(\"edge\") {\n        return false;\n    }\n\n    suspicious_patterns.iter().any(|&pattern| user_agent_lower.contains(pattern))\n}\n\n/// Validate security headers\nfn validate_security_headers(headers: &HeaderMap) -> Result<(), StatusCode> {\n    // Check for potential XSS attempts in headers\n    for (name, value) in headers.iter() {\n        let value_str = value.to_str().unwrap_or(\"\");\n        if contains_xss_patterns(value_str) {\n            return Err(StatusCode::BAD_REQUEST);\n        }\n    }\n\n    // Validate Origin header if present\n    if let Some(origin) = headers.get(\"origin\") {\n        let origin_str = origin.to_str().unwrap_or(\"\");\n        if !is_valid_origin(origin_str) {\n            return Err(StatusCode::FORBIDDEN);\n        }\n    }\n\n    Ok(())\n}\n\n/// Check for XSS patterns in header values\nfn contains_xss_patterns(value: &str) -> bool {\n    let xss_patterns = [\n        \"<script\",\n        \"javascript:\",\n        \"vbscript:\",\n        \"onload=\",\n        \"onerror=\",\n        \"onclick=\",\n        \"eval(\",\n        \"expression(\",\n    ];\n\n    let value_lower = value.to_lowercase();\n    xss_patterns.iter().any(|&pattern| value_lower.contains(pattern))\n}\n\n/// Validate origin header\nfn is_valid_origin(origin: &str) -> bool {\n    // Allow localhost for development\n    if origin.starts_with(\"http://localhost\") || origin.starts_with(\"https://localhost\") {\n        return true;\n    }\n\n    // Add your allowed origins here\n    let allowed_origins = [\n        \"https://excelsync.com\",\n        \"https://app.excelsync.com\",\n        \"https://admin.excelsync.com\",\n    ];\n\n    allowed_origins.iter().any(|&allowed| origin == allowed)\n}\n\n/// SQL injection detection middleware\npub async fn sql_injection_protection_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // Extract query parameters and check for SQL injection patterns\n    let query = request.uri().query().unwrap_or(\"\");\n    \n    if contains_sql_injection_patterns(query) {\n        return Err(StatusCode::BAD_REQUEST);\n    }\n\n    Ok(next.run(request).await)\n}\n\n/// Check for SQL injection patterns\nfn contains_sql_injection_patterns(input: &str) -> bool {\n    let sql_patterns = [\n        \"union select\",\n        \"drop table\",\n        \"delete from\",\n        \"insert into\",\n        \"update set\",\n        \"exec(\",\n        \"execute(\",\n        \"sp_\",\n        \"xp_\",\n        \"--\",\n        \"/*\",\n        \"*/\",\n        \"char(\",\n        \"ascii(\",\n        \"substring(\",\n        \"waitfor delay\",\n    ];\n\n    let input_lower = input.to_lowercase();\n    sql_patterns.iter().any(|&pattern| input_lower.contains(pattern))\n}\n\n/// Path traversal protection middleware\npub async fn path_traversal_protection_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    let path = request.uri().path();\n    \n    if contains_path_traversal_patterns(path) {\n        return Err(StatusCode::BAD_REQUEST);\n    }\n\n    Ok(next.run(request).await)\n}\n\n/// Check for path traversal patterns\nfn contains_path_traversal_patterns(path: &str) -> bool {\n    let traversal_patterns = [\n        \"../\",\n        \"..\\\\\",\n        \"%2e%2e%2f\",\n        \"%2e%2e%5c\",\n        \"..%2f\",\n        \"..%5c\",\n        \"%252e%252e%252f\",\n    ];\n\n    let path_lower = path.to_lowercase();\n    traversal_patterns.iter().any(|&pattern| path_lower.contains(pattern))\n}\n\n/// Request size validation middleware\npub async fn request_size_validation_middleware(\n    request: Request,\n    next: Next,\n) -> Result<Response, StatusCode> {\n    // This is handled by tower-http RequestBodyLimitLayer in most cases\n    // But we can add additional custom logic here if needed\n    \n    Ok(next.run(request).await)\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_valid_content_types() {\n        assert!(is_valid_content_type(\"application/json\"));\n        assert!(is_valid_content_type(\"application/json; charset=utf-8\"));\n        assert!(is_valid_content_type(\"multipart/form-data; boundary=something\"));\n        assert!(!is_valid_content_type(\"application/xml\"));\n        assert!(!is_valid_content_type(\"text/html\"));\n    }\n\n    #[test]\n    fn test_suspicious_user_agents() {\n        assert!(is_suspicious_user_agent(\"python-requests/2.25.1\"));\n        assert!(is_suspicious_user_agent(\"curl/7.68.0\"));\n        assert!(is_suspicious_user_agent(\"Googlebot/2.1\"));\n        assert!(!is_suspicious_user_agent(\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"));\n        assert!(!is_suspicious_user_agent(\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"));\n    }\n\n    #[test]\n    fn test_xss_detection() {\n        assert!(contains_xss_patterns(\"<script>alert('xss')</script>\"));\n        assert!(contains_xss_patterns(\"javascript:alert(1)\"));\n        assert!(contains_xss_patterns(\"onload=alert(1)\"));\n        assert!(!contains_xss_patterns(\"normal text\"));\n        assert!(!contains_xss_patterns(\"<EMAIL>\"));\n    }\n\n    #[test]\n    fn test_sql_injection_detection() {\n        assert!(contains_sql_injection_patterns(\"1' UNION SELECT * FROM users--\"));\n        assert!(contains_sql_injection_patterns(\"'; DROP TABLE users; --\"));\n        assert!(contains_sql_injection_patterns(\"1 OR 1=1\"));\n        assert!(!contains_sql_injection_patterns(\"normal query\"));\n        assert!(!contains_sql_injection_patterns(\"<EMAIL>\"));\n    }\n\n    #[test]\n    fn test_path_traversal_detection() {\n        assert!(contains_path_traversal_patterns(\"../../../etc/passwd\"));\n        assert!(contains_path_traversal_patterns(\"..\\\\..\\\\windows\\\\system32\"));\n        assert!(contains_path_traversal_patterns(\"%2e%2e%2f\"));\n        assert!(!contains_path_traversal_patterns(\"/api/v1/users\"));\n        assert!(!contains_path_traversal_patterns(\"/normal/path\"));\n    }\n}\n"}