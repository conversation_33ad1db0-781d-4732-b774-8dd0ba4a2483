{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/health.rs"}, "modifiedCode": "use axum::{extract::State, http::StatusCode, response::Json};\nuse serde_json::{json, Value};\nuse crate::AppState;\n\n/// Health check endpoint\npub async fn health_check(State(state): State<AppState>) -> Result<Json<Value>, StatusCode> {\n    // Test database connection\n    match database::health_check(state.db.get_connection()).await {\n        Ok(_) => Ok(Json(json!({\n            \"status\": \"healthy\",\n            \"database\": \"connected\",\n            \"timestamp\": chrono::Utc::now().to_rfc3339()\n        }))),\n        Err(_) => Err(StatusCode::SERVICE_UNAVAILABLE),\n    }\n}\n"}