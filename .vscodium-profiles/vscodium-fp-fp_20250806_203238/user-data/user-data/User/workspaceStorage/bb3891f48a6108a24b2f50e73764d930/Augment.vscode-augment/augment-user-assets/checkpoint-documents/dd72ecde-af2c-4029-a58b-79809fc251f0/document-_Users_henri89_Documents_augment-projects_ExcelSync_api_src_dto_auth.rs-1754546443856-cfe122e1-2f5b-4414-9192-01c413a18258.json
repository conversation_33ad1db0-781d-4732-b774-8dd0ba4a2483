{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/auth.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Login request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct LoginRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n}\n\n/// Login response DTO\n#[derive(Debug, Serialize)]\npub struct LoginResponse {\n    pub token: String,\n    pub expires_at: String,\n    pub user: UserInfo,\n}\n\n/// User info in auth response\n#[derive(Debug, Serialize)]\npub struct UserInfo {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// Token refresh request DTO\n#[derive(Debug, Deserialize)]\npub struct RefreshTokenRequest {\n    pub token: String,\n}\n\n/// Token refresh response DTO\n#[derive(Debug, Serialize)]\npub struct RefreshTokenResponse {\n    pub token: String,\n    pub expires_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Login request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct LoginRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n}\n\n/// Login response DTO\n#[derive(Debug, Serialize)]\npub struct LoginResponse {\n    pub token: String,\n    pub expires_at: String,\n    pub user: UserInfo,\n}\n\n/// User info in auth response\n#[derive(Debug, Serialize)]\npub struct UserInfo {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// Token refresh request DTO\n#[derive(Debug, Deserialize)]\npub struct RefreshTokenRequest {\n    pub token: String,\n}\n\n/// Token refresh response DTO\n#[derive(Debug, Serialize)]\npub struct RefreshTokenResponse {\n    pub token: String,\n    pub expires_at: String,\n}\n\n/// Register request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct RegisterRequest {\n    #[validate(email)]\n    pub email: String,\n\n    #[validate(length(min = 8, max = 128))]\n    pub password: String,\n\n    #[validate(length(min = 2, max = 255))]\n    pub full_name: String,\n\n    pub organization_name: Option<String>,\n}\n\n/// Token response DTO (generic)\n#[derive(Debug, Serialize)]\npub struct TokenResponse {\n    pub access_token: String,\n    pub refresh_token: Option<String>,\n    pub token_type: String,\n    pub expires_in: u64,\n}\n"}