{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/financial.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\nuse rust_decimal::Decimal;\n\n/// Financial calculation request\n#[derive(Debug, Deserialize, Validate)]\npub struct FinancialCalculationRequest {\n    pub project_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub input_data: FinancialInputData,\n}\n\n/// Types of financial calculations\n#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum FinancialCalculationType {\n    TaxCalculation,\n    RoiAnalysis,\n    InvestmentAnalysis,\n    CashFlowProjection,\n    BreakEvenAnalysis,\n    ProfitabilityAnalysis,\n}\n\n/// Financial input data\n#[derive(Debug, Deserialize, Validate)]\npub struct FinancialInputData {\n    #[validate(range(min = 0.0))]\n    pub total_investment: f64,\n    \n    #[validate(range(min = 0.0))]\n    pub expected_revenue: f64,\n    \n    #[validate(range(min = 0.0))]\n    pub land_area: Option<f64>,\n    \n    #[validate(range(min = 0.0))]\n    pub construction_area: Option<f64>,\n    \n    #[validate(range(min = 1, max = 600))]\n    pub project_duration_months: u32,\n    \n    pub land_location: Option<String>,\n    pub land_use_type: Option<String>,\n    \n    /// Additional financial parameters as key-value pairs\n    pub additional_data: Option<Value>,\n}\n\n/// Financial calculation response\n#[derive(Debug, Serialize)]\npub struct FinancialCalculationResponse {\n    pub calculation_id: String,\n    pub project_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub results: FinancialResults,\n    pub calculated_at: String,\n    pub valid_until: Option<String>,\n}\n\n/// Financial calculation results\n#[derive(Debug, Serialize)]\npub struct FinancialResults {\n    pub tax_calculations: Option<TaxCalculationResults>,\n    pub roi_analysis: Option<RoiAnalysisResults>,\n    pub investment_analysis: Option<InvestmentAnalysisResults>,\n    pub cash_flow: Option<CashFlowResults>,\n    pub break_even: Option<BreakEvenResults>,\n    pub profitability: Option<ProfitabilityResults>,\n    pub summary: FinancialSummary,\n}\n\n/// Vietnamese tax calculation results\n#[derive(Debug, Serialize)]\npub struct TaxCalculationResults {\n    pub vat_amount: String,\n    pub vat_rate: String,\n    pub corporate_tax: String,\n    pub corporate_tax_rate: String,\n    pub land_use_tax: Option<String>,\n    pub construction_permit_fees: Option<String>,\n    pub total_tax_obligations: String,\n    pub tax_breakdown: Vec<TaxBreakdownItem>,\n}\n\n/// Tax breakdown item\n#[derive(Debug, Serialize)]\npub struct TaxBreakdownItem {\n    pub tax_type: String,\n    pub amount: String,\n    pub rate: String,\n    pub description: String,\n}\n\n/// ROI analysis results\n#[derive(Debug, Serialize)]\npub struct RoiAnalysisResults {\n    pub roi_percentage: String,\n    pub payback_period_months: u32,\n    pub net_present_value: String,\n    pub internal_rate_of_return: String,\n    pub profitability_index: String,\n    pub risk_assessment: RiskAssessment,\n}\n\n/// Risk assessment\n#[derive(Debug, Serialize)]\npub struct RiskAssessment {\n    pub risk_level: RiskLevel,\n    pub risk_factors: Vec<String>,\n    pub mitigation_suggestions: Vec<String>,\n}\n\n/// Risk level enum\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum RiskLevel {\n    Low,\n    Medium,\n    High,\n    Critical,\n}\n\n/// Investment analysis results\n#[derive(Debug, Serialize)]\npub struct InvestmentAnalysisResults {\n    pub total_cost_breakdown: CostBreakdown,\n    pub revenue_projections: RevenueProjections,\n    pub sensitivity_analysis: SensitivityAnalysis,\n    pub scenario_analysis: Vec<ScenarioResult>,\n}\n\n/// Cost breakdown\n#[derive(Debug, Serialize)]\npub struct CostBreakdown {\n    pub land_acquisition: String,\n    pub construction_costs: String,\n    pub permits_and_fees: String,\n    pub financing_costs: String,\n    pub marketing_costs: String,\n    pub contingency: String,\n    pub total: String,\n}\n\n/// Revenue projections\n#[derive(Debug, Serialize)]\npub struct RevenueProjections {\n    pub sales_revenue: String,\n    pub rental_income: Option<String>,\n    pub other_income: Option<String>,\n    pub total_revenue: String,\n    pub revenue_timeline: Vec<RevenueTimelineItem>,\n}\n\n/// Revenue timeline item\n#[derive(Debug, Serialize)]\npub struct RevenueTimelineItem {\n    pub period: String,\n    pub amount: String,\n    pub cumulative: String,\n}\n\n/// Sensitivity analysis\n#[derive(Debug, Serialize)]\npub struct SensitivityAnalysis {\n    pub price_sensitivity: SensitivityResult,\n    pub cost_sensitivity: SensitivityResult,\n    pub timeline_sensitivity: SensitivityResult,\n}\n\n/// Sensitivity result\n#[derive(Debug, Serialize)]\npub struct SensitivityResult {\n    pub variable: String,\n    pub base_case: String,\n    pub optimistic: String,\n    pub pessimistic: String,\n    pub impact_on_roi: String,\n}\n\n/// Scenario analysis result\n#[derive(Debug, Serialize)]\npub struct ScenarioResult {\n    pub scenario_name: String,\n    pub probability: String,\n    pub roi: String,\n    pub npv: String,\n    pub description: String,\n}\n\n/// Cash flow results\n#[derive(Debug, Serialize)]\npub struct CashFlowResults {\n    pub monthly_cash_flow: Vec<CashFlowItem>,\n    pub cumulative_cash_flow: Vec<CashFlowItem>,\n    pub peak_funding_requirement: String,\n    pub cash_flow_summary: CashFlowSummary,\n}\n\n/// Cash flow item\n#[derive(Debug, Serialize)]\npub struct CashFlowItem {\n    pub period: String,\n    pub inflow: String,\n    pub outflow: String,\n    pub net_flow: String,\n}\n\n/// Cash flow summary\n#[derive(Debug, Serialize)]\npub struct CashFlowSummary {\n    pub total_inflow: String,\n    pub total_outflow: String,\n    pub net_cash_flow: String,\n    pub break_even_month: Option<u32>,\n}\n\n/// Break-even analysis results\n#[derive(Debug, Serialize)]\npub struct BreakEvenResults {\n    pub break_even_point: String,\n    pub break_even_timeline: String,\n    pub margin_of_safety: String,\n    pub contribution_margin: String,\n}\n\n/// Profitability analysis results\n#[derive(Debug, Serialize)]\npub struct ProfitabilityResults {\n    pub gross_profit: String,\n    pub gross_profit_margin: String,\n    pub net_profit: String,\n    pub net_profit_margin: String,\n    pub ebitda: String,\n    pub ebitda_margin: String,\n}\n\n/// Financial summary\n#[derive(Debug, Serialize)]\npub struct FinancialSummary {\n    pub total_investment: String,\n    pub expected_revenue: String,\n    pub estimated_profit: String,\n    pub roi_percentage: String,\n    pub payback_period: String,\n    pub recommendation: FinancialRecommendation,\n}\n\n/// Financial recommendation\n#[derive(Debug, Serialize)]\npub struct FinancialRecommendation {\n    pub recommendation: RecommendationType,\n    pub confidence_level: String,\n    pub key_factors: Vec<String>,\n    pub next_steps: Vec<String>,\n}\n\n/// Recommendation type\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum RecommendationType {\n    StronglyRecommend,\n    Recommend,\n    Neutral,\n    Caution,\n    NotRecommended,\n}\n\n/// Financial calculation history request\n#[derive(Debug, Deserialize, Validate)]\npub struct FinancialHistoryRequest {\n    pub project_id: String,\n    pub calculation_type: Option<FinancialCalculationType>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n}\n\n/// Financial calculation history response\n#[derive(Debug, Serialize)]\npub struct FinancialHistoryResponse {\n    pub calculations: Vec<FinancialCalculationSummary>,\n    pub total: u64,\n}\n\n/// Financial calculation summary\n#[derive(Debug, Serialize)]\npub struct FinancialCalculationSummary {\n    pub calculation_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub calculated_at: String,\n    pub roi_percentage: Option<String>,\n    pub total_investment: String,\n    pub expected_revenue: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\nuse rust_decimal::Decimal;\n\n/// Financial calculation request\n#[derive(Debug, Deserialize, Validate)]\npub struct FinancialCalculationRequest {\n    pub project_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub input_data: FinancialInputData,\n}\n\n/// Types of financial calculations\n#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum FinancialCalculationType {\n    TaxCalculation,\n    RoiAnalysis,\n    InvestmentAnalysis,\n    CashFlowProjection,\n    BreakEvenAnalysis,\n    ProfitabilityAnalysis,\n}\n\n/// Financial input data\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct FinancialInputData {\n    #[validate(range(min = 0.0))]\n    pub total_investment: f64,\n    \n    #[validate(range(min = 0.0))]\n    pub expected_revenue: f64,\n    \n    #[validate(range(min = 0.0))]\n    pub land_area: Option<f64>,\n    \n    #[validate(range(min = 0.0))]\n    pub construction_area: Option<f64>,\n    \n    #[validate(range(min = 1, max = 600))]\n    pub project_duration_months: u32,\n    \n    pub land_location: Option<String>,\n    pub land_use_type: Option<String>,\n    \n    /// Additional financial parameters as key-value pairs\n    pub additional_data: Option<Value>,\n}\n\n/// Financial calculation response\n#[derive(Debug, Serialize)]\npub struct FinancialCalculationResponse {\n    pub calculation_id: String,\n    pub project_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub results: FinancialResults,\n    pub calculated_at: String,\n    pub valid_until: Option<String>,\n}\n\n/// Financial calculation results\n#[derive(Debug, Serialize)]\npub struct FinancialResults {\n    pub tax_calculations: Option<TaxCalculationResults>,\n    pub roi_analysis: Option<RoiAnalysisResults>,\n    pub investment_analysis: Option<InvestmentAnalysisResults>,\n    pub cash_flow: Option<CashFlowResults>,\n    pub break_even: Option<BreakEvenResults>,\n    pub profitability: Option<ProfitabilityResults>,\n    pub summary: FinancialSummary,\n}\n\n/// Vietnamese tax calculation results\n#[derive(Debug, Serialize)]\npub struct TaxCalculationResults {\n    pub vat_amount: String,\n    pub vat_rate: String,\n    pub corporate_tax: String,\n    pub corporate_tax_rate: String,\n    pub land_use_tax: Option<String>,\n    pub construction_permit_fees: Option<String>,\n    pub total_tax_obligations: String,\n    pub tax_breakdown: Vec<TaxBreakdownItem>,\n}\n\n/// Tax breakdown item\n#[derive(Debug, Serialize)]\npub struct TaxBreakdownItem {\n    pub tax_type: String,\n    pub amount: String,\n    pub rate: String,\n    pub description: String,\n}\n\n/// ROI analysis results\n#[derive(Debug, Serialize)]\npub struct RoiAnalysisResults {\n    pub roi_percentage: String,\n    pub payback_period_months: u32,\n    pub net_present_value: String,\n    pub internal_rate_of_return: String,\n    pub profitability_index: String,\n    pub risk_assessment: RiskAssessment,\n}\n\n/// Risk assessment\n#[derive(Debug, Serialize)]\npub struct RiskAssessment {\n    pub risk_level: RiskLevel,\n    pub risk_factors: Vec<String>,\n    pub mitigation_suggestions: Vec<String>,\n}\n\n/// Risk level enum\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum RiskLevel {\n    Low,\n    Medium,\n    High,\n    Critical,\n}\n\n/// Investment analysis results\n#[derive(Debug, Serialize)]\npub struct InvestmentAnalysisResults {\n    pub total_cost_breakdown: CostBreakdown,\n    pub revenue_projections: RevenueProjections,\n    pub sensitivity_analysis: SensitivityAnalysis,\n    pub scenario_analysis: Vec<ScenarioResult>,\n}\n\n/// Cost breakdown\n#[derive(Debug, Serialize)]\npub struct CostBreakdown {\n    pub land_acquisition: String,\n    pub construction_costs: String,\n    pub permits_and_fees: String,\n    pub financing_costs: String,\n    pub marketing_costs: String,\n    pub contingency: String,\n    pub total: String,\n}\n\n/// Revenue projections\n#[derive(Debug, Serialize)]\npub struct RevenueProjections {\n    pub sales_revenue: String,\n    pub rental_income: Option<String>,\n    pub other_income: Option<String>,\n    pub total_revenue: String,\n    pub revenue_timeline: Vec<RevenueTimelineItem>,\n}\n\n/// Revenue timeline item\n#[derive(Debug, Serialize)]\npub struct RevenueTimelineItem {\n    pub period: String,\n    pub amount: String,\n    pub cumulative: String,\n}\n\n/// Sensitivity analysis\n#[derive(Debug, Serialize)]\npub struct SensitivityAnalysis {\n    pub price_sensitivity: SensitivityResult,\n    pub cost_sensitivity: SensitivityResult,\n    pub timeline_sensitivity: SensitivityResult,\n}\n\n/// Sensitivity result\n#[derive(Debug, Serialize)]\npub struct SensitivityResult {\n    pub variable: String,\n    pub base_case: String,\n    pub optimistic: String,\n    pub pessimistic: String,\n    pub impact_on_roi: String,\n}\n\n/// Scenario analysis result\n#[derive(Debug, Serialize)]\npub struct ScenarioResult {\n    pub scenario_name: String,\n    pub probability: String,\n    pub roi: String,\n    pub npv: String,\n    pub description: String,\n}\n\n/// Cash flow results\n#[derive(Debug, Serialize)]\npub struct CashFlowResults {\n    pub monthly_cash_flow: Vec<CashFlowItem>,\n    pub cumulative_cash_flow: Vec<CashFlowItem>,\n    pub peak_funding_requirement: String,\n    pub cash_flow_summary: CashFlowSummary,\n}\n\n/// Cash flow item\n#[derive(Debug, Serialize)]\npub struct CashFlowItem {\n    pub period: String,\n    pub inflow: String,\n    pub outflow: String,\n    pub net_flow: String,\n}\n\n/// Cash flow summary\n#[derive(Debug, Serialize)]\npub struct CashFlowSummary {\n    pub total_inflow: String,\n    pub total_outflow: String,\n    pub net_cash_flow: String,\n    pub break_even_month: Option<u32>,\n}\n\n/// Break-even analysis results\n#[derive(Debug, Serialize)]\npub struct BreakEvenResults {\n    pub break_even_point: String,\n    pub break_even_timeline: String,\n    pub margin_of_safety: String,\n    pub contribution_margin: String,\n}\n\n/// Profitability analysis results\n#[derive(Debug, Serialize)]\npub struct ProfitabilityResults {\n    pub gross_profit: String,\n    pub gross_profit_margin: String,\n    pub net_profit: String,\n    pub net_profit_margin: String,\n    pub ebitda: String,\n    pub ebitda_margin: String,\n}\n\n/// Financial summary\n#[derive(Debug, Serialize)]\npub struct FinancialSummary {\n    pub total_investment: String,\n    pub expected_revenue: String,\n    pub estimated_profit: String,\n    pub roi_percentage: String,\n    pub payback_period: String,\n    pub recommendation: FinancialRecommendation,\n}\n\n/// Financial recommendation\n#[derive(Debug, Serialize)]\npub struct FinancialRecommendation {\n    pub recommendation: RecommendationType,\n    pub confidence_level: String,\n    pub key_factors: Vec<String>,\n    pub next_steps: Vec<String>,\n}\n\n/// Recommendation type\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum RecommendationType {\n    StronglyRecommend,\n    Recommend,\n    Neutral,\n    Caution,\n    NotRecommended,\n}\n\n/// Financial calculation history request\n#[derive(Debug, Deserialize, Validate)]\npub struct FinancialHistoryRequest {\n    pub project_id: String,\n    pub calculation_type: Option<FinancialCalculationType>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n}\n\n/// Financial calculation history response\n#[derive(Debug, Serialize)]\npub struct FinancialHistoryResponse {\n    pub calculations: Vec<FinancialCalculationSummary>,\n    pub total: u64,\n}\n\n/// Financial calculation summary\n#[derive(Debug, Serialize)]\npub struct FinancialCalculationSummary {\n    pub calculation_id: String,\n    pub calculation_type: FinancialCalculationType,\n    pub calculated_at: String,\n    pub roi_percentage: Option<String>,\n    pub total_investment: String,\n    pub expected_revenue: String,\n}\n"}