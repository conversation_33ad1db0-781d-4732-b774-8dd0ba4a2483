{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/Cargo.toml"}, "modifiedCode": "[package]\nname = \"excelsync-api\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync API - REST API endpoints and middleware\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\naxum = { workspace = true }\ntokio = { workspace = true }\ntower = { workspace = true }\ntower-http = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\ntracing = { workspace = true }\nvalidator = { workspace = true }\n\n# Local workspace crates\nexcelsync-core = { path = \"../core\" }\nexcelsync-auth = { path = \"../auth\" }\nexcelsync-database = { path = \"../database\" }\n\n# Additional dependencies for API\nhttp = \"1.0\"\nhttp-body-util = \"0.1\"\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n"}