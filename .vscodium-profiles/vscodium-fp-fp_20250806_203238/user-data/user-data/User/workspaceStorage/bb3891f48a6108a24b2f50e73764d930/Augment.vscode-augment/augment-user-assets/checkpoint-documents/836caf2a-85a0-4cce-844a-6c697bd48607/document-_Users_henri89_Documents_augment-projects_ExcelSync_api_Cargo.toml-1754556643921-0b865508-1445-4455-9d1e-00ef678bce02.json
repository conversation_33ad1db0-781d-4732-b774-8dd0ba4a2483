{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/Cargo.toml"}, "originalCode": "[package]\nname = \"api\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync API - REST API endpoints and middleware\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\naxum = { workspace = true }\ntokio = { workspace = true }\ntower = { workspace = true }\ntower-http = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\ntracing = { workspace = true }\nvalidator = { workspace = true }\n\n# Local workspace crates\nexcelsync-core = { path = \"../core\" }\nauth = { path = \"../auth\" }\ndatabase = { path = \"../database\" }\nsea-orm = { workspace = true }\nrust_decimal = { workspace = true }\ncalamine = { workspace = true }\nxlsxwriter = { workspace = true }\nnum-traits = { workspace = true }\nstatrs = { workspace = true }\n\n# Additional dependencies for API\nhttp = \"1.0\"\nhttp-body-util = \"0.1\"\ntower_governor = \"0.4\"\ngovernor = \"0.6\"\nfutures = \"0.3\"\nfutures-util = \"0.3\"\ntokio-util = { version = \"0.7\", features = [\"io\"] }\nsha2 = \"0.10\"\n\n# API Documentation\nutoipa = { version = \"4.0\", features = [\"axum_extras\", \"chrono\", \"uuid\"] }\nutoipa-swagger-ui = { version = \"4.0\", features = [\"axum\"] }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\nmockall = { workspace = true }\nrust_decimal_macros = \"1.32\"\n", "modifiedCode": "[package]\nname = \"api\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync API - REST API endpoints and middleware\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\naxum = { workspace = true }\ntokio = { workspace = true }\ntower = { workspace = true }\ntower-http = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\ntracing = { workspace = true }\nvalidator = { workspace = true }\n\n# Local workspace crates\nexcelsync-core = { path = \"../core\" }\nauth = { path = \"../auth\" }\ndatabase = { path = \"../database\" }\nsea-orm = { workspace = true }\nrust_decimal = { workspace = true }\ncalamine = { workspace = true }\nxlsxwriter = { workspace = true }\nnum-traits = { workspace = true }\nstatrs = { workspace = true }\n\n# Additional dependencies for API\nhttp = \"1.0\"\nhttp-body-util = \"0.1\"\ntower_governor = \"0.4\"\ngovernor = \"0.6\"\nfutures = \"0.3\"\nfutures-util = \"0.3\"\ntokio-util = { version = \"0.7\", features = [\"io\"] }\nsha2 = \"0.10\"\n\n# API Documentation\nutoipa = { version = \"4.0\", features = [\"axum_extras\", \"chrono\", \"uuid\"] }\nutoipa-swagger-ui = { version = \"4.0\", features = [\"axum\"] }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\nmockall = { workspace = true }\nrust_decimal_macros = \"1.32\"\ncriterion = { workspace = true }\n\n[[bench]]\nname = \"performance_benchmarks\"\nharness = false\n"}