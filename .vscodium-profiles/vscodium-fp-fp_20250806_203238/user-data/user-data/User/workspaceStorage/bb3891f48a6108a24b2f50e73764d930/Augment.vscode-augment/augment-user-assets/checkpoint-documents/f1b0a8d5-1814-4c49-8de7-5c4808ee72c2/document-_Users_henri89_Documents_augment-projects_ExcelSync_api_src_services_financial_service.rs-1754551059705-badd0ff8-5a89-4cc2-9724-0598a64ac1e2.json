{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/financial_service.rs"}, "originalCode": "use anyhow::Result;\nuse sea_orm::{DatabaseConnection, EntityTrait, Set, ActiveModelTrait, PaginatorTrait, QueryFilter, ColumnTrait};\nuse uuid::Uuid;\nuse chrono::Utc;\nuse serde_json::Value;\n\nuse crate::{\n    dto::financial::*,\n    handlers::ApiError,\n    services::financial_calculator::{FinancialCalculator, FinancialCalculationError},\n};\nuse database::entities::{financial_calculations, projects};\n\n/// Financial service for managing financial calculations and analysis\npub struct FinancialService {\n    db: DatabaseConnection,\n    calculator: FinancialCalculator,\n}\n\nimpl FinancialService {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self {\n            db,\n            calculator: FinancialCalculator::new(),\n        }\n    }\n\n    /// Perform financial calculation for a project\n    pub async fn calculate_financial_metrics(\n        &self,\n        request: FinancialCalculationRequest,\n        user_id: Uuid,\n    ) -> Result<FinancialCalculationResponse, ApiError> {\n        // Validate project exists and user has access\n        let project_id = Uuid::parse_str(&request.project_id)\n            .map_err(|_| ApiError::BadRequest(\"Invalid project ID format\".to_string()))?;\n\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        // Check if user has access to this project\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        // Perform the calculation\n        let calculation_result = self.calculator\n            .calculate_financial_metrics(&request)\n            .await\n            .map_err(|e| match e {\n                FinancialCalculationError::InvalidInput(msg) => ApiError::BadRequest(msg),\n                FinancialCalculationError::InsufficientData(msg) => ApiError::BadRequest(msg),\n                _ => ApiError::InternalServerError(format!(\"Calculation failed: {}\", e)),\n            })?;\n\n        // Save calculation to database\n        let calculation_id = Uuid::parse_str(&calculation_result.calculation_id)\n            .map_err(|_| ApiError::InternalServerError(\"Invalid calculation ID generated\".to_string()))?;\n\n        let financial_calculation = financial_calculations::ActiveModel {\n            id: Set(calculation_id),\n            project_id: Set(project_id),\n            calculation_type: Set(serde_json::to_string(&request.calculation_type)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize calculation type: {}\", e)))?),\n            input_data: Set(serde_json::to_value(&request.input_data)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize input data: {}\", e)))?),\n            results: Set(serde_json::to_value(&calculation_result.results)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize results: {}\", e)))?),\n            calculated_by: Set(user_id),\n            calculated_at: Set(Utc::now()),\n            valid_until: Set(calculation_result.valid_until.as_ref().and_then(|v| chrono::DateTime::parse_from_rfc3339(v).ok().map(|dt| dt.with_timezone(&Utc)))),\n            ..Default::default()\n        };\n\n        financial_calculation\n            .insert(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to save calculation: {}\", e)))?;\n\n        Ok(calculation_result)\n    }\n\n    /// Get financial calculation history for a project\n    pub async fn get_financial_history(\n        &self,\n        request: FinancialHistoryRequest,\n        user_id: Uuid,\n        page: u64,\n        per_page: u64,\n    ) -> Result<FinancialHistoryResponse, ApiError> {\n        let project_id = Uuid::parse_str(&request.project_id)\n            .map_err(|_| ApiError::BadRequest(\"Invalid project ID format\".to_string()))?;\n\n        // Validate project exists and user has access\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        let page = page.max(1);\n        let per_page = per_page.min(100).max(1);\n\n        let mut query = financial_calculations::Entity::find()\n            .filter(financial_calculations::Column::ProjectId.eq(project_id));\n\n        // Apply filters\n        if let Some(calc_type) = &request.calculation_type {\n            let calc_type_str = serde_json::to_string(calc_type)\n                .map_err(|e| ApiError::BadRequest(format!(\"Invalid calculation type: {}\", e)))?;\n            query = query.filter(financial_calculations::Column::CalculationType.eq(calc_type_str));\n        }\n\n        if let Some(start_date) = &request.start_date {\n            let start_dt = chrono::DateTime::parse_from_rfc3339(start_date)\n                .map_err(|_| ApiError::BadRequest(\"Invalid start date format\".to_string()))?\n                .with_timezone(&Utc);\n            query = query.filter(financial_calculations::Column::CalculatedAt.gte(start_dt));\n        }\n\n        if let Some(end_date) = &request.end_date {\n            let end_dt = chrono::DateTime::parse_from_rfc3339(end_date)\n                .map_err(|_| ApiError::BadRequest(\"Invalid end date format\".to_string()))?\n                .with_timezone(&Utc);\n            query = query.filter(financial_calculations::Column::CalculatedAt.lte(end_dt));\n        }\n\n        let paginator = query\n            .order_by_desc(financial_calculations::Column::CalculatedAt)\n            .paginate(&self.db, per_page);\n\n        let total = paginator.num_items().await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to count calculations: {}\", e)))?;\n\n        let calculations = paginator\n            .fetch_page(page - 1)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculations: {}\", e)))?;\n\n        let calculation_summaries: Result<Vec<FinancialCalculationSummary>, ApiError> = calculations\n            .into_iter()\n            .map(|calc| {\n                let calculation_type: FinancialCalculationType = serde_json::from_str(&calc.calculation_type)\n                    .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize calculation type: {}\", e)))?;\n\n                let results: FinancialResults = serde_json::from_value(calc.results)\n                    .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize results: {}\", e)))?;\n\n                let roi_percentage = match &results.roi_analysis {\n                    Some(roi) => Some(roi.roi_percentage.clone()),\n                    None => results.summary.roi_percentage.strip_suffix('%').map(|s| s.to_string()),\n                };\n\n                Ok(FinancialCalculationSummary {\n                    calculation_id: calc.id.to_string(),\n                    calculation_type,\n                    calculated_at: calc.calculated_at.to_rfc3339(),\n                    roi_percentage,\n                    total_investment: results.summary.total_investment,\n                })\n            })\n            .collect();\n\n        Ok(FinancialHistoryResponse {\n            calculations: calculation_summaries?,\n            total,\n        })\n    }\n\n    /// Get a specific financial calculation by ID\n    pub async fn get_financial_calculation(\n        &self,\n        calculation_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<FinancialCalculationResponse, ApiError> {\n        let calculation = financial_calculations::Entity::find_by_id(calculation_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculation: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Financial calculation not found\".to_string()))?;\n\n        // Verify user has access to this calculation's project\n        let project = projects::Entity::find_by_id(calculation.project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this calculation\".to_string()));\n        }\n\n        let calculation_type: FinancialCalculationType = serde_json::from_str(&calculation.calculation_type)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize calculation type: {}\", e)))?;\n\n        let results: FinancialResults = serde_json::from_value(calculation.results)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize results: {}\", e)))?;\n\n        Ok(FinancialCalculationResponse {\n            calculation_id: calculation.id.to_string(),\n            project_id: calculation.project_id.to_string(),\n            calculation_type,\n            results,\n            calculated_at: calculation.calculated_at.to_rfc3339(),\n            valid_until: calculation.valid_until.map(|dt| dt.to_rfc3339()),\n        })\n    }\n\n    /// Delete a financial calculation\n    pub async fn delete_financial_calculation(\n        &self,\n        calculation_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<(), ApiError> {\n        let calculation = financial_calculations::Entity::find_by_id(calculation_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculation: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Financial calculation not found\".to_string()))?;\n\n        // Verify user has access to this calculation's project\n        let project = projects::Entity::find_by_id(calculation.project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this calculation\".to_string()));\n        }\n\n        financial_calculations::Entity::delete_by_id(calculation_id)\n            .exec(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to delete calculation: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// Get financial calculation statistics for a project\n    pub async fn get_calculation_statistics(\n        &self,\n        project_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<Value, ApiError> {\n        // Verify user has access to this project\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        let calculations = financial_calculations::Entity::find()\n            .filter(financial_calculations::Column::ProjectId.eq(project_id))\n            .all(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculations: {}\", e)))?;\n\n        let total_calculations = calculations.len();\n        let mut calculation_types = std::collections::HashMap::new();\n        let mut latest_calculation = None;\n        let mut latest_date = None;\n\n        for calc in calculations {\n            // Count by calculation type\n            *calculation_types.entry(calc.calculation_type.clone()).or_insert(0) += 1;\n\n            // Track latest calculation\n            if latest_date.is_none() || calc.calculated_at > latest_date.unwrap() {\n                latest_date = Some(calc.calculated_at);\n                latest_calculation = Some(calc.id.to_string());\n            }\n        }\n\n        let statistics = serde_json::json!({\n            \"total_calculations\": total_calculations,\n            \"calculation_types\": calculation_types,\n            \"latest_calculation_id\": latest_calculation,\n            \"latest_calculation_date\": latest_date.map(|dt| dt.to_rfc3339()),\n            \"project_id\": project_id.to_string()\n        });\n\n        Ok(statistics)\n    }\n}\n\nimpl Clone for FinancialService {\n    fn clone(&self) -> Self {\n        Self {\n            db: self.db.clone(),\n            calculator: FinancialCalculator::new(),\n        }\n    }\n}\n", "modifiedCode": "use anyhow::Result;\nuse sea_orm::{DatabaseConnection, EntityTrait, Set, ActiveModelTrait, PaginatorTrait, QueryFilter, ColumnTrait, QueryOrder};\nuse uuid::Uuid;\nuse chrono::Utc;\nuse serde_json::Value;\n\nuse crate::{\n    dto::financial::*,\n    handlers::ApiError,\n    services::financial_calculator::{FinancialCalculator, FinancialCalculationError},\n};\nuse database::entities::{financial_calculations, projects};\n\n/// Financial service for managing financial calculations and analysis\npub struct FinancialService {\n    db: DatabaseConnection,\n    calculator: FinancialCalculator,\n}\n\nimpl FinancialService {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self {\n            db,\n            calculator: FinancialCalculator::new(),\n        }\n    }\n\n    /// Perform financial calculation for a project\n    pub async fn calculate_financial_metrics(\n        &self,\n        request: FinancialCalculationRequest,\n        user_id: Uuid,\n    ) -> Result<FinancialCalculationResponse, ApiError> {\n        // Validate project exists and user has access\n        let project_id = Uuid::parse_str(&request.project_id)\n            .map_err(|_| ApiError::BadRequest(\"Invalid project ID format\".to_string()))?;\n\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        // Check if user has access to this project\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        // Perform the calculation\n        let calculation_result = self.calculator\n            .calculate_financial_metrics(&request)\n            .await\n            .map_err(|e| match e {\n                FinancialCalculationError::InvalidInput(msg) => ApiError::BadRequest(msg),\n                FinancialCalculationError::InsufficientData(msg) => ApiError::BadRequest(msg),\n                _ => ApiError::InternalServerError(format!(\"Calculation failed: {}\", e)),\n            })?;\n\n        // Save calculation to database\n        let calculation_id = Uuid::parse_str(&calculation_result.calculation_id)\n            .map_err(|_| ApiError::InternalServerError(\"Invalid calculation ID generated\".to_string()))?;\n\n        let financial_calculation = financial_calculations::ActiveModel {\n            id: Set(calculation_id),\n            project_id: Set(project_id),\n            calculation_type: Set(serde_json::to_string(&request.calculation_type)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize calculation type: {}\", e)))?),\n            input_data: Set(serde_json::to_value(&request.input_data)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize input data: {}\", e)))?),\n            results: Set(serde_json::to_value(&calculation_result.results)\n                .map_err(|e| ApiError::InternalServerError(format!(\"Failed to serialize results: {}\", e)))?),\n            calculated_by: Set(user_id),\n            calculated_at: Set(Utc::now()),\n            valid_until: Set(calculation_result.valid_until.as_ref().and_then(|v| chrono::DateTime::parse_from_rfc3339(v).ok().map(|dt| dt.with_timezone(&Utc)))),\n            ..Default::default()\n        };\n\n        financial_calculation\n            .insert(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to save calculation: {}\", e)))?;\n\n        Ok(calculation_result)\n    }\n\n    /// Get financial calculation history for a project\n    pub async fn get_financial_history(\n        &self,\n        request: FinancialHistoryRequest,\n        user_id: Uuid,\n        page: u64,\n        per_page: u64,\n    ) -> Result<FinancialHistoryResponse, ApiError> {\n        let project_id = Uuid::parse_str(&request.project_id)\n            .map_err(|_| ApiError::BadRequest(\"Invalid project ID format\".to_string()))?;\n\n        // Validate project exists and user has access\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        let page = page.max(1);\n        let per_page = per_page.min(100).max(1);\n\n        let mut query = financial_calculations::Entity::find()\n            .filter(financial_calculations::Column::ProjectId.eq(project_id));\n\n        // Apply filters\n        if let Some(calc_type) = &request.calculation_type {\n            let calc_type_str = serde_json::to_string(calc_type)\n                .map_err(|e| ApiError::BadRequest(format!(\"Invalid calculation type: {}\", e)))?;\n            query = query.filter(financial_calculations::Column::CalculationType.eq(calc_type_str));\n        }\n\n        if let Some(start_date) = &request.start_date {\n            let start_dt = chrono::DateTime::parse_from_rfc3339(start_date)\n                .map_err(|_| ApiError::BadRequest(\"Invalid start date format\".to_string()))?\n                .with_timezone(&Utc);\n            query = query.filter(financial_calculations::Column::CalculatedAt.gte(start_dt));\n        }\n\n        if let Some(end_date) = &request.end_date {\n            let end_dt = chrono::DateTime::parse_from_rfc3339(end_date)\n                .map_err(|_| ApiError::BadRequest(\"Invalid end date format\".to_string()))?\n                .with_timezone(&Utc);\n            query = query.filter(financial_calculations::Column::CalculatedAt.lte(end_dt));\n        }\n\n        let paginator = query\n            .order_by_desc(financial_calculations::Column::CalculatedAt)\n            .paginate(&self.db, per_page);\n\n        let total = paginator.num_items().await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to count calculations: {}\", e)))?;\n\n        let calculations = paginator\n            .fetch_page(page - 1)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculations: {}\", e)))?;\n\n        let calculation_summaries: Result<Vec<FinancialCalculationSummary>, ApiError> = calculations\n            .into_iter()\n            .map(|calc| {\n                let calculation_type: FinancialCalculationType = serde_json::from_str(&calc.calculation_type)\n                    .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize calculation type: {}\", e)))?;\n\n                let results: FinancialResults = serde_json::from_value(calc.results)\n                    .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize results: {}\", e)))?;\n\n                let roi_percentage = match &results.roi_analysis {\n                    Some(roi) => Some(roi.roi_percentage.clone()),\n                    None => results.summary.roi_percentage.strip_suffix('%').map(|s| s.to_string()),\n                };\n\n                Ok(FinancialCalculationSummary {\n                    calculation_id: calc.id.to_string(),\n                    calculation_type,\n                    calculated_at: calc.calculated_at.to_rfc3339(),\n                    roi_percentage,\n                    total_investment: results.summary.total_investment,\n                })\n            })\n            .collect();\n\n        Ok(FinancialHistoryResponse {\n            calculations: calculation_summaries?,\n            total,\n        })\n    }\n\n    /// Get a specific financial calculation by ID\n    pub async fn get_financial_calculation(\n        &self,\n        calculation_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<FinancialCalculationResponse, ApiError> {\n        let calculation = financial_calculations::Entity::find_by_id(calculation_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculation: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Financial calculation not found\".to_string()))?;\n\n        // Verify user has access to this calculation's project\n        let project = projects::Entity::find_by_id(calculation.project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this calculation\".to_string()));\n        }\n\n        let calculation_type: FinancialCalculationType = serde_json::from_str(&calculation.calculation_type)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize calculation type: {}\", e)))?;\n\n        let results: FinancialResults = serde_json::from_value(calculation.results)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to deserialize results: {}\", e)))?;\n\n        Ok(FinancialCalculationResponse {\n            calculation_id: calculation.id.to_string(),\n            project_id: calculation.project_id.to_string(),\n            calculation_type,\n            results,\n            calculated_at: calculation.calculated_at.to_rfc3339(),\n            valid_until: calculation.valid_until.map(|dt| dt.to_rfc3339()),\n        })\n    }\n\n    /// Delete a financial calculation\n    pub async fn delete_financial_calculation(\n        &self,\n        calculation_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<(), ApiError> {\n        let calculation = financial_calculations::Entity::find_by_id(calculation_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculation: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Financial calculation not found\".to_string()))?;\n\n        // Verify user has access to this calculation's project\n        let project = projects::Entity::find_by_id(calculation.project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this calculation\".to_string()));\n        }\n\n        financial_calculations::Entity::delete_by_id(calculation_id)\n            .exec(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to delete calculation: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// Get financial calculation statistics for a project\n    pub async fn get_calculation_statistics(\n        &self,\n        project_id: Uuid,\n        user_id: Uuid,\n    ) -> Result<Value, ApiError> {\n        // Verify user has access to this project\n        let project = projects::Entity::find_by_id(project_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch project: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"Project not found\".to_string()))?;\n\n        if project.owner_id != user_id {\n            return Err(ApiError::Forbidden(\"Access denied to this project\".to_string()));\n        }\n\n        let calculations = financial_calculations::Entity::find()\n            .filter(financial_calculations::Column::ProjectId.eq(project_id))\n            .all(&self.db)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch calculations: {}\", e)))?;\n\n        let total_calculations = calculations.len();\n        let mut calculation_types = std::collections::HashMap::new();\n        let mut latest_calculation = None;\n        let mut latest_date = None;\n\n        for calc in calculations {\n            // Count by calculation type\n            *calculation_types.entry(calc.calculation_type.clone()).or_insert(0) += 1;\n\n            // Track latest calculation\n            if latest_date.is_none() || calc.calculated_at > latest_date.unwrap() {\n                latest_date = Some(calc.calculated_at);\n                latest_calculation = Some(calc.id.to_string());\n            }\n        }\n\n        let statistics = serde_json::json!({\n            \"total_calculations\": total_calculations,\n            \"calculation_types\": calculation_types,\n            \"latest_calculation_id\": latest_calculation,\n            \"latest_calculation_date\": latest_date.map(|dt| dt.to_rfc3339()),\n            \"project_id\": project_id.to_string()\n        });\n\n        Ok(statistics)\n    }\n}\n\nimpl Clone for FinancialService {\n    fn clone(&self) -> Self {\n        Self {\n            db: self.db.clone(),\n            calculator: FinancialCalculator::new(),\n        }\n    }\n}\n"}