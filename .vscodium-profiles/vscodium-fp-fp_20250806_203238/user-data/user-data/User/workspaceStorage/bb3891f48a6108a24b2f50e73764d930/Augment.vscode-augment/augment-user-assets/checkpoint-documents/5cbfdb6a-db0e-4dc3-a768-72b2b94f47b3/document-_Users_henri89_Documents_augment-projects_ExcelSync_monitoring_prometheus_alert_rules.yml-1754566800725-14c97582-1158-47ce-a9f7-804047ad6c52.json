{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/prometheus/alert_rules.yml"}, "modifiedCode": "# Alert rules for ExcelSync monitoring\ngroups:\n  - name: excelsync.rules\n    rules:\n      # Service availability alerts\n      - alert: ExcelSyncAPIDown\n        expr: up{job=\"excelsync-api\"} == 0\n        for: 1m\n        labels:\n          severity: critical\n        annotations:\n          summary: \"ExcelSync API is down\"\n          description: \"ExcelSync API has been down for more than 1 minute.\"\n\n      - alert: ExcelSyncAPIUnhealthy\n        expr: excelsync_health_status != 1\n        for: 2m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"ExcelSync API is unhealthy\"\n          description: \"ExcelSync API health check is failing for more than 2 minutes.\"\n\n      # Performance alerts\n      - alert: HighResponseTime\n        expr: histogram_quantile(0.95, rate(excelsync_http_request_duration_seconds_bucket[5m])) > 0.2\n        for: 5m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"High response time detected\"\n          description: \"95th percentile response time is {{ $value }}s, which is above the 200ms threshold.\"\n\n      - alert: HighErrorRate\n        expr: rate(excelsync_http_requests_total{status=~\"5..\"}[5m]) > 0.1\n        for: 2m\n        labels:\n          severity: critical\n        annotations:\n          summary: \"High error rate detected\"\n          description: \"Error rate is {{ $value }} errors per second, which is above the threshold.\"\n\n      - alert: HighRequestRate\n        expr: rate(excelsync_http_requests_total[5m]) > 100\n        for: 5m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"High request rate detected\"\n          description: \"Request rate is {{ $value }} requests per second, which may indicate unusual load.\"\n\n      # Database alerts\n      - alert: DatabaseConnectionsHigh\n        expr: excelsync_db_connections_active > 18\n        for: 5m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"Database connections running high\"\n          description: \"Active database connections ({{ $value }}) are approaching the pool limit of 20.\"\n\n      - alert: DatabaseConnectionsExhausted\n        expr: excelsync_db_connections_active >= 20\n        for: 1m\n        labels:\n          severity: critical\n        annotations:\n          summary: \"Database connection pool exhausted\"\n          description: \"All database connections are in use. New requests may fail.\"\n\n      # Authentication alerts\n      - alert: HighAuthFailureRate\n        expr: rate(excelsync_auth_attempts_total{result=\"failure\"}[5m]) > 5\n        for: 2m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"High authentication failure rate\"\n          description: \"Authentication failure rate is {{ $value }} failures per second, which may indicate a brute force attack.\"\n\n      - alert: NoSuccessfulAuthAttempts\n        expr: rate(excelsync_auth_attempts_total{result=\"success\"}[10m]) == 0 and rate(excelsync_auth_attempts_total{result=\"failure\"}[10m]) > 0\n        for: 10m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"No successful authentication attempts\"\n          description: \"No successful authentication attempts in the last 10 minutes, but failures are occurring.\"\n\n      # Business logic alerts\n      - alert: NoNewProjects\n        expr: increase(excelsync_projects_total[24h]) == 0\n        for: 24h\n        labels:\n          severity: info\n        annotations:\n          summary: \"No new projects created\"\n          description: \"No new projects have been created in the last 24 hours.\"\n\n      - alert: NoNewUsers\n        expr: increase(excelsync_users_total[24h]) == 0\n        for: 24h\n        labels:\n          severity: info\n        annotations:\n          summary: \"No new users registered\"\n          description: \"No new users have registered in the last 24 hours.\"\n\n      # System resource alerts\n      - alert: HighMemoryUsage\n        expr: process_resident_memory_bytes / 1024 / 1024 > 512\n        for: 5m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"High memory usage\"\n          description: \"Memory usage is {{ $value }}MB, which is above the 512MB threshold.\"\n\n      # Redis alerts\n      - alert: RedisConnectionFailed\n        expr: excelsync_redis_connections_active == 0\n        for: 1m\n        labels:\n          severity: critical\n        annotations:\n          summary: \"Redis connection failed\"\n          description: \"Unable to connect to Redis for more than 1 minute.\"\n\n      # Custom business alerts\n      - alert: HighErrorCount\n        expr: increase(excelsync_errors_total[5m]) > 10\n        for: 1m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"High error count\"\n          description: \"More than 10 errors occurred in the last 5 minutes.\"\n\n      - alert: DatabaseQuerySlow\n        expr: histogram_quantile(0.95, rate(excelsync_db_query_duration_seconds_bucket[5m])) > 0.1\n        for: 5m\n        labels:\n          severity: warning\n        annotations:\n          summary: \"Slow database queries detected\"\n          description: \"95th percentile database query time is {{ $value }}s, which is above the 100ms threshold.\"\n"}