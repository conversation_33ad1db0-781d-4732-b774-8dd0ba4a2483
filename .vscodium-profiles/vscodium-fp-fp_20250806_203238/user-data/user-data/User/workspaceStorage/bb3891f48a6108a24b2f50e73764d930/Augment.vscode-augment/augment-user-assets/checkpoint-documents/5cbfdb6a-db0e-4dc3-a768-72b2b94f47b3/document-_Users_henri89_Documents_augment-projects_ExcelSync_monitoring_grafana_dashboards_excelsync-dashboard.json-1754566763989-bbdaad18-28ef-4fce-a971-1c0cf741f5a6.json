{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/dashboards/excelsync-dashboard.json"}, "modifiedCode": "{\n  \"dashboard\": {\n    \"id\": null,\n    \"title\": \"ExcelSync API Monitoring\",\n    \"tags\": [\"excelsync\", \"api\", \"monitoring\"],\n    \"style\": \"dark\",\n    \"timezone\": \"browser\",\n    \"panels\": [\n      {\n        \"id\": 1,\n        \"title\": \"System Health\",\n        \"type\": \"stat\",\n        \"targets\": [\n          {\n            \"expr\": \"up{job=\\\"excelsync-api\\\"}\",\n            \"legendFormat\": \"API Status\"\n          }\n        ],\n        \"fieldConfig\": {\n          \"defaults\": {\n            \"color\": {\n              \"mode\": \"thresholds\"\n            },\n            \"thresholds\": {\n              \"steps\": [\n                {\"color\": \"red\", \"value\": 0},\n                {\"color\": \"green\", \"value\": 1}\n              ]\n            }\n          }\n        },\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 0, \"y\": 0}\n      },\n      {\n        \"id\": 2,\n        \"title\": \"HTTP Request Rate\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(excelsync_http_requests_total[5m])\",\n            \"legendFormat\": \"{{method}} {{path}}\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Requests/sec\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 12, \"y\": 0}\n      },\n      {\n        \"id\": 3,\n        \"title\": \"Response Time\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"histogram_quantile(0.95, rate(excelsync_http_request_duration_seconds_bucket[5m]))\",\n            \"legendFormat\": \"95th percentile\"\n          },\n          {\n            \"expr\": \"histogram_quantile(0.50, rate(excelsync_http_request_duration_seconds_bucket[5m]))\",\n            \"legendFormat\": \"50th percentile\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Seconds\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 0, \"y\": 8}\n      },\n      {\n        \"id\": 4,\n        \"title\": \"Error Rate\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(excelsync_http_requests_total{status=~\\\"4..|5..\\\"}[5m])\",\n            \"legendFormat\": \"{{status}} errors\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Errors/sec\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 12, \"y\": 8}\n      },\n      {\n        \"id\": 5,\n        \"title\": \"Database Connections\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"excelsync_db_connections_active\",\n            \"legendFormat\": \"Active\"\n          },\n          {\n            \"expr\": \"excelsync_db_connections_idle\",\n            \"legendFormat\": \"Idle\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Connections\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 0, \"y\": 16}\n      },\n      {\n        \"id\": 6,\n        \"title\": \"Business Metrics\",\n        \"type\": \"stat\",\n        \"targets\": [\n          {\n            \"expr\": \"excelsync_projects_total\",\n            \"legendFormat\": \"Projects\"\n          },\n          {\n            \"expr\": \"excelsync_users_total\",\n            \"legendFormat\": \"Users\"\n          },\n          {\n            \"expr\": \"excelsync_templates_total\",\n            \"legendFormat\": \"Templates\"\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 12, \"y\": 16}\n      },\n      {\n        \"id\": 7,\n        \"title\": \"Authentication Metrics\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(excelsync_auth_attempts_total[5m])\",\n            \"legendFormat\": \"{{result}} attempts\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Attempts/sec\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 0, \"y\": 24}\n      },\n      {\n        \"id\": 8,\n        \"title\": \"JWT Tokens Issued\",\n        \"type\": \"graph\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(excelsync_jwt_tokens_issued_total[5m])\",\n            \"legendFormat\": \"{{token_type}} tokens\"\n          }\n        ],\n        \"yAxes\": [\n          {\n            \"label\": \"Tokens/sec\",\n            \"min\": 0\n          }\n        ],\n        \"gridPos\": {\"h\": 8, \"w\": 12, \"x\": 12, \"y\": 24}\n      }\n    ],\n    \"time\": {\n      \"from\": \"now-1h\",\n      \"to\": \"now\"\n    },\n    \"refresh\": \"30s\"\n  }\n}\n"}