{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "config/default.toml"}, "originalCode": "[server]\nhost = \"0.0.0.0\"\nport = 8080\nworkers = 4\n\n[database]\nurl = \"********************************************/postgres\"\nmax_connections = 20\nmin_connections = 5\nconnection_timeout = 30\nidle_timeout = 600\n\n[redis]\nurl = \"redis://localhost:6379\"\nmax_connections = 10\nconnection_timeout = 30\n\n[security]\njwt_secret = \"your-super-secret-jwt-key-32-characters-long-minimum\"\nencryption_key = \"your-super-secret-encryption-key-64-characters-long-minimum-for-aes256\"\npassword_salt = \"your-password-salt-16-chars-minimum\"\nsession_timeout = 28800  # 8 hours\nmax_sessions_per_user = 5\n\n[logging]\nlevel = \"debug\"\nformat = \"json\"\n\n[performance]\nrequest_timeout = 30\nmax_request_size = 1048576  # 1MB\n", "modifiedCode": "[server]\nhost = \"0.0.0.0\"\nport = 8080\nworkers = 4\n\n[database]\nurl = \"********************************************/postgres\"\nmax_connections = 20\nmin_connections = 5\nconnection_timeout = 30\nidle_timeout = 600\n\n[redis]\nurl = \"redis://localhost:6379\"\nmax_connections = 10\nconnection_timeout = 30\n\n[security]\njwt_secret = \"your-super-secret-jwt-key-32-characters-long-minimum\"\nencryption_key = \"your-super-secret-encryption-key-64-characters-long-minimum-for-aes256\"\npassword_salt = \"your-password-salt-16-chars-minimum\"\nsession_timeout = 28800  # 8 hours\nmax_sessions_per_user = 5\n\n[logging]\nlevel = \"debug\"\nformat = \"json\"\n\n[performance]\nrate_limit_requests = 100\nrate_limit_window = 60\n"}