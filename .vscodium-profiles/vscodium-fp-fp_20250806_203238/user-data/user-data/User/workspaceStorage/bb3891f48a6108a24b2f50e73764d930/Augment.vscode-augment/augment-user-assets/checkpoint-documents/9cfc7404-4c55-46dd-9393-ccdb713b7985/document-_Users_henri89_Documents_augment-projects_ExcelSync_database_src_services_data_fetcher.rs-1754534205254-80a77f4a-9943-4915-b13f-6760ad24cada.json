{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/services/data_fetcher.rs"}, "originalCode": "use anyhow::Result;\nuse sea_orm::{Database, DatabaseConnection, ConnectionTrait, Statement, Value};\nuse serde::{Deserialize, Serialize};\nuse serde_json::Value as JsonValue;\nuse std::collections::HashMap;\nuse tracing::{info, error};\n\n/// Data fetcher service for retrieving data from PostgreSQL\n#[derive(Clone)]\npub struct DataFetcher {\n    db: DatabaseConnection,\n}\n\n/// Generic data row representation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct DataRow {\n    pub columns: HashMap<String, JsonValue>,\n}\n\n/// Query result with metadata\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct QueryResult {\n    pub rows: Vec<DataRow>,\n    pub row_count: usize,\n    pub columns: Vec<String>,\n}\n\nimpl DataFetcher {\n    /// Create new data fetcher with database connection\n    pub async fn new(database_url: &str) -> Result<Self> {\n        info!(\"Connecting to PostgreSQL database: {}\", database_url);\n        \n        let db = Database::connect(database_url)\n            .await\n            .map_err(|e| anyhow::anyhow!(\"Failed to connect to database: {}\", e))?;\n        \n        info!(\"Successfully connected to PostgreSQL database\");\n        \n        Ok(Self { db })\n    }\n\n    /// Execute a raw SQL query and return results\n    pub async fn execute_query(&self, sql: &str) -> Result<QueryResult> {\n        info!(\"Executing query: {}\", sql);\n\n        // For now, return a simple test result\n        let result = QueryResult {\n            row_count: 0,\n            columns: vec![\"test\".to_string()],\n            rows: vec![],\n        };\n\n        info!(\"Query executed successfully, returned {} rows\", result.row_count);\n        Ok(result)\n    }\n\n    /// Fetch all tables in the database\n    pub async fn get_tables(&self) -> Result<Vec<String>> {\n        let sql = r#\"\n            SELECT table_name \n            FROM information_schema.tables \n            WHERE table_schema = 'public' \n            ORDER BY table_name\n        \"#;\n        \n        let result = self.execute_query(sql).await?;\n        let tables = result.rows\n            .into_iter()\n            .filter_map(|row| {\n                row.columns.get(\"table_name\")\n                    .and_then(|v| v.as_str())\n                    .map(|s| s.to_string())\n            })\n            .collect();\n        \n        Ok(tables)\n    }\n\n    /// Get table schema information\n    pub async fn get_table_schema(&self, table_name: &str) -> Result<QueryResult> {\n        let sql = format!(r#\"\n            SELECT \n                column_name,\n                data_type,\n                is_nullable,\n                column_default,\n                character_maximum_length\n            FROM information_schema.columns \n            WHERE table_name = '{}' \n            AND table_schema = 'public'\n            ORDER BY ordinal_position\n        \"#, table_name);\n        \n        self.execute_query(&sql).await\n    }\n\n    /// Fetch data from a specific table with optional limit\n    pub async fn fetch_table_data(&self, table_name: &str, limit: Option<u32>) -> Result<QueryResult> {\n        let limit_clause = match limit {\n            Some(l) => format!(\" LIMIT {}\", l),\n            None => String::new(),\n        };\n        \n        let sql = format!(\"SELECT * FROM {}{}\", table_name, limit_clause);\n        self.execute_query(&sql).await\n    }\n\n    /// Count rows in a table\n    pub async fn count_table_rows(&self, table_name: &str) -> Result<i64> {\n        let sql = format!(\"SELECT COUNT(*) as count FROM {}\", table_name);\n        let result = self.execute_query(&sql).await?;\n        \n        if let Some(row) = result.rows.first() {\n            if let Some(count_value) = row.columns.get(\"count\") {\n                if let Some(count) = count_value.as_i64() {\n                    return Ok(count);\n                }\n            }\n        }\n        \n        Ok(0)\n    }\n\n    /// Execute a custom query with parameters (for safety)\n    pub async fn execute_safe_query(&self, sql: &str, _params: Vec<Value>) -> Result<QueryResult> {\n        info!(\"Executing parameterized query: {}\", sql);\n\n        // For now, return a simple test result\n        let result = QueryResult {\n            row_count: 0,\n            columns: vec![\"test\".to_string()],\n            rows: vec![],\n        };\n\n        info!(\"Parameterized query executed successfully, returned {} rows\", result.row_count);\n        Ok(result)\n    }\n\n    /// Test database connection\n    pub async fn test_connection(&self) -> Result<bool> {\n        match self.execute_query(\"SELECT 1 as test\").await {\n            Ok(_) => {\n                info!(\"Database connection test successful\");\n                Ok(true)\n            }\n            Err(e) => {\n                error!(\"Database connection test failed: {}\", e);\n                Err(e)\n            }\n        }\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[tokio::test]\n    async fn test_data_fetcher_creation() {\n        // This test would require a real database connection\n        // For now, we'll just test that the struct can be created\n        let database_url = \"********************************************/postgres\";\n        \n        // In a real test environment, you would:\n        // let fetcher = DataFetcher::new(database_url).await.unwrap();\n        // assert!(fetcher.test_connection().await.unwrap());\n        \n        // For now, just verify the URL format\n        assert!(database_url.starts_with(\"postgresql://\"));\n    }\n}\n", "modifiedCode": "use anyhow::Result;\nuse sea_orm::{Database, DatabaseConnection, ConnectionTrait, Statement, Value};\nuse serde::{Deserialize, Serialize};\nuse serde_json::Value as JsonValue;\nuse std::collections::HashMap;\nuse tracing::{info, error};\n\n/// Data fetcher service for retrieving data from PostgreSQL\n#[derive(Clone)]\npub struct DataFetcher {\n    db: DatabaseConnection,\n}\n\n/// Generic data row representation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct DataRow {\n    pub columns: HashMap<String, JsonValue>,\n}\n\n/// Query result with metadata\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct QueryResult {\n    pub rows: Vec<DataRow>,\n    pub row_count: usize,\n    pub columns: Vec<String>,\n}\n\nimpl DataFetcher {\n    /// Create new data fetcher with database connection\n    pub async fn new(database_url: &str) -> Result<Self> {\n        info!(\"Connecting to PostgreSQL database: {}\", database_url);\n        \n        let db = Database::connect(database_url)\n            .await\n            .map_err(|e| anyhow::anyhow!(\"Failed to connect to database: {}\", e))?;\n        \n        info!(\"Successfully connected to PostgreSQL database\");\n        \n        Ok(Self { db })\n    }\n\n    /// Execute a raw SQL query and return results\n    pub async fn execute_query(&self, sql: &str) -> Result<QueryResult> {\n        info!(\"Executing query: {}\", sql);\n\n        // For now, return a simple test result\n        let result = QueryResult {\n            row_count: 0,\n            columns: vec![\"test\".to_string()],\n            rows: vec![],\n        };\n\n        info!(\"Query executed successfully, returned {} rows\", result.row_count);\n        Ok(result)\n    }\n\n    /// Fetch all tables in the database\n    pub async fn get_tables(&self) -> Result<Vec<String>> {\n        let sql = r#\"\n            SELECT table_name \n            FROM information_schema.tables \n            WHERE table_schema = 'public' \n            ORDER BY table_name\n        \"#;\n        \n        let result = self.execute_query(sql).await?;\n        let tables = result.rows\n            .into_iter()\n            .filter_map(|row| {\n                row.columns.get(\"table_name\")\n                    .and_then(|v| v.as_str())\n                    .map(|s| s.to_string())\n            })\n            .collect();\n        \n        Ok(tables)\n    }\n\n    /// Get table schema information\n    pub async fn get_table_schema(&self, table_name: &str) -> Result<QueryResult> {\n        let sql = format!(r#\"\n            SELECT \n                column_name,\n                data_type,\n                is_nullable,\n                column_default,\n                character_maximum_length\n            FROM information_schema.columns \n            WHERE table_name = '{}' \n            AND table_schema = 'public'\n            ORDER BY ordinal_position\n        \"#, table_name);\n        \n        self.execute_query(&sql).await\n    }\n\n    /// Fetch data from a specific table with optional limit\n    pub async fn fetch_table_data(&self, table_name: &str, limit: Option<u32>) -> Result<QueryResult> {\n        let limit_clause = match limit {\n            Some(l) => format!(\" LIMIT {}\", l),\n            None => String::new(),\n        };\n        \n        let sql = format!(\"SELECT * FROM {}{}\", table_name, limit_clause);\n        self.execute_query(&sql).await\n    }\n\n    /// Count rows in a table\n    pub async fn count_table_rows(&self, table_name: &str) -> Result<i64> {\n        let sql = format!(\"SELECT COUNT(*) as count FROM {}\", table_name);\n        let result = self.execute_query(&sql).await?;\n        \n        if let Some(row) = result.rows.first() {\n            if let Some(count_value) = row.columns.get(\"count\") {\n                if let Some(count) = count_value.as_i64() {\n                    return Ok(count);\n                }\n            }\n        }\n        \n        Ok(0)\n    }\n\n    /// Execute a custom query with parameters (for safety)\n    pub async fn execute_safe_query(&self, sql: &str, _params: Vec<Value>) -> Result<QueryResult> {\n        info!(\"Executing parameterized query: {}\", sql);\n\n        // For now, return a simple test result\n        let result = QueryResult {\n            row_count: 0,\n            columns: vec![\"test\".to_string()],\n            rows: vec![],\n        };\n\n        info!(\"Parameterized query executed successfully, returned {} rows\", result.row_count);\n        Ok(result)\n    }\n\n    /// Test database connection\n    pub async fn test_connection(&self) -> Result<bool> {\n        info!(\"Testing database connection...\");\n\n        let statement = Statement::from_string(\n            sea_orm::DatabaseBackend::Postgres,\n            \"SELECT 1 as test\".to_string(),\n        );\n\n        match self.db.query_one(statement).await {\n            Ok(_) => {\n                info!(\"Database connection test successful\");\n                Ok(true)\n            }\n            Err(e) => {\n                error!(\"Database connection test failed: {}\", e);\n                Err(anyhow::anyhow!(\"Connection test failed: {}\", e))\n            }\n        }\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[tokio::test]\n    async fn test_data_fetcher_creation() {\n        // This test would require a real database connection\n        // For now, we'll just test that the struct can be created\n        let database_url = \"********************************************/postgres\";\n        \n        // In a real test environment, you would:\n        // let fetcher = DataFetcher::new(database_url).await.unwrap();\n        // assert!(fetcher.test_connection().await.unwrap());\n        \n        // For now, just verify the URL format\n        assert!(database_url.starts_with(\"postgresql://\"));\n    }\n}\n"}