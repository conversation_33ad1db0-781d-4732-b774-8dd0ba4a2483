{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/error.rs"}, "modifiedCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    Json,\n};\nuse serde_json::json;\n\n/// API error types\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error\")]\n    InternalServerError,\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n    #[error(\"Unauthorized\")]\n    Unauthorized,\n    #[error(\"Forbidden\")]\n    Forbidden,\n    #[error(\"Not found\")]\n    NotFound,\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let (status, error_message) = match self {\n            ApiError::InternalServerError => (StatusCode::INTERNAL_SERVER_ERROR, \"Internal server error\"),\n            ApiError::BadRequest(_) => (StatusCode::BAD_REQUEST, \"Bad request\"),\n            ApiError::Unauthorized => (StatusCode::UNAUTHORIZED, \"Unauthorized\"),\n            ApiError::Forbidden => (StatusCode::FORBIDDEN, \"Forbidden\"),\n            ApiError::NotFound => (StatusCode::NOT_FOUND, \"Not found\"),\n            ApiError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Database error\"),\n            ApiError::AuthError(_) => (StatusCode::UNAUTHORIZED, \"Authentication error\"),\n        };\n\n        let body = Json(json!({\n            \"error\": error_message,\n            \"message\": self.to_string()\n        }));\n\n        (status, body).into_response()\n    }\n}\n"}