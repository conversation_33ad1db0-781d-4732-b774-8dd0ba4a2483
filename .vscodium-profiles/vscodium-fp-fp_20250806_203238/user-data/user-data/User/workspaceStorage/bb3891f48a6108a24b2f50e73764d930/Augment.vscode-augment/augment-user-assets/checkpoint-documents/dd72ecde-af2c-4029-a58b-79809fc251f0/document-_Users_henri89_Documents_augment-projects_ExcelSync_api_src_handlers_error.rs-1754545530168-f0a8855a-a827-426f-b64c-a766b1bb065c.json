{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/error.rs"}, "originalCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    J<PERSON>,\n};\nuse serde::{Deserialize, Serialize};\nuse serde_json::json;\nuse std::collections::HashMap;\nuse uuid::Uuid;\n\n/// API error types with enhanced context and details\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n\n    #[error(\"Validation error: {0}\")]\n    ValidationError(ValidationErrorDetails),\n\n    #[error(\"Business rule violation: {0}\")]\n    BusinessRuleViolation(BusinessRuleError),\n\n    #[error(\"Rate limit exceeded: {0}\")]\n    RateLimitExceeded(RateLimitError),\n\n    #[error(\"File processing error: {0}\")]\n    FileProcessingError(FileError),\n\n    #[error(\"External service error: {0}\")]\n    ExternalServiceError(ExternalServiceError),\n\n    #[error(\"Conflict: {0}\")]\n    Conflict(ConflictError),\n\n    #[error(\"Timeout: {0}\")]\n    Timeout(TimeoutError),\n\n    #[error(\"Service unavailable: {0}\")]\n    ServiceUnavailable(String),\n\n    #[error(\"Payment required: {0}\")]\n    PaymentRequired(PaymentError),\n\n    #[error(\"Too many requests: {0}\")]\n    TooManyRequests(String),\n\n    #[error(\"Unprocessable entity: {0}\")]\n    UnprocessableEntity(UnprocessableEntityError),\n}\n\n/// Validation error details\n#[derive(Debug, Serialize, Deserialize)]\npub struct ValidationErrorDetails {\n    pub field_errors: HashMap<String, Vec<FieldError>>,\n    pub global_errors: Vec<String>,\n    pub error_count: u32,\n}\n\nimpl std::fmt::Display for ValidationErrorDetails {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Validation failed with {} errors\", self.error_count)\n    }\n}\n\n/// Field error\n#[derive(Debug, Serialize, Deserialize)]\npub struct FieldError {\n    pub code: String,\n    pub message: String,\n    pub rejected_value: Option<serde_json::Value>,\n    pub constraint: Option<String>,\n}\n\n/// Business rule error\n#[derive(Debug, Serialize, Deserialize)]\npub struct BusinessRuleError {\n    pub rule_id: String,\n    pub rule_name: String,\n    pub violation_type: String,\n    pub affected_fields: Vec<String>,\n    pub message: String,\n    pub severity: String,\n    pub remediation_steps: Vec<String>,\n}\n\nimpl std::fmt::Display for BusinessRuleError {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Business rule '{}' violated: {}\", self.rule_name, self.message)\n    }\n}\n\n/// Rate limit error\n#[derive(Debug, Serialize, Deserialize)]\npub struct RateLimitError {\n    pub limit_type: String,\n    pub current_usage: u64,\n    pub limit: u64,\n    pub reset_time: String,\n    pub retry_after_seconds: u64,\n}\n\nimpl std::fmt::Display for RateLimitError {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Rate limit exceeded: {}/{} for {}\", self.current_usage, self.limit, self.limit_type)\n    }\n}\n\n/// File error\n#[derive(Debug, Serialize, Deserialize)]\npub struct FileError {\n    pub file_name: String,\n    pub file_size: Option<u64>,\n    pub error_type: String,\n    pub supported_formats: Vec<String>,\n    pub max_file_size: Option<u64>,\n}\n\n/// External service error\n#[derive(Debug, Serialize, Deserialize)]\npub struct ExternalServiceError {\n    pub service_name: String,\n    pub error_code: Option<String>,\n    pub message: String,\n    pub retry_possible: bool,\n    pub estimated_recovery_time: Option<String>,\n}\n\n/// Conflict error\n#[derive(Debug, Serialize, Deserialize)]\npub struct ConflictError {\n    pub resource_type: String,\n    pub resource_id: String,\n    pub conflict_type: String,\n    pub current_version: Option<String>,\n    pub attempted_version: Option<String>,\n    pub resolution_suggestions: Vec<String>,\n}\n\n/// Timeout error\n#[derive(Debug, Serialize, Deserialize)]\npub struct TimeoutError {\n    pub operation: String,\n    pub timeout_duration_ms: u64,\n    pub suggestion: String,\n}\n\n/// Payment error\n#[derive(Debug, Serialize, Deserialize)]\npub struct PaymentError {\n    pub payment_method: String,\n    pub error_code: String,\n    pub message: String,\n    pub retry_possible: bool,\n}\n\n/// Unprocessable entity error\n#[derive(Debug, Serialize, Deserialize)]\npub struct UnprocessableEntityError {\n    pub entity_type: String,\n    pub validation_errors: Vec<String>,\n    pub business_rule_violations: Vec<String>,\n}\n\n/// Error response structure\n#[derive(Debug, Serialize)]\npub struct ErrorResponse {\n    pub success: bool,\n    pub error: ErrorInfo,\n    pub timestamp: String,\n    pub request_id: String,\n    pub path: Option<String>,\n    pub method: Option<String>,\n}\n\n/// Error information\n#[derive(Debug, Serialize)]\npub struct ErrorInfo {\n    pub code: String,\n    pub message: String,\n    pub details: Option<serde_json::Value>,\n    pub help_url: Option<String>,\n    pub trace_id: Option<String>,\n}\n\nimpl ApiError {\n    /// Get the HTTP status code for this error\n    pub fn status_code(&self) -> StatusCode {\n        match self {\n            ApiError::BadRequest(_) => StatusCode::BAD_REQUEST,\n            ApiError::Unauthorized(_) => StatusCode::UNAUTHORIZED,\n            ApiError::Forbidden(_) => StatusCode::FORBIDDEN,\n            ApiError::NotFound(_) => StatusCode::NOT_FOUND,\n            ApiError::Conflict(_) => StatusCode::CONFLICT,\n            ApiError::ValidationError(_) => StatusCode::BAD_REQUEST,\n            ApiError::BusinessRuleViolation(_) => StatusCode::UNPROCESSABLE_ENTITY,\n            ApiError::UnprocessableEntity(_) => StatusCode::UNPROCESSABLE_ENTITY,\n            ApiError::RateLimitExceeded(_) => StatusCode::TOO_MANY_REQUESTS,\n            ApiError::TooManyRequests(_) => StatusCode::TOO_MANY_REQUESTS,\n            ApiError::PaymentRequired(_) => StatusCode::PAYMENT_REQUIRED,\n            ApiError::Timeout(_) => StatusCode::REQUEST_TIMEOUT,\n            ApiError::ServiceUnavailable(_) => StatusCode::SERVICE_UNAVAILABLE,\n            ApiError::FileProcessingError(_) => StatusCode::BAD_REQUEST,\n            ApiError::ExternalServiceError(_) => StatusCode::BAD_GATEWAY,\n            ApiError::DatabaseError(_) => StatusCode::INTERNAL_SERVER_ERROR,\n            ApiError::AuthError(_) => StatusCode::UNAUTHORIZED,\n            ApiError::InternalServerError(_) => StatusCode::INTERNAL_SERVER_ERROR,\n        }\n    }\n\n    /// Get the error code for this error\n    pub fn error_code(&self) -> &'static str {\n        match self {\n            ApiError::BadRequest(_) => \"BAD_REQUEST\",\n            ApiError::Unauthorized(_) => \"UNAUTHORIZED\",\n            ApiError::Forbidden(_) => \"FORBIDDEN\",\n            ApiError::NotFound(_) => \"NOT_FOUND\",\n            ApiError::Conflict(_) => \"CONFLICT\",\n            ApiError::ValidationError(_) => \"VALIDATION_ERROR\",\n            ApiError::BusinessRuleViolation(_) => \"BUSINESS_RULE_VIOLATION\",\n            ApiError::UnprocessableEntity(_) => \"UNPROCESSABLE_ENTITY\",\n            ApiError::RateLimitExceeded(_) => \"RATE_LIMIT_EXCEEDED\",\n            ApiError::TooManyRequests(_) => \"TOO_MANY_REQUESTS\",\n            ApiError::PaymentRequired(_) => \"PAYMENT_REQUIRED\",\n            ApiError::Timeout(_) => \"TIMEOUT\",\n            ApiError::ServiceUnavailable(_) => \"SERVICE_UNAVAILABLE\",\n            ApiError::FileProcessingError(_) => \"FILE_PROCESSING_ERROR\",\n            ApiError::ExternalServiceError(_) => \"EXTERNAL_SERVICE_ERROR\",\n            ApiError::DatabaseError(_) => \"DATABASE_ERROR\",\n            ApiError::AuthError(_) => \"AUTH_ERROR\",\n            ApiError::InternalServerError(_) => \"INTERNAL_SERVER_ERROR\",\n        }\n    }\n\n    /// Get error details as JSON value\n    pub fn error_details(&self) -> Option<serde_json::Value> {\n        match self {\n            ApiError::ValidationError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::BusinessRuleViolation(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::RateLimitExceeded(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::FileProcessingError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::ExternalServiceError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::Conflict(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::Timeout(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::PaymentRequired(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::UnprocessableEntity(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            _ => None,\n        }\n    }\n\n    /// Get help URL for this error type\n    pub fn help_url(&self) -> Option<String> {\n        match self {\n            ApiError::ValidationError(_) => Some(\"https://docs.excelsync.com/errors/validation\".to_string()),\n            ApiError::BusinessRuleViolation(_) => Some(\"https://docs.excelsync.com/errors/business-rules\".to_string()),\n            ApiError::RateLimitExceeded(_) => Some(\"https://docs.excelsync.com/errors/rate-limits\".to_string()),\n            ApiError::AuthError(_) => Some(\"https://docs.excelsync.com/errors/authentication\".to_string()),\n            ApiError::PaymentRequired(_) => Some(\"https://docs.excelsync.com/errors/payment\".to_string()),\n            _ => None,\n        }\n    }\n\n    /// Check if this error should be logged\n    pub fn should_log(&self) -> bool {\n        match self {\n            ApiError::InternalServerError(_) => true,\n            ApiError::DatabaseError(_) => true,\n            ApiError::ExternalServiceError(_) => true,\n            ApiError::ServiceUnavailable(_) => true,\n            _ => false,\n        }\n    }\n\n    /// Check if this error should be reported to monitoring\n    pub fn should_report(&self) -> bool {\n        match self {\n            ApiError::InternalServerError(_) => true,\n            ApiError::DatabaseError(_) => true,\n            ApiError::ExternalServiceError(_) => true,\n            ApiError::ServiceUnavailable(_) => true,\n            ApiError::Timeout(_) => true,\n            _ => false,\n        }\n    }\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let status = self.status_code();\n        let request_id = Uuid::new_v4().to_string();\n\n        // Log error if necessary\n        if self.should_log() {\n            tracing::error!(\n                error = %self,\n                error_code = self.error_code(),\n                request_id = %request_id,\n                \"API error occurred\"\n            );\n        }\n\n        let error_response = ErrorResponse {\n            success: false,\n            error: ErrorInfo {\n                code: self.error_code().to_string(),\n                message: self.to_string(),\n                details: self.error_details(),\n                help_url: self.help_url(),\n                trace_id: Some(request_id.clone()),\n            },\n            timestamp: chrono::Utc::now().to_rfc3339(),\n            request_id,\n            path: None, // This could be populated by middleware\n            method: None, // This could be populated by middleware\n        };\n\n        let body = Json(error_response);\n        (status, body).into_response()\n    }\n}\n\n/// Helper functions for creating common errors\nimpl ApiError {\n    /// Create a validation error from validator errors\n    pub fn from_validation_errors(errors: validator::ValidationErrors) -> Self {\n        let mut field_errors = HashMap::new();\n\n        for (field, field_errors_vec) in errors.field_errors() {\n            let mut field_error_list = Vec::new();\n            for error in field_errors_vec {\n                field_error_list.push(FieldError {\n                    code: error.code.to_string(),\n                    message: error.message.as_ref()\n                        .map(|m| m.to_string())\n                        .unwrap_or_else(|| format!(\"Validation failed for field: {}\", field)),\n                    rejected_value: error.params.get(\"value\").cloned(),\n                    constraint: error.params.get(\"constraint\")\n                        .and_then(|v| v.as_str())\n                        .map(|s| s.to_string()),\n                });\n            }\n            field_errors.insert(field.to_string(), field_error_list);\n        }\n\n        ApiError::ValidationError(ValidationErrorDetails {\n            field_errors,\n            global_errors: Vec::new(),\n            error_count: errors.field_errors().len() as u32,\n        })\n    }\n\n    /// Create a not found error for a specific resource\n    pub fn resource_not_found(resource_type: &str, resource_id: &str) -> Self {\n        ApiError::NotFound(format!(\"{} with ID '{}' not found\", resource_type, resource_id))\n    }\n\n    /// Create a conflict error for resource version mismatch\n    pub fn version_conflict(resource_type: &str, resource_id: &str, current_version: &str, attempted_version: &str) -> Self {\n        ApiError::Conflict(ConflictError {\n            resource_type: resource_type.to_string(),\n            resource_id: resource_id.to_string(),\n            conflict_type: \"version_mismatch\".to_string(),\n            current_version: Some(current_version.to_string()),\n            attempted_version: Some(attempted_version.to_string()),\n            resolution_suggestions: vec![\n                \"Fetch the latest version of the resource\".to_string(),\n                \"Merge your changes with the current version\".to_string(),\n                \"Force update if you're certain about the changes\".to_string(),\n            ],\n        })\n    }\n\n    /// Create a rate limit error\n    pub fn rate_limit_exceeded(limit_type: &str, current: u64, limit: u64, reset_time: chrono::DateTime<chrono::Utc>) -> Self {\n        let reset_time_str = reset_time.to_rfc3339();\n        let retry_after = (reset_time - chrono::Utc::now()).num_seconds().max(0) as u64;\n\n        ApiError::RateLimitExceeded(RateLimitError {\n            limit_type: limit_type.to_string(),\n            current_usage: current,\n            limit,\n            reset_time: reset_time_str,\n            retry_after_seconds: retry_after,\n        })\n    }\n\n    /// Create a file processing error\n    pub fn file_too_large(file_name: &str, file_size: u64, max_size: u64) -> Self {\n        ApiError::FileProcessingError(FileError {\n            file_name: file_name.to_string(),\n            file_size: Some(file_size),\n            error_type: \"file_too_large\".to_string(),\n            supported_formats: vec![], // Could be populated based on context\n            max_file_size: Some(max_size),\n        })\n    }\n\n    /// Create an unsupported file format error\n    pub fn unsupported_file_format(file_name: &str, supported_formats: Vec<String>) -> Self {\n        ApiError::FileProcessingError(FileError {\n            file_name: file_name.to_string(),\n            file_size: None,\n            error_type: \"unsupported_format\".to_string(),\n            supported_formats,\n            max_file_size: None,\n        })\n    }\n}\n", "modifiedCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    J<PERSON>,\n};\nuse serde::{Deserialize, Serialize};\nuse serde_json::json;\nuse std::collections::HashMap;\nuse uuid::Uuid;\n\n/// API error types with enhanced context and details\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n\n    #[error(\"Validation error: {0}\")]\n    ValidationError(ValidationErrorDetails),\n\n    #[error(\"Business rule violation: {0}\")]\n    BusinessRuleViolation(BusinessRuleError),\n\n    #[error(\"Rate limit exceeded: {0}\")]\n    RateLimitExceeded(RateLimitError),\n\n    #[error(\"File processing error: {0}\")]\n    FileProcessingError(FileError),\n\n    #[error(\"External service error: {0}\")]\n    ExternalServiceError(ExternalServiceError),\n\n    #[error(\"Conflict: {0}\")]\n    Conflict(ConflictError),\n\n    #[error(\"Timeout: {0}\")]\n    Timeout(TimeoutError),\n\n    #[error(\"Service unavailable: {0}\")]\n    ServiceUnavailable(String),\n\n    #[error(\"Payment required: {0}\")]\n    PaymentRequired(PaymentError),\n\n    #[error(\"Too many requests: {0}\")]\n    TooManyRequests(String),\n\n    #[error(\"Unprocessable entity: {0}\")]\n    UnprocessableEntity(UnprocessableEntityError),\n}\n\n/// Validation error details\n#[derive(Debug, Serialize, Deserialize)]\npub struct ValidationErrorDetails {\n    pub field_errors: HashMap<String, Vec<FieldError>>,\n    pub global_errors: Vec<String>,\n    pub error_count: u32,\n}\n\nimpl std::fmt::Display for ValidationErrorDetails {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Validation failed with {} errors\", self.error_count)\n    }\n}\n\n/// Field error\n#[derive(Debug, Serialize, Deserialize)]\npub struct FieldError {\n    pub code: String,\n    pub message: String,\n    pub rejected_value: Option<serde_json::Value>,\n    pub constraint: Option<String>,\n}\n\n/// Business rule error\n#[derive(Debug, Serialize, Deserialize)]\npub struct BusinessRuleError {\n    pub rule_id: String,\n    pub rule_name: String,\n    pub violation_type: String,\n    pub affected_fields: Vec<String>,\n    pub message: String,\n    pub severity: String,\n    pub remediation_steps: Vec<String>,\n}\n\nimpl std::fmt::Display for BusinessRuleError {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Business rule '{}' violated: {}\", self.rule_name, self.message)\n    }\n}\n\n/// Rate limit error\n#[derive(Debug, Serialize, Deserialize)]\npub struct RateLimitError {\n    pub limit_type: String,\n    pub current_usage: u64,\n    pub limit: u64,\n    pub reset_time: String,\n    pub retry_after_seconds: u64,\n}\n\nimpl std::fmt::Display for RateLimitError {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"Rate limit exceeded: {}/{} for {}\", self.current_usage, self.limit, self.limit_type)\n    }\n}\n\n/// File error\n#[derive(Debug, Serialize, Deserialize)]\npub struct FileError {\n    pub file_name: String,\n    pub file_size: Option<u64>,\n    pub error_type: String,\n    pub supported_formats: Vec<String>,\n    pub max_file_size: Option<u64>,\n}\n\nimpl std::fmt::Display for FileError {\n    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {\n        write!(f, \"File processing error for '{}': {}\", self.file_name, self.error_type)\n    }\n}\n\n/// External service error\n#[derive(Debug, Serialize, Deserialize)]\npub struct ExternalServiceError {\n    pub service_name: String,\n    pub error_code: Option<String>,\n    pub message: String,\n    pub retry_possible: bool,\n    pub estimated_recovery_time: Option<String>,\n}\n\n/// Conflict error\n#[derive(Debug, Serialize, Deserialize)]\npub struct ConflictError {\n    pub resource_type: String,\n    pub resource_id: String,\n    pub conflict_type: String,\n    pub current_version: Option<String>,\n    pub attempted_version: Option<String>,\n    pub resolution_suggestions: Vec<String>,\n}\n\n/// Timeout error\n#[derive(Debug, Serialize, Deserialize)]\npub struct TimeoutError {\n    pub operation: String,\n    pub timeout_duration_ms: u64,\n    pub suggestion: String,\n}\n\n/// Payment error\n#[derive(Debug, Serialize, Deserialize)]\npub struct PaymentError {\n    pub payment_method: String,\n    pub error_code: String,\n    pub message: String,\n    pub retry_possible: bool,\n}\n\n/// Unprocessable entity error\n#[derive(Debug, Serialize, Deserialize)]\npub struct UnprocessableEntityError {\n    pub entity_type: String,\n    pub validation_errors: Vec<String>,\n    pub business_rule_violations: Vec<String>,\n}\n\n/// Error response structure\n#[derive(Debug, Serialize)]\npub struct ErrorResponse {\n    pub success: bool,\n    pub error: ErrorInfo,\n    pub timestamp: String,\n    pub request_id: String,\n    pub path: Option<String>,\n    pub method: Option<String>,\n}\n\n/// Error information\n#[derive(Debug, Serialize)]\npub struct ErrorInfo {\n    pub code: String,\n    pub message: String,\n    pub details: Option<serde_json::Value>,\n    pub help_url: Option<String>,\n    pub trace_id: Option<String>,\n}\n\nimpl ApiError {\n    /// Get the HTTP status code for this error\n    pub fn status_code(&self) -> StatusCode {\n        match self {\n            ApiError::BadRequest(_) => StatusCode::BAD_REQUEST,\n            ApiError::Unauthorized(_) => StatusCode::UNAUTHORIZED,\n            ApiError::Forbidden(_) => StatusCode::FORBIDDEN,\n            ApiError::NotFound(_) => StatusCode::NOT_FOUND,\n            ApiError::Conflict(_) => StatusCode::CONFLICT,\n            ApiError::ValidationError(_) => StatusCode::BAD_REQUEST,\n            ApiError::BusinessRuleViolation(_) => StatusCode::UNPROCESSABLE_ENTITY,\n            ApiError::UnprocessableEntity(_) => StatusCode::UNPROCESSABLE_ENTITY,\n            ApiError::RateLimitExceeded(_) => StatusCode::TOO_MANY_REQUESTS,\n            ApiError::TooManyRequests(_) => StatusCode::TOO_MANY_REQUESTS,\n            ApiError::PaymentRequired(_) => StatusCode::PAYMENT_REQUIRED,\n            ApiError::Timeout(_) => StatusCode::REQUEST_TIMEOUT,\n            ApiError::ServiceUnavailable(_) => StatusCode::SERVICE_UNAVAILABLE,\n            ApiError::FileProcessingError(_) => StatusCode::BAD_REQUEST,\n            ApiError::ExternalServiceError(_) => StatusCode::BAD_GATEWAY,\n            ApiError::DatabaseError(_) => StatusCode::INTERNAL_SERVER_ERROR,\n            ApiError::AuthError(_) => StatusCode::UNAUTHORIZED,\n            ApiError::InternalServerError(_) => StatusCode::INTERNAL_SERVER_ERROR,\n        }\n    }\n\n    /// Get the error code for this error\n    pub fn error_code(&self) -> &'static str {\n        match self {\n            ApiError::BadRequest(_) => \"BAD_REQUEST\",\n            ApiError::Unauthorized(_) => \"UNAUTHORIZED\",\n            ApiError::Forbidden(_) => \"FORBIDDEN\",\n            ApiError::NotFound(_) => \"NOT_FOUND\",\n            ApiError::Conflict(_) => \"CONFLICT\",\n            ApiError::ValidationError(_) => \"VALIDATION_ERROR\",\n            ApiError::BusinessRuleViolation(_) => \"BUSINESS_RULE_VIOLATION\",\n            ApiError::UnprocessableEntity(_) => \"UNPROCESSABLE_ENTITY\",\n            ApiError::RateLimitExceeded(_) => \"RATE_LIMIT_EXCEEDED\",\n            ApiError::TooManyRequests(_) => \"TOO_MANY_REQUESTS\",\n            ApiError::PaymentRequired(_) => \"PAYMENT_REQUIRED\",\n            ApiError::Timeout(_) => \"TIMEOUT\",\n            ApiError::ServiceUnavailable(_) => \"SERVICE_UNAVAILABLE\",\n            ApiError::FileProcessingError(_) => \"FILE_PROCESSING_ERROR\",\n            ApiError::ExternalServiceError(_) => \"EXTERNAL_SERVICE_ERROR\",\n            ApiError::DatabaseError(_) => \"DATABASE_ERROR\",\n            ApiError::AuthError(_) => \"AUTH_ERROR\",\n            ApiError::InternalServerError(_) => \"INTERNAL_SERVER_ERROR\",\n        }\n    }\n\n    /// Get error details as JSON value\n    pub fn error_details(&self) -> Option<serde_json::Value> {\n        match self {\n            ApiError::ValidationError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::BusinessRuleViolation(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::RateLimitExceeded(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::FileProcessingError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::ExternalServiceError(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::Conflict(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::Timeout(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::PaymentRequired(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            ApiError::UnprocessableEntity(details) => Some(serde_json::to_value(details).unwrap_or_default()),\n            _ => None,\n        }\n    }\n\n    /// Get help URL for this error type\n    pub fn help_url(&self) -> Option<String> {\n        match self {\n            ApiError::ValidationError(_) => Some(\"https://docs.excelsync.com/errors/validation\".to_string()),\n            ApiError::BusinessRuleViolation(_) => Some(\"https://docs.excelsync.com/errors/business-rules\".to_string()),\n            ApiError::RateLimitExceeded(_) => Some(\"https://docs.excelsync.com/errors/rate-limits\".to_string()),\n            ApiError::AuthError(_) => Some(\"https://docs.excelsync.com/errors/authentication\".to_string()),\n            ApiError::PaymentRequired(_) => Some(\"https://docs.excelsync.com/errors/payment\".to_string()),\n            _ => None,\n        }\n    }\n\n    /// Check if this error should be logged\n    pub fn should_log(&self) -> bool {\n        match self {\n            ApiError::InternalServerError(_) => true,\n            ApiError::DatabaseError(_) => true,\n            ApiError::ExternalServiceError(_) => true,\n            ApiError::ServiceUnavailable(_) => true,\n            _ => false,\n        }\n    }\n\n    /// Check if this error should be reported to monitoring\n    pub fn should_report(&self) -> bool {\n        match self {\n            ApiError::InternalServerError(_) => true,\n            ApiError::DatabaseError(_) => true,\n            ApiError::ExternalServiceError(_) => true,\n            ApiError::ServiceUnavailable(_) => true,\n            ApiError::Timeout(_) => true,\n            _ => false,\n        }\n    }\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let status = self.status_code();\n        let request_id = Uuid::new_v4().to_string();\n\n        // Log error if necessary\n        if self.should_log() {\n            tracing::error!(\n                error = %self,\n                error_code = self.error_code(),\n                request_id = %request_id,\n                \"API error occurred\"\n            );\n        }\n\n        let error_response = ErrorResponse {\n            success: false,\n            error: ErrorInfo {\n                code: self.error_code().to_string(),\n                message: self.to_string(),\n                details: self.error_details(),\n                help_url: self.help_url(),\n                trace_id: Some(request_id.clone()),\n            },\n            timestamp: chrono::Utc::now().to_rfc3339(),\n            request_id,\n            path: None, // This could be populated by middleware\n            method: None, // This could be populated by middleware\n        };\n\n        let body = Json(error_response);\n        (status, body).into_response()\n    }\n}\n\n/// Helper functions for creating common errors\nimpl ApiError {\n    /// Create a validation error from validator errors\n    pub fn from_validation_errors(errors: validator::ValidationErrors) -> Self {\n        let mut field_errors = HashMap::new();\n\n        for (field, field_errors_vec) in errors.field_errors() {\n            let mut field_error_list = Vec::new();\n            for error in field_errors_vec {\n                field_error_list.push(FieldError {\n                    code: error.code.to_string(),\n                    message: error.message.as_ref()\n                        .map(|m| m.to_string())\n                        .unwrap_or_else(|| format!(\"Validation failed for field: {}\", field)),\n                    rejected_value: error.params.get(\"value\").cloned(),\n                    constraint: error.params.get(\"constraint\")\n                        .and_then(|v| v.as_str())\n                        .map(|s| s.to_string()),\n                });\n            }\n            field_errors.insert(field.to_string(), field_error_list);\n        }\n\n        ApiError::ValidationError(ValidationErrorDetails {\n            field_errors,\n            global_errors: Vec::new(),\n            error_count: errors.field_errors().len() as u32,\n        })\n    }\n\n    /// Create a not found error for a specific resource\n    pub fn resource_not_found(resource_type: &str, resource_id: &str) -> Self {\n        ApiError::NotFound(format!(\"{} with ID '{}' not found\", resource_type, resource_id))\n    }\n\n    /// Create a conflict error for resource version mismatch\n    pub fn version_conflict(resource_type: &str, resource_id: &str, current_version: &str, attempted_version: &str) -> Self {\n        ApiError::Conflict(ConflictError {\n            resource_type: resource_type.to_string(),\n            resource_id: resource_id.to_string(),\n            conflict_type: \"version_mismatch\".to_string(),\n            current_version: Some(current_version.to_string()),\n            attempted_version: Some(attempted_version.to_string()),\n            resolution_suggestions: vec![\n                \"Fetch the latest version of the resource\".to_string(),\n                \"Merge your changes with the current version\".to_string(),\n                \"Force update if you're certain about the changes\".to_string(),\n            ],\n        })\n    }\n\n    /// Create a rate limit error\n    pub fn rate_limit_exceeded(limit_type: &str, current: u64, limit: u64, reset_time: chrono::DateTime<chrono::Utc>) -> Self {\n        let reset_time_str = reset_time.to_rfc3339();\n        let retry_after = (reset_time - chrono::Utc::now()).num_seconds().max(0) as u64;\n\n        ApiError::RateLimitExceeded(RateLimitError {\n            limit_type: limit_type.to_string(),\n            current_usage: current,\n            limit,\n            reset_time: reset_time_str,\n            retry_after_seconds: retry_after,\n        })\n    }\n\n    /// Create a file processing error\n    pub fn file_too_large(file_name: &str, file_size: u64, max_size: u64) -> Self {\n        ApiError::FileProcessingError(FileError {\n            file_name: file_name.to_string(),\n            file_size: Some(file_size),\n            error_type: \"file_too_large\".to_string(),\n            supported_formats: vec![], // Could be populated based on context\n            max_file_size: Some(max_size),\n        })\n    }\n\n    /// Create an unsupported file format error\n    pub fn unsupported_file_format(file_name: &str, supported_formats: Vec<String>) -> Self {\n        ApiError::FileProcessingError(FileError {\n            file_name: file_name.to_string(),\n            file_size: None,\n            error_type: \"unsupported_format\".to_string(),\n            supported_formats,\n            max_file_size: None,\n        })\n    }\n}\n"}