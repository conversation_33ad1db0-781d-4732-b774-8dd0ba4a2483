{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/load_test.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nLoad testing script for ExcelSync API\nTests concurrent users and requests per second performance\n\"\"\"\n\nimport asyncio\nimport aiohttp\nimport time\nimport json\nimport statistics\nfrom typing import List, Dict, Any\nfrom dataclasses import dataclass\nimport argparse\nimport sys\n\n@dataclass\nclass TestResult:\n    \"\"\"Result of a single API request\"\"\"\n    status_code: int\n    response_time: float\n    success: bool\n    error: str = \"\"\n\n@dataclass\nclass LoadTestConfig:\n    \"\"\"Configuration for load testing\"\"\"\n    base_url: str = \"http://localhost:3000\"\n    concurrent_users: int = 100\n    requests_per_user: int = 10\n    test_duration: int = 60  # seconds\n    endpoints: List[str] = None\n    \n    def __post_init__(self):\n        if self.endpoints is None:\n            self.endpoints = [\n                \"/health\",\n                \"/auth/login\",\n                \"/users\",\n                \"/projects\",\n                \"/templates\"\n            ]\n\nclass LoadTester:\n    \"\"\"Load testing implementation\"\"\"\n    \n    def __init__(self, config: LoadTestConfig):\n        self.config = config\n        self.results: List[TestResult] = []\n        self.start_time = None\n        self.end_time = None\n        \n    async def make_request(self, session: aiohttp.ClientSession, endpoint: str, method: str = \"GET\", \n                          headers: Dict[str, str] = None, data: Dict[str, Any] = None) -> TestResult:\n        \"\"\"Make a single HTTP request and measure performance\"\"\"\n        start_time = time.time()\n        \n        try:\n            if method.upper() == \"POST\":\n                async with session.post(\n                    f\"{self.config.base_url}{endpoint}\",\n                    headers=headers or {},\n                    json=data\n                ) as response:\n                    await response.text()  # Read response body\n                    response_time = time.time() - start_time\n                    return TestResult(\n                        status_code=response.status,\n                        response_time=response_time,\n                        success=200 <= response.status < 400\n                    )\n            else:\n                async with session.get(\n                    f\"{self.config.base_url}{endpoint}\",\n                    headers=headers or {}\n                ) as response:\n                    await response.text()  # Read response body\n                    response_time = time.time() - start_time\n                    return TestResult(\n                        status_code=response.status,\n                        response_time=response_time,\n                        success=200 <= response.status < 400\n                    )\n                    \n        except Exception as e:\n            response_time = time.time() - start_time\n            return TestResult(\n                status_code=0,\n                response_time=response_time,\n                success=False,\n                error=str(e)\n            )\n    \n    async def simulate_user(self, session: aiohttp.ClientSession, user_id: int) -> List[TestResult]:\n        \"\"\"Simulate a single user making multiple requests\"\"\"\n        user_results = []\n        \n        # Simulate user login\n        login_data = {\n            \"email\": f\"testuser{user_id}@example.com\",\n            \"password\": \"testpassword123\"\n        }\n        \n        login_result = await self.make_request(\n            session, \n            \"/auth/login\", \n            method=\"POST\", \n            data=login_data\n        )\n        user_results.append(login_result)\n        \n        # Extract token if login was successful (in real scenario)\n        headers = {\"Authorization\": \"Bearer fake-token-for-testing\"}\n        \n        # Make requests to various endpoints\n        for _ in range(self.config.requests_per_user):\n            for endpoint in self.config.endpoints:\n                if endpoint != \"/auth/login\":  # Skip login endpoint in loop\n                    result = await self.make_request(session, endpoint, headers=headers)\n                    user_results.append(result)\n                    \n                    # Small delay between requests to simulate real user behavior\n                    await asyncio.sleep(0.1)\n        \n        return user_results\n    \n    async def run_load_test(self) -> None:\n        \"\"\"Run the complete load test\"\"\"\n        print(f\"Starting load test with {self.config.concurrent_users} concurrent users\")\n        print(f\"Each user will make {self.config.requests_per_user} requests to each endpoint\")\n        print(f\"Testing endpoints: {', '.join(self.config.endpoints)}\")\n        print(f\"Target URL: {self.config.base_url}\")\n        print(\"-\" * 60)\n        \n        self.start_time = time.time()\n        \n        # Create HTTP session with connection pooling\n        connector = aiohttp.TCPConnector(\n            limit=self.config.concurrent_users * 2,\n            limit_per_host=self.config.concurrent_users\n        )\n        \n        timeout = aiohttp.ClientTimeout(total=30)\n        \n        async with aiohttp.ClientSession(\n            connector=connector,\n            timeout=timeout\n        ) as session:\n            # Create tasks for all concurrent users\n            tasks = []\n            for user_id in range(self.config.concurrent_users):\n                task = asyncio.create_task(self.simulate_user(session, user_id))\n                tasks.append(task)\n            \n            # Wait for all users to complete\n            print(\"Running load test...\")\n            user_results = await asyncio.gather(*tasks, return_exceptions=True)\n            \n            # Collect all results\n            for results in user_results:\n                if isinstance(results, list):\n                    self.results.extend(results)\n                else:\n                    print(f\"Error in user simulation: {results}\")\n        \n        self.end_time = time.time()\n        \n    def generate_report(self) -> None:\n        \"\"\"Generate and print load test report\"\"\"\n        if not self.results:\n            print(\"No results to report!\")\n            return\n            \n        total_duration = self.end_time - self.start_time\n        total_requests = len(self.results)\n        successful_requests = sum(1 for r in self.results if r.success)\n        failed_requests = total_requests - successful_requests\n        \n        response_times = [r.response_time for r in self.results if r.success]\n        \n        print(\"\\n\" + \"=\" * 60)\n        print(\"LOAD TEST RESULTS\")\n        print(\"=\" * 60)\n        \n        print(f\"Test Duration: {total_duration:.2f} seconds\")\n        print(f\"Concurrent Users: {self.config.concurrent_users}\")\n        print(f\"Total Requests: {total_requests}\")\n        print(f\"Successful Requests: {successful_requests}\")\n        print(f\"Failed Requests: {failed_requests}\")\n        print(f\"Success Rate: {(successful_requests/total_requests)*100:.2f}%\")\n        print(f\"Requests per Second: {total_requests/total_duration:.2f}\")\n        \n        if response_times:\n            print(\"\\nResponse Time Statistics:\")\n            print(f\"  Average: {statistics.mean(response_times)*1000:.2f} ms\")\n            print(f\"  Median: {statistics.median(response_times)*1000:.2f} ms\")\n            print(f\"  Min: {min(response_times)*1000:.2f} ms\")\n            print(f\"  Max: {max(response_times)*1000:.2f} ms\")\n            print(f\"  95th Percentile: {statistics.quantiles(response_times, n=20)[18]*1000:.2f} ms\")\n            print(f\"  99th Percentile: {statistics.quantiles(response_times, n=100)[98]*1000:.2f} ms\")\n        \n        # Status code breakdown\n        status_codes = {}\n        for result in self.results:\n            status_codes[result.status_code] = status_codes.get(result.status_code, 0) + 1\n        \n        print(\"\\nStatus Code Breakdown:\")\n        for status_code, count in sorted(status_codes.items()):\n            print(f\"  {status_code}: {count} requests\")\n        \n        # Error breakdown\n        errors = {}\n        for result in self.results:\n            if not result.success and result.error:\n                errors[result.error] = errors.get(result.error, 0) + 1\n        \n        if errors:\n            print(\"\\nError Breakdown:\")\n            for error, count in sorted(errors.items()):\n                print(f\"  {error}: {count} occurrences\")\n        \n        # Performance assessment\n        print(\"\\nPerformance Assessment:\")\n        if response_times:\n            avg_response_time = statistics.mean(response_times) * 1000\n            p95_response_time = statistics.quantiles(response_times, n=20)[18] * 1000\n            \n            if avg_response_time < 200:\n                print(\"  ✅ Average response time is excellent (< 200ms)\")\n            elif avg_response_time < 500:\n                print(\"  ⚠️  Average response time is acceptable (< 500ms)\")\n            else:\n                print(\"  ❌ Average response time is poor (> 500ms)\")\n                \n            if p95_response_time < 500:\n                print(\"  ✅ 95th percentile response time is excellent (< 500ms)\")\n            elif p95_response_time < 1000:\n                print(\"  ⚠️  95th percentile response time is acceptable (< 1000ms)\")\n            else:\n                print(\"  ❌ 95th percentile response time is poor (> 1000ms)\")\n        \n        success_rate = (successful_requests/total_requests)*100\n        if success_rate >= 99:\n            print(\"  ✅ Success rate is excellent (>= 99%)\")\n        elif success_rate >= 95:\n            print(\"  ⚠️  Success rate is acceptable (>= 95%)\")\n        else:\n            print(\"  ❌ Success rate is poor (< 95%)\")\n        \n        rps = total_requests/total_duration\n        if rps >= 1000:\n            print(\"  ✅ Request throughput is excellent (>= 1000 RPS)\")\n        elif rps >= 500:\n            print(\"  ⚠️  Request throughput is acceptable (>= 500 RPS)\")\n        else:\n            print(\"  ❌ Request throughput is poor (< 500 RPS)\")\n\nasync def main():\n    \"\"\"Main entry point\"\"\"\n    parser = argparse.ArgumentParser(description=\"Load test ExcelSync API\")\n    parser.add_argument(\"--url\", default=\"http://localhost:3000\", help=\"Base URL for API\")\n    parser.add_argument(\"--users\", type=int, default=100, help=\"Number of concurrent users\")\n    parser.add_argument(\"--requests\", type=int, default=10, help=\"Requests per user per endpoint\")\n    parser.add_argument(\"--duration\", type=int, default=60, help=\"Test duration in seconds\")\n    \n    args = parser.parse_args()\n    \n    config = LoadTestConfig(\n        base_url=args.url,\n        concurrent_users=args.users,\n        requests_per_user=args.requests,\n        test_duration=args.duration\n    )\n    \n    tester = LoadTester(config)\n    \n    try:\n        await tester.run_load_test()\n        tester.generate_report()\n    except KeyboardInterrupt:\n        print(\"\\nLoad test interrupted by user\")\n        if tester.results:\n            tester.generate_report()\n    except Exception as e:\n        print(f\"Load test failed: {e}\")\n        sys.exit(1)\n\nif __name__ == \"__main__\":\n    asyncio.run(main())\n"}