{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/report_service.rs"}, "originalCode": "use anyhow::Result;\nuse sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, QueryOrder, PaginatorTrait};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse std::collections::HashMap;\nuse std::path::PathBuf;\nuse tokio::fs;\nuse xlsxwriter::{Workbook, Format};\n\nuse crate::{\n    dto::reports::*,\n    handlers::ApiError,\n};\nuse database::entities::{projects, users, organizations, templates, audit_logs, financial_calculations};\n\n/// Report service for generating various types of reports\npub struct ReportService {\n    db: DatabaseConnection,\n    temp_dir: PathBuf,\n    reports_dir: PathBuf,\n}\n\nimpl ReportService {\n    pub fn new(db: DatabaseConnection) -> Result<Self, ReportError> {\n        let temp_dir = std::env::temp_dir().join(\"excelsync_reports\");\n        let reports_dir = PathBuf::from(\"./reports\");\n        \n        // Create directories if they don't exist\n        std::fs::create_dir_all(&temp_dir)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create temp directory: {}\", e)))?;\n        std::fs::create_dir_all(&reports_dir)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create reports directory: {}\", e)))?;\n\n        Ok(Self {\n            db,\n            temp_dir,\n            reports_dir,\n        })\n    }\n\n    /// Generate a report based on the request\n    pub async fn generate_report(\n        &self,\n        request: ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportGenerationResponse, ReportError> {\n        let report_id = Uuid::new_v4().to_string();\n        let start_time = std::time::Instant::now();\n\n        // Collect data based on report type\n        let report_data = self.collect_report_data(&request, user_id).await?;\n        \n        // Generate file based on format\n        let file_path = self.generate_report_file(&report_id, &request.format, &report_data).await?;\n        \n        // Calculate file size\n        let file_size = fs::metadata(&file_path).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to get file size: {}\", e)))?\n            .len();\n\n        let processing_time = start_time.elapsed().as_millis() as u64;\n        let expires_at = Utc::now() + chrono::Duration::hours(24); // Reports expire after 24 hours\n\n        Ok(ReportGenerationResponse {\n            report_id: report_id.clone(),\n            report_type: request.report_type.clone(),\n            format: request.format.clone(),\n            status: ReportStatus::Completed,\n            generated_at: Utc::now().to_rfc3339(),\n            generated_by: user_id.to_string(),\n            file_size: Some(file_size),\n            download_url: Some(format!(\"/api/v1/reports/{}/download\", report_id)),\n            expires_at: Some(expires_at.to_rfc3339()),\n            metadata: ReportMetadata {\n                total_records: report_data.rows.len() as u64,\n                processing_time_ms: processing_time,\n                data_sources: self.get_data_sources(&request.report_type),\n                filters_applied: self.get_applied_filters(&request.filters),\n                columns: report_data.headers.iter().map(|h| ReportColumn {\n                    name: h.clone(),\n                    data_type: \"string\".to_string(),\n                    description: None,\n                }).collect(),\n            },\n        })\n    }\n\n    /// Collect data for the report based on type\n    async fn collect_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        match &request.report_type {\n            ReportType::ProjectSummary => self.collect_project_summary_data(request, user_id).await,\n            ReportType::FinancialAnalysis => self.collect_financial_analysis_data(request, user_id).await,\n            ReportType::AuditReport => self.collect_audit_report_data(request, user_id).await,\n            ReportType::TemplateUsage => self.collect_template_usage_data(request, user_id).await,\n            ReportType::UserActivity => self.collect_user_activity_data(request, user_id).await,\n            ReportType::SystemPerformance => self.collect_system_performance_data(request, user_id).await,\n            ReportType::ComplianceReport => self.collect_compliance_report_data(request, user_id).await,\n            ReportType::CustomReport => self.collect_custom_report_data(request, user_id).await,\n        }\n    }\n\n    /// Collect project summary data\n    async fn collect_project_summary_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = projects::Entity::find();\n\n        // Apply filters if provided\n        if let Some(filters) = &request.filters {\n            if let Some(project_ids) = &filters.project_ids {\n                let uuids: Result<Vec<Uuid>, _> = project_ids.iter()\n                    .map(|id| Uuid::parse_str(id))\n                    .collect();\n                let uuids = uuids.map_err(|_| ReportError::InvalidConfiguration(\"Invalid project ID format\".to_string()))?;\n                query = query.filter(projects::Column::Id.is_in(uuids));\n            }\n        }\n\n        let projects = query.all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Project ID\".to_string(),\n            \"Name\".to_string(),\n            \"Description\".to_string(),\n            \"Status\".to_string(),\n            \"Owner ID\".to_string(),\n            \"Organization ID\".to_string(),\n            \"Created At\".to_string(),\n            \"Updated At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = projects.into_iter().map(|project| {\n            vec![\n                project.id.to_string(),\n                project.name,\n                project.description.unwrap_or_default(),\n                project.status.to_string(),\n                project.owner_id.to_string(),\n                project.organization_id.map(|id| id.to_string()).unwrap_or_default(),\n                project.created_at.to_rfc3339(),\n                project.updated_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0, // Will be set by caller\n                processing_time_ms: 0, // Will be set by caller\n                data_sources: vec![\"projects\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect financial analysis data\n    async fn collect_financial_analysis_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = financial_calculations::Entity::find();\n\n        // Apply filters if provided\n        if let Some(filters) = &request.filters {\n            if let Some(project_ids) = &filters.project_ids {\n                let uuids: Result<Vec<Uuid>, _> = project_ids.iter()\n                    .map(|id| Uuid::parse_str(id))\n                    .collect();\n                let uuids = uuids.map_err(|_| ReportError::InvalidConfiguration(\"Invalid project ID format\".to_string()))?;\n                query = query.filter(financial_calculations::Column::ProjectId.is_in(uuids));\n            }\n        }\n\n        let calculations = query.all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Calculation ID\".to_string(),\n            \"Project ID\".to_string(),\n            \"Calculation Type\".to_string(),\n            \"Calculated By\".to_string(),\n            \"Calculated At\".to_string(),\n            \"Results Summary\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = calculations.into_iter().map(|calc| {\n            vec![\n                calc.id.to_string(),\n                calc.project_id.to_string(),\n                calc.calculation_type,\n                calc.calculated_by.to_string(),\n                calc.calculated_at.to_rfc3339(),\n                // Extract summary from results JSON\n                calc.results.get(\"summary\")\n                    .and_then(|s| s.as_str())\n                    .unwrap_or(\"N/A\")\n                    .to_string(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"financial_calculations\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect audit report data\n    async fn collect_audit_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = audit_logs::Entity::find();\n\n        // Apply date range filter if provided\n        if let Some(filters) = &request.filters {\n            if let Some(date_range) = &filters.date_range {\n                if let (Ok(start), Ok(end)) = (\n                    DateTime::parse_from_rfc3339(&date_range.start_date),\n                    DateTime::parse_from_rfc3339(&date_range.end_date)\n                ) {\n                    query = query\n                        .filter(audit_logs::Column::CreatedAt.gte(start.with_timezone(&Utc)))\n                        .filter(audit_logs::Column::CreatedAt.lte(end.with_timezone(&Utc)));\n                }\n            }\n        }\n\n        let logs = query.order_by_desc(audit_logs::Column::CreatedAt)\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Log ID\".to_string(),\n            \"User ID\".to_string(),\n            \"Action\".to_string(),\n            \"Entity Type\".to_string(),\n            \"Entity ID\".to_string(),\n            \"IP Address\".to_string(),\n            \"User Agent\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = logs.into_iter().map(|log| {\n            vec![\n                log.id.to_string(),\n                log.user_id.to_string(),\n                log.action.to_string(),\n                log.entity_type,\n                log.entity_id.map(|id| id.to_string()).unwrap_or_default(),\n                log.ip_address,\n                log.user_agent,\n                log.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"audit_logs\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect template usage data\n    async fn collect_template_usage_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let templates = templates::Entity::find()\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Template ID\".to_string(),\n            \"Name\".to_string(),\n            \"Type\".to_string(),\n            \"Version\".to_string(),\n            \"Is Active\".to_string(),\n            \"Is Default\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = templates.into_iter().map(|template| {\n            vec![\n                template.id.to_string(),\n                template.name,\n                template.template_type.to_string(),\n                template.version,\n                template.is_active.to_string(),\n                template.is_default.to_string(),\n                template.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"templates\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect user activity data\n    async fn collect_user_activity_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let users = users::Entity::find()\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"User ID\".to_string(),\n            \"Email\".to_string(),\n            \"Full Name\".to_string(),\n            \"Role\".to_string(),\n            \"Is Active\".to_string(),\n            \"Last Login\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = users.into_iter().map(|user| {\n            vec![\n                user.id.to_string(),\n                user.email,\n                user.full_name,\n                user.role.to_string(),\n                user.is_active.to_string(),\n                user.last_login.map(|dt| dt.to_rfc3339()).unwrap_or_default(),\n                user.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"users\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect system performance data (placeholder)\n    async fn collect_system_performance_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // This would typically collect metrics from monitoring systems\n        let headers = vec![\n            \"Metric\".to_string(),\n            \"Value\".to_string(),\n            \"Unit\".to_string(),\n            \"Timestamp\".to_string(),\n        ];\n\n        let rows = vec![\n            vec![\"CPU Usage\".to_string(), \"45.2\".to_string(), \"%\".to_string(), Utc::now().to_rfc3339()],\n            vec![\"Memory Usage\".to_string(), \"2.1\".to_string(), \"GB\".to_string(), Utc::now().to_rfc3339()],\n            vec![\"Database Connections\".to_string(), \"15\".to_string(), \"count\".to_string(), Utc::now().to_rfc3339()],\n        ];\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"system_metrics\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect compliance report data\n    async fn collect_compliance_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // For compliance, we'll combine audit logs with user activity\n        let audit_data = self.collect_audit_report_data(request, user_id).await?;\n\n        // Add compliance-specific headers\n        let mut headers = audit_data.headers;\n        headers.push(\"Compliance Status\".to_string());\n        headers.push(\"Risk Level\".to_string());\n\n        let rows: Vec<Vec<String>> = audit_data.rows.into_iter().map(|mut row| {\n            // Add compliance analysis (simplified)\n            row.push(\"Compliant\".to_string());\n            row.push(\"Low\".to_string());\n            row\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"audit_logs\".to_string(), \"compliance_rules\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect custom report data\n    async fn collect_custom_report_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // Placeholder for custom reports\n        let headers = vec![\"Custom Field 1\".to_string(), \"Custom Field 2\".to_string()];\n        let rows = vec![vec![\"Value 1\".to_string(), \"Value 2\".to_string()]];\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"custom\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Generate report file based on format\n    async fn generate_report_file(\n        &self,\n        report_id: &str,\n        format: &ReportFormat,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        match format {\n            ReportFormat::Excel => self.generate_excel_file(report_id, data).await,\n            ReportFormat::Csv => self.generate_csv_file(report_id, data).await,\n            ReportFormat::Json => self.generate_json_file(report_id, data).await,\n            ReportFormat::Pdf => self.generate_pdf_file(report_id, data).await,\n        }\n    }\n\n    /// Generate Excel file\n    async fn generate_excel_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.xlsx\", report_id));\n\n        let workbook = Workbook::new(&file_path.to_string_lossy())\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create Excel workbook: {}\", e)))?;\n\n        let mut worksheet = workbook.add_worksheet(Some(\"Report\"))\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to add worksheet: {}\", e)))?;\n\n        // Create header format\n        let mut header_format = Format::new();\n        header_format.set_bold();\n\n        // Write headers\n        for (col, header) in data.headers.iter().enumerate() {\n            worksheet.write_string(0, col as u16, header, Some(&header_format))\n                .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write header: {}\", e)))?;\n        }\n\n        // Write data rows\n        for (row_idx, row) in data.rows.iter().enumerate() {\n            for (col_idx, cell) in row.iter().enumerate() {\n                worksheet.write_string((row_idx + 1) as u32, col_idx as u16, cell, None)\n                    .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write cell: {}\", e)))?;\n            }\n        }\n\n        // Auto-fit columns\n        for col in 0..data.headers.len() {\n            worksheet.set_column(col as u16, col as u16, 15.0, None)\n                .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to set column width: {}\", e)))?;\n        }\n\n        workbook.close()\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to close workbook: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate CSV file\n    async fn generate_csv_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.csv\", report_id));\n\n        let mut csv_content = String::new();\n\n        // Write headers\n        csv_content.push_str(&data.headers.join(\",\"));\n        csv_content.push('\\n');\n\n        // Write data rows\n        for row in &data.rows {\n            // Escape commas and quotes in CSV data\n            let escaped_row: Vec<String> = row.iter().map(|cell| {\n                if cell.contains(',') || cell.contains('\"') || cell.contains('\\n') {\n                    format!(\"\\\"{}\\\"\", cell.replace('\"', \"\\\"\\\"\"))\n                } else {\n                    cell.clone()\n                }\n            }).collect();\n            csv_content.push_str(&escaped_row.join(\",\"));\n            csv_content.push('\\n');\n        }\n\n        fs::write(&file_path, csv_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write CSV file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate JSON file\n    async fn generate_json_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.json\", report_id));\n\n        // Convert data to JSON structure\n        let json_data: Vec<HashMap<String, String>> = data.rows.iter().map(|row| {\n            data.headers.iter().zip(row.iter())\n                .map(|(header, value)| (header.clone(), value.clone()))\n                .collect()\n        }).collect();\n\n        let json_content = serde_json::to_string_pretty(&json_data)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to serialize JSON: {}\", e)))?;\n\n        fs::write(&file_path, json_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write JSON file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate PDF file (placeholder implementation)\n    async fn generate_pdf_file(\n        &self,\n        report_id: &str,\n        _data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.pdf\", report_id));\n\n        // For now, create a simple text file as PDF generation requires additional dependencies\n        let pdf_content = \"PDF generation not yet implemented. This is a placeholder.\";\n\n        fs::write(&file_path, pdf_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write PDF file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Get data sources for a report type\n    fn get_data_sources(&self, report_type: &ReportType) -> Vec<String> {\n        match report_type {\n            ReportType::ProjectSummary => vec![\"projects\".to_string()],\n            ReportType::FinancialAnalysis => vec![\"financial_calculations\".to_string()],\n            ReportType::AuditReport => vec![\"audit_logs\".to_string()],\n            ReportType::TemplateUsage => vec![\"templates\".to_string()],\n            ReportType::UserActivity => vec![\"users\".to_string()],\n            ReportType::SystemPerformance => vec![\"system_metrics\".to_string()],\n            ReportType::ComplianceReport => vec![\"audit_logs\".to_string(), \"compliance_rules\".to_string()],\n            ReportType::CustomReport => vec![\"custom\".to_string()],\n        }\n    }\n\n    /// Get applied filters description\n    fn get_applied_filters(&self, filters: &Option<ReportFilters>) -> Vec<String> {\n        let mut applied = Vec::new();\n\n        if let Some(filters) = filters {\n            if filters.date_range.is_some() {\n                applied.push(\"Date Range\".to_string());\n            }\n            if filters.project_ids.is_some() {\n                applied.push(\"Project IDs\".to_string());\n            }\n            if filters.user_ids.is_some() {\n                applied.push(\"User IDs\".to_string());\n            }\n            if filters.organization_ids.is_some() {\n                applied.push(\"Organization IDs\".to_string());\n            }\n            if filters.template_ids.is_some() {\n                applied.push(\"Template IDs\".to_string());\n            }\n            if filters.status_filter.is_some() {\n                applied.push(\"Status Filter\".to_string());\n            }\n        }\n\n        applied\n    }\n\n    /// Download a generated report\n    pub async fn download_report(&self, report_id: &str) -> Result<(PathBuf, String), ReportError> {\n        // Try different file extensions\n        let extensions = [\"xlsx\", \"csv\", \"json\", \"pdf\"];\n\n        for ext in &extensions {\n            let file_path = self.reports_dir.join(format!(\"{}.{}\", report_id, ext));\n            if file_path.exists() {\n                let mime_type = match *ext {\n                    \"xlsx\" => \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                    \"csv\" => \"text/csv\",\n                    \"json\" => \"application/json\",\n                    \"pdf\" => \"application/pdf\",\n                    _ => \"application/octet-stream\",\n                };\n                return Ok((file_path, mime_type.to_string()));\n            }\n        }\n\n        Err(ReportError::ReportNotFound(format!(\"Report {} not found\", report_id)))\n    }\n\n    /// List generated reports\n    pub async fn list_reports(\n        &self,\n        request: ReportListRequest,\n    ) -> Result<ReportListResponse, ReportError> {\n        // This is a simplified implementation\n        // In a real system, you'd store report metadata in the database\n\n        let page = request.page.unwrap_or(1).max(1);\n        let per_page = request.per_page.unwrap_or(20).min(100).max(1);\n\n        // For now, return empty list as we don't have persistent report storage\n        Ok(ReportListResponse {\n            reports: vec![],\n            total: 0,\n            page,\n            per_page,\n            total_pages: 0,\n        })\n    }\n\n    /// Delete a report\n    pub async fn delete_report(&self, report_id: &str) -> Result<(), ReportError> {\n        let extensions = [\"xlsx\", \"csv\", \"json\", \"pdf\"];\n        let mut deleted = false;\n\n        for ext in &extensions {\n            let file_path = self.reports_dir.join(format!(\"{}.{}\", report_id, ext));\n            if file_path.exists() {\n                fs::remove_file(&file_path).await\n                    .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to delete report file: {}\", e)))?;\n                deleted = true;\n            }\n        }\n\n        if !deleted {\n            return Err(ReportError::ReportNotFound(format!(\"Report {} not found\", report_id)));\n        }\n\n        Ok(())\n    }\n}\n", "modifiedCode": "use anyhow::Result;\nuse sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, QueryOrder, PaginatorTrait};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse std::collections::HashMap;\nuse std::path::PathBuf;\nuse tokio::fs;\nuse xlsxwriter::{Workbook, Format};\n\nuse crate::{\n    dto::reports::*,\n    handlers::ApiError,\n};\nuse database::entities::{projects, users, organizations, templates, audit_logs, financial_calculations};\n\n/// Report service for generating various types of reports\npub struct ReportService {\n    db: DatabaseConnection,\n    temp_dir: PathBuf,\n    reports_dir: PathBuf,\n}\n\nimpl ReportService {\n    pub fn new(db: DatabaseConnection) -> Result<Self, ReportError> {\n        let temp_dir = std::env::temp_dir().join(\"excelsync_reports\");\n        let reports_dir = PathBuf::from(\"./reports\");\n        \n        // Create directories if they don't exist\n        std::fs::create_dir_all(&temp_dir)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create temp directory: {}\", e)))?;\n        std::fs::create_dir_all(&reports_dir)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create reports directory: {}\", e)))?;\n\n        Ok(Self {\n            db,\n            temp_dir,\n            reports_dir,\n        })\n    }\n\n    /// Generate a report based on the request\n    pub async fn generate_report(\n        &self,\n        request: ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportGenerationResponse, ReportError> {\n        let report_id = Uuid::new_v4().to_string();\n        let start_time = std::time::Instant::now();\n\n        // Collect data based on report type\n        let report_data = self.collect_report_data(&request, user_id).await?;\n        \n        // Generate file based on format\n        let file_path = self.generate_report_file(&report_id, &request.format, &report_data).await?;\n        \n        // Calculate file size\n        let file_size = fs::metadata(&file_path).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to get file size: {}\", e)))?\n            .len();\n\n        let processing_time = start_time.elapsed().as_millis() as u64;\n        let expires_at = Utc::now() + chrono::Duration::hours(24); // Reports expire after 24 hours\n\n        Ok(ReportGenerationResponse {\n            report_id: report_id.clone(),\n            report_type: request.report_type.clone(),\n            format: request.format.clone(),\n            status: ReportStatus::Completed,\n            generated_at: Utc::now().to_rfc3339(),\n            generated_by: user_id.to_string(),\n            file_size: Some(file_size),\n            download_url: Some(format!(\"/api/v1/reports/{}/download\", report_id)),\n            expires_at: Some(expires_at.to_rfc3339()),\n            metadata: ReportMetadata {\n                total_records: report_data.rows.len() as u64,\n                processing_time_ms: processing_time,\n                data_sources: self.get_data_sources(&request.report_type),\n                filters_applied: self.get_applied_filters(&request.filters),\n                columns: report_data.headers.iter().map(|h| ReportColumn {\n                    name: h.clone(),\n                    data_type: \"string\".to_string(),\n                    description: None,\n                }).collect(),\n            },\n        })\n    }\n\n    /// Collect data for the report based on type\n    async fn collect_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        match &request.report_type {\n            ReportType::ProjectSummary => self.collect_project_summary_data(request, user_id).await,\n            ReportType::FinancialAnalysis => self.collect_financial_analysis_data(request, user_id).await,\n            ReportType::AuditReport => self.collect_audit_report_data(request, user_id).await,\n            ReportType::TemplateUsage => self.collect_template_usage_data(request, user_id).await,\n            ReportType::UserActivity => self.collect_user_activity_data(request, user_id).await,\n            ReportType::SystemPerformance => self.collect_system_performance_data(request, user_id).await,\n            ReportType::ComplianceReport => self.collect_compliance_report_data(request, user_id).await,\n            ReportType::CustomReport => self.collect_custom_report_data(request, user_id).await,\n        }\n    }\n\n    /// Collect project summary data\n    async fn collect_project_summary_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = projects::Entity::find();\n\n        // Apply filters if provided\n        if let Some(filters) = &request.filters {\n            if let Some(project_ids) = &filters.project_ids {\n                let uuids: Result<Vec<Uuid>, _> = project_ids.iter()\n                    .map(|id| Uuid::parse_str(id))\n                    .collect();\n                let uuids = uuids.map_err(|_| ReportError::InvalidConfiguration(\"Invalid project ID format\".to_string()))?;\n                query = query.filter(projects::Column::Id.is_in(uuids));\n            }\n        }\n\n        let projects = query.all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Project ID\".to_string(),\n            \"Name\".to_string(),\n            \"Description\".to_string(),\n            \"Status\".to_string(),\n            \"Owner ID\".to_string(),\n            \"Organization ID\".to_string(),\n            \"Created At\".to_string(),\n            \"Updated At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = projects.into_iter().map(|project| {\n            vec![\n                project.id.to_string(),\n                project.name,\n                project.description.unwrap_or_default(),\n                project.status.to_string(),\n                project.owner_id.to_string(),\n                project.organization_id.map_or_else(String::new, |id| id.to_string()),\n                project.created_at.to_rfc3339(),\n                project.updated_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0, // Will be set by caller\n                processing_time_ms: 0, // Will be set by caller\n                data_sources: vec![\"projects\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect financial analysis data\n    async fn collect_financial_analysis_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = financial_calculations::Entity::find();\n\n        // Apply filters if provided\n        if let Some(filters) = &request.filters {\n            if let Some(project_ids) = &filters.project_ids {\n                let uuids: Result<Vec<Uuid>, _> = project_ids.iter()\n                    .map(|id| Uuid::parse_str(id))\n                    .collect();\n                let uuids = uuids.map_err(|_| ReportError::InvalidConfiguration(\"Invalid project ID format\".to_string()))?;\n                query = query.filter(financial_calculations::Column::ProjectId.is_in(uuids));\n            }\n        }\n\n        let calculations = query.all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Calculation ID\".to_string(),\n            \"Project ID\".to_string(),\n            \"Calculation Type\".to_string(),\n            \"Calculated By\".to_string(),\n            \"Calculated At\".to_string(),\n            \"Results Summary\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = calculations.into_iter().map(|calc| {\n            vec![\n                calc.id.to_string(),\n                calc.project_id.to_string(),\n                calc.calculation_type,\n                calc.calculated_by.to_string(),\n                calc.calculated_at.to_rfc3339(),\n                // Extract summary from results JSON\n                calc.results.get(\"summary\")\n                    .and_then(|s| s.as_str())\n                    .unwrap_or(\"N/A\")\n                    .to_string(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"financial_calculations\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect audit report data\n    async fn collect_audit_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let mut query = audit_logs::Entity::find();\n\n        // Apply date range filter if provided\n        if let Some(filters) = &request.filters {\n            if let Some(date_range) = &filters.date_range {\n                if let (Ok(start), Ok(end)) = (\n                    DateTime::parse_from_rfc3339(&date_range.start_date),\n                    DateTime::parse_from_rfc3339(&date_range.end_date)\n                ) {\n                    query = query\n                        .filter(audit_logs::Column::CreatedAt.gte(start.with_timezone(&Utc)))\n                        .filter(audit_logs::Column::CreatedAt.lte(end.with_timezone(&Utc)));\n                }\n            }\n        }\n\n        let logs = query.order_by_desc(audit_logs::Column::CreatedAt)\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Log ID\".to_string(),\n            \"User ID\".to_string(),\n            \"Action\".to_string(),\n            \"Entity Type\".to_string(),\n            \"Entity ID\".to_string(),\n            \"IP Address\".to_string(),\n            \"User Agent\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = logs.into_iter().map(|log| {\n            vec![\n                log.id.to_string(),\n                log.user_id.to_string(),\n                log.action.to_string(),\n                log.entity_type,\n                log.entity_id.map(|id| id.to_string()).unwrap_or_default(),\n                log.ip_address,\n                log.user_agent,\n                log.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"audit_logs\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect template usage data\n    async fn collect_template_usage_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let templates = templates::Entity::find()\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"Template ID\".to_string(),\n            \"Name\".to_string(),\n            \"Type\".to_string(),\n            \"Version\".to_string(),\n            \"Is Active\".to_string(),\n            \"Is Default\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = templates.into_iter().map(|template| {\n            vec![\n                template.id.to_string(),\n                template.name,\n                template.template_type.to_string(),\n                template.version,\n                template.is_active.to_string(),\n                template.is_default.to_string(),\n                template.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"templates\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect user activity data\n    async fn collect_user_activity_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        let users = users::Entity::find()\n            .all(&self.db).await\n            .map_err(|e| ReportError::DatabaseError(e.to_string()))?;\n\n        let headers = vec![\n            \"User ID\".to_string(),\n            \"Email\".to_string(),\n            \"Full Name\".to_string(),\n            \"Role\".to_string(),\n            \"Is Active\".to_string(),\n            \"Last Login\".to_string(),\n            \"Created At\".to_string(),\n        ];\n\n        let rows: Vec<Vec<String>> = users.into_iter().map(|user| {\n            vec![\n                user.id.to_string(),\n                user.email,\n                user.full_name,\n                user.role.to_string(),\n                user.is_active.to_string(),\n                user.last_login.map(|dt| dt.to_rfc3339()).unwrap_or_default(),\n                user.created_at.to_rfc3339(),\n            ]\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"users\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect system performance data (placeholder)\n    async fn collect_system_performance_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // This would typically collect metrics from monitoring systems\n        let headers = vec![\n            \"Metric\".to_string(),\n            \"Value\".to_string(),\n            \"Unit\".to_string(),\n            \"Timestamp\".to_string(),\n        ];\n\n        let rows = vec![\n            vec![\"CPU Usage\".to_string(), \"45.2\".to_string(), \"%\".to_string(), Utc::now().to_rfc3339()],\n            vec![\"Memory Usage\".to_string(), \"2.1\".to_string(), \"GB\".to_string(), Utc::now().to_rfc3339()],\n            vec![\"Database Connections\".to_string(), \"15\".to_string(), \"count\".to_string(), Utc::now().to_rfc3339()],\n        ];\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"system_metrics\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect compliance report data\n    async fn collect_compliance_report_data(\n        &self,\n        request: &ReportGenerationRequest,\n        user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // For compliance, we'll combine audit logs with user activity\n        let audit_data = self.collect_audit_report_data(request, user_id).await?;\n\n        // Add compliance-specific headers\n        let mut headers = audit_data.headers;\n        headers.push(\"Compliance Status\".to_string());\n        headers.push(\"Risk Level\".to_string());\n\n        let rows: Vec<Vec<String>> = audit_data.rows.into_iter().map(|mut row| {\n            // Add compliance analysis (simplified)\n            row.push(\"Compliant\".to_string());\n            row.push(\"Low\".to_string());\n            row\n        }).collect();\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"audit_logs\".to_string(), \"compliance_rules\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Collect custom report data\n    async fn collect_custom_report_data(\n        &self,\n        _request: &ReportGenerationRequest,\n        _user_id: Uuid,\n    ) -> Result<ReportData, ReportError> {\n        // Placeholder for custom reports\n        let headers = vec![\"Custom Field 1\".to_string(), \"Custom Field 2\".to_string()];\n        let rows = vec![vec![\"Value 1\".to_string(), \"Value 2\".to_string()]];\n\n        Ok(ReportData {\n            headers,\n            rows,\n            metadata: ReportMetadata {\n                total_records: 0,\n                processing_time_ms: 0,\n                data_sources: vec![\"custom\".to_string()],\n                filters_applied: vec![],\n                columns: vec![],\n            },\n        })\n    }\n\n    /// Generate report file based on format\n    async fn generate_report_file(\n        &self,\n        report_id: &str,\n        format: &ReportFormat,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        match format {\n            ReportFormat::Excel => self.generate_excel_file(report_id, data).await,\n            ReportFormat::Csv => self.generate_csv_file(report_id, data).await,\n            ReportFormat::Json => self.generate_json_file(report_id, data).await,\n            ReportFormat::Pdf => self.generate_pdf_file(report_id, data).await,\n        }\n    }\n\n    /// Generate Excel file\n    async fn generate_excel_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.xlsx\", report_id));\n\n        let workbook = Workbook::new(&file_path.to_string_lossy())\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to create Excel workbook: {}\", e)))?;\n\n        let mut worksheet = workbook.add_worksheet(Some(\"Report\"))\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to add worksheet: {}\", e)))?;\n\n        // Create header format\n        let mut header_format = Format::new();\n        header_format.set_bold();\n\n        // Write headers\n        for (col, header) in data.headers.iter().enumerate() {\n            worksheet.write_string(0, col as u16, header, Some(&header_format))\n                .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write header: {}\", e)))?;\n        }\n\n        // Write data rows\n        for (row_idx, row) in data.rows.iter().enumerate() {\n            for (col_idx, cell) in row.iter().enumerate() {\n                worksheet.write_string((row_idx + 1) as u32, col_idx as u16, cell, None)\n                    .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write cell: {}\", e)))?;\n            }\n        }\n\n        // Auto-fit columns\n        for col in 0..data.headers.len() {\n            worksheet.set_column(col as u16, col as u16, 15.0, None)\n                .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to set column width: {}\", e)))?;\n        }\n\n        workbook.close()\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to close workbook: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate CSV file\n    async fn generate_csv_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.csv\", report_id));\n\n        let mut csv_content = String::new();\n\n        // Write headers\n        csv_content.push_str(&data.headers.join(\",\"));\n        csv_content.push('\\n');\n\n        // Write data rows\n        for row in &data.rows {\n            // Escape commas and quotes in CSV data\n            let escaped_row: Vec<String> = row.iter().map(|cell| {\n                if cell.contains(',') || cell.contains('\"') || cell.contains('\\n') {\n                    format!(\"\\\"{}\\\"\", cell.replace('\"', \"\\\"\\\"\"))\n                } else {\n                    cell.clone()\n                }\n            }).collect();\n            csv_content.push_str(&escaped_row.join(\",\"));\n            csv_content.push('\\n');\n        }\n\n        fs::write(&file_path, csv_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write CSV file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate JSON file\n    async fn generate_json_file(\n        &self,\n        report_id: &str,\n        data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.json\", report_id));\n\n        // Convert data to JSON structure\n        let json_data: Vec<HashMap<String, String>> = data.rows.iter().map(|row| {\n            data.headers.iter().zip(row.iter())\n                .map(|(header, value)| (header.clone(), value.clone()))\n                .collect()\n        }).collect();\n\n        let json_content = serde_json::to_string_pretty(&json_data)\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to serialize JSON: {}\", e)))?;\n\n        fs::write(&file_path, json_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write JSON file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Generate PDF file (placeholder implementation)\n    async fn generate_pdf_file(\n        &self,\n        report_id: &str,\n        _data: &ReportData,\n    ) -> Result<PathBuf, ReportError> {\n        let file_path = self.reports_dir.join(format!(\"{}.pdf\", report_id));\n\n        // For now, create a simple text file as PDF generation requires additional dependencies\n        let pdf_content = \"PDF generation not yet implemented. This is a placeholder.\";\n\n        fs::write(&file_path, pdf_content).await\n            .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to write PDF file: {}\", e)))?;\n\n        Ok(file_path)\n    }\n\n    /// Get data sources for a report type\n    fn get_data_sources(&self, report_type: &ReportType) -> Vec<String> {\n        match report_type {\n            ReportType::ProjectSummary => vec![\"projects\".to_string()],\n            ReportType::FinancialAnalysis => vec![\"financial_calculations\".to_string()],\n            ReportType::AuditReport => vec![\"audit_logs\".to_string()],\n            ReportType::TemplateUsage => vec![\"templates\".to_string()],\n            ReportType::UserActivity => vec![\"users\".to_string()],\n            ReportType::SystemPerformance => vec![\"system_metrics\".to_string()],\n            ReportType::ComplianceReport => vec![\"audit_logs\".to_string(), \"compliance_rules\".to_string()],\n            ReportType::CustomReport => vec![\"custom\".to_string()],\n        }\n    }\n\n    /// Get applied filters description\n    fn get_applied_filters(&self, filters: &Option<ReportFilters>) -> Vec<String> {\n        let mut applied = Vec::new();\n\n        if let Some(filters) = filters {\n            if filters.date_range.is_some() {\n                applied.push(\"Date Range\".to_string());\n            }\n            if filters.project_ids.is_some() {\n                applied.push(\"Project IDs\".to_string());\n            }\n            if filters.user_ids.is_some() {\n                applied.push(\"User IDs\".to_string());\n            }\n            if filters.organization_ids.is_some() {\n                applied.push(\"Organization IDs\".to_string());\n            }\n            if filters.template_ids.is_some() {\n                applied.push(\"Template IDs\".to_string());\n            }\n            if filters.status_filter.is_some() {\n                applied.push(\"Status Filter\".to_string());\n            }\n        }\n\n        applied\n    }\n\n    /// Download a generated report\n    pub async fn download_report(&self, report_id: &str) -> Result<(PathBuf, String), ReportError> {\n        // Try different file extensions\n        let extensions = [\"xlsx\", \"csv\", \"json\", \"pdf\"];\n\n        for ext in &extensions {\n            let file_path = self.reports_dir.join(format!(\"{}.{}\", report_id, ext));\n            if file_path.exists() {\n                let mime_type = match *ext {\n                    \"xlsx\" => \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                    \"csv\" => \"text/csv\",\n                    \"json\" => \"application/json\",\n                    \"pdf\" => \"application/pdf\",\n                    _ => \"application/octet-stream\",\n                };\n                return Ok((file_path, mime_type.to_string()));\n            }\n        }\n\n        Err(ReportError::ReportNotFound(format!(\"Report {} not found\", report_id)))\n    }\n\n    /// List generated reports\n    pub async fn list_reports(\n        &self,\n        request: ReportListRequest,\n    ) -> Result<ReportListResponse, ReportError> {\n        // This is a simplified implementation\n        // In a real system, you'd store report metadata in the database\n\n        let page = request.page.unwrap_or(1).max(1);\n        let per_page = request.per_page.unwrap_or(20).min(100).max(1);\n\n        // For now, return empty list as we don't have persistent report storage\n        Ok(ReportListResponse {\n            reports: vec![],\n            total: 0,\n            page,\n            per_page,\n            total_pages: 0,\n        })\n    }\n\n    /// Delete a report\n    pub async fn delete_report(&self, report_id: &str) -> Result<(), ReportError> {\n        let extensions = [\"xlsx\", \"csv\", \"json\", \"pdf\"];\n        let mut deleted = false;\n\n        for ext in &extensions {\n            let file_path = self.reports_dir.join(format!(\"{}.{}\", report_id, ext));\n            if file_path.exists() {\n                fs::remove_file(&file_path).await\n                    .map_err(|e| ReportError::FileGenerationError(format!(\"Failed to delete report file: {}\", e)))?;\n                deleted = true;\n            }\n        }\n\n        if !deleted {\n            return Err(ReportError::ReportNotFound(format!(\"Report {} not found\", report_id)));\n        }\n\n        Ok(())\n    }\n}\n"}