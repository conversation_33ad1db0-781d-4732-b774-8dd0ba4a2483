{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/common.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Generic pagination parameters\n#[derive(Debug, Deserialize, Validate)]\npub struct PaginationQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n}\n\nimpl Default for PaginationQuery {\n    fn default() -> Self {\n        Self {\n            page: Some(1),\n            per_page: Some(20),\n        }\n    }\n}\n\nimpl PaginationQuery {\n    pub fn page(&self) -> u64 {\n        self.page.unwrap_or(1)\n    }\n\n    pub fn per_page(&self) -> u64 {\n        self.per_page.unwrap_or(20).min(100).max(1)\n    }\n}\n\n/// Generic paginated response wrapper\n#[derive(Debug, Serialize, Deserialize)]\npub struct PaginatedResponse<T> {\n    pub data: Vec<T>,\n    pub pagination: PaginationInfo,\n}\n\n/// Pagination metadata\n#[derive(Debug, Serialize, Deserialize)]\npub struct PaginationInfo {\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n    pub has_next: bool,\n    pub has_prev: bool,\n}\n\nimpl PaginationInfo {\n    pub fn new(total: u64, page: u64, per_page: u64) -> Self {\n        let total_pages = (total + per_page - 1) / per_page;\n        Self {\n            total,\n            page,\n            per_page,\n            total_pages,\n            has_next: page < total_pages,\n            has_prev: page > 1,\n        }\n    }\n}\n\n/// Generic ID parameter\n#[derive(Debug, Deserialize, Validate)]\npub struct IdParam {\n    pub id: String,\n}\n\n/// Generic search query parameters\n#[derive(Debug, Deserialize, Validate)]\npub struct SearchQuery {\n    #[validate(length(min = 1, max = 255))]\n    pub q: Option<String>,\n    \n    pub sort_by: Option<String>,\n    pub sort_order: Option<SortOrder>,\n}\n\n/// Sort order enum\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum SortOrder {\n    Asc,\n    Desc,\n}\n\nimpl Default for SortOrder {\n    fn default() -> Self {\n        Self::Asc\n    }\n}\n\n/// Date range filter\n#[derive(Debug, Deserialize, Validate)]\npub struct DateRangeFilter {\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n}\n\n/// Generic status filter\n#[derive(Debug, Deserialize)]\npub struct StatusFilter {\n    pub status: Option<String>,\n    pub is_active: Option<bool>,\n}\n\n/// File upload metadata\n#[derive(Debug, Serialize, Deserialize)]\npub struct FileMetadata {\n    pub filename: String,\n    pub content_type: String,\n    pub size: u64,\n    pub checksum: String,\n    pub uploaded_at: String,\n}\n\n/// Bulk operation request\n#[derive(Debug, Deserialize, Validate)]\npub struct BulkOperationRequest {\n    #[validate(length(min = 1, max = 1000))]\n    pub ids: Vec<String>,\n    pub operation: BulkOperation,\n}\n\n/// Bulk operation types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum BulkOperation {\n    Delete,\n    Archive,\n    Activate,\n    Deactivate,\n    Export,\n}\n\n/// Bulk operation response\n#[derive(Debug, Serialize)]\npub struct BulkOperationResponse {\n    pub total_requested: u64,\n    pub successful: u64,\n    pub failed: u64,\n    pub errors: Vec<BulkOperationError>,\n}\n\n/// Bulk operation error\n#[derive(Debug, Serialize)]\npub struct BulkOperationError {\n    pub id: String,\n    pub error: String,\n}\n"}