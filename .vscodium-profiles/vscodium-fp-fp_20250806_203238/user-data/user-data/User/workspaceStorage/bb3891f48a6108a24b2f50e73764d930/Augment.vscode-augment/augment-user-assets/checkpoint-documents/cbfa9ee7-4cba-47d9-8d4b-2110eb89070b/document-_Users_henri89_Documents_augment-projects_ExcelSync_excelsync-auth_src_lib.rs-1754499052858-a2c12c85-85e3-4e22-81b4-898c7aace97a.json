{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "excelsync-auth/src/lib.rs"}, "modifiedCode": "pub mod jwt;\npub mod password;\npub mod session;\n\npub use jwt::*;\npub use password::*;\npub use session::*;\n\nuse anyhow::Result;\nuse chrono::{Duration, Utc};\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\n/// Main authentication service\n#[derive(Clone)]\npub struct AuthService {\n    jwt_service: JwtService,\n    password_service: PasswordService,\n}\n\nimpl AuthService {\n    /// Create new authentication service\n    pub fn new(jwt_secret: String) -> Self {\n        Self {\n            jwt_service: JwtService::new(jwt_secret),\n            password_service: PasswordService::new(),\n        }\n    }\n\n    /// Hash a password\n    pub fn hash_password(&self, password: &str) -> Result<String, AuthError> {\n        self.password_service.hash_password(password)\n    }\n\n    /// Verify a password against its hash\n    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {\n        self.password_service.verify_password(password, hash)\n    }\n\n    /// Generate JWT token\n    pub fn generate_token(\n        &self,\n        user_id: Uuid,\n        email: String,\n        role: String,\n        org_id: Option<Uuid>,\n    ) -> Result<String, AuthError> {\n        self.jwt_service.generate_token(user_id, email, role, org_id)\n    }\n\n    /// Validate JWT token\n    pub fn validate_token(&self, token: &str) -> Result<Claims, AuthError> {\n        self.jwt_service.validate_token(token)\n    }\n\n    /// Refresh JWT token\n    pub fn refresh_token(&self, token: &str) -> Result<String, AuthError> {\n        let claims = self.validate_token(token)?;\n        self.generate_token(\n            Uuid::parse_str(&claims.sub)?,\n            claims.email,\n            claims.role,\n            claims.org_id.map(|id| Uuid::parse_str(&id)).transpose()?,\n        )\n    }\n}\n\n/// JWT Claims structure\n#[derive(Debug, Serialize, Deserialize, Clone)]\npub struct Claims {\n    pub sub: String,        // User ID\n    pub email: String,      // User email\n    pub role: String,       // User role\n    pub org_id: Option<String>, // Organization ID\n    pub exp: i64,          // Expiration timestamp\n    pub iat: i64,          // Issued at timestamp\n    pub jti: String,       // JWT ID for revocation\n}\n\n/// Authentication errors\n#[derive(Debug, thiserror::Error)]\npub enum AuthError {\n    #[error(\"Password hashing failed\")]\n    HashingFailed,\n    #[error(\"Invalid password hash\")]\n    InvalidHash,\n    #[error(\"Token generation failed\")]\n    TokenGenerationFailed,\n    #[error(\"Invalid or expired token\")]\n    InvalidToken,\n    #[error(\"User not found\")]\n    UserNotFound,\n    #[error(\"Invalid credentials\")]\n    InvalidCredentials,\n    #[error(\"UUID parsing error: {0}\")]\n    UuidError(#[from] uuid::Error),\n    #[error(\"JWT error: {0}\")]\n    JwtError(String),\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_auth_service_creation() {\n        let auth_service = AuthService::new(\"test-secret-key-32-characters-long\".to_string());\n        assert!(true); // Service created successfully\n    }\n\n    #[test]\n    fn test_password_hashing() {\n        let auth_service = AuthService::new(\"test-secret-key-32-characters-long\".to_string());\n        let password = \"test-password-123\";\n        \n        let hash = auth_service.hash_password(password).unwrap();\n        assert!(auth_service.verify_password(password, &hash).unwrap());\n        assert!(!auth_service.verify_password(\"wrong-password\", &hash).unwrap());\n    }\n\n    #[test]\n    fn test_jwt_token_generation() {\n        let auth_service = AuthService::new(\"test-secret-key-32-characters-long\".to_string());\n        let user_id = Uuid::new_v4();\n        \n        let token = auth_service.generate_token(\n            user_id,\n            \"<EMAIL>\".to_string(),\n            \"admin\".to_string(),\n            None,\n        ).unwrap();\n        \n        let claims = auth_service.validate_token(&token).unwrap();\n        assert_eq!(claims.email, \"<EMAIL>\");\n        assert_eq!(claims.role, \"admin\");\n    }\n}\n"}