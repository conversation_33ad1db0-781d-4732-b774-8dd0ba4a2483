{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/data_versions.rs"}, "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"data_versions\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub project_id: Uuid,\n    pub entity_type: String,    // \"project_data\", \"template\", \"financial_calculation\", etc.\n    pub entity_id: Uuid,       // ID of the versioned entity\n    pub version_number: i32,    // Sequential version number\n    pub parent_version_id: Option<Uuid>, // Previous version for history chain\n    \n    pub data_snapshot: Json,    // Complete snapshot of the entity at this version\n    pub changes: Json,          // Diff/changes from previous version\n    pub change_summary: String, // Human-readable summary of changes\n    \n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n    pub is_current: bool,       // Whether this is the current active version\n    pub is_merged: bool,        // Whether this version was created from a merge\n    \n    // Conflict resolution\n    pub conflict_resolution_strategy: Option<String>, // \"auto\", \"manual\", \"latest_wins\", etc.\n    pub conflict_metadata: Option<Json>, // Additional conflict resolution data\n    \n    // Metadata\n    pub tags: Option<Json>,     // Version tags (e.g., [\"milestone\", \"release\"])\n    pub notes: Option<String>, // Version notes/comments\n}\n\n#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::projects::Entity\",\n        from = \"Column::ProjectId\",\n        to = \"super::projects::Column::Id\"\n    )]\n    Project,\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CreatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n    #[sea_orm(\n        belongs_to = \"Entity\",\n        from = \"Column::ParentVersionId\",\n        to = \"Column::Id\"\n    )]\n    ParentVersion,\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Project.def()\n    }\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            is_current: Set(false),\n            is_merged: Set(false),\n            version_number: Set(1),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n\n/// Version conflict information\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub struct VersionConflict {\n    pub field_path: String,\n    pub base_value: serde_json::Value,\n    pub current_value: serde_json::Value,\n    pub incoming_value: serde_json::Value,\n    pub conflict_type: ConflictType,\n}\n\n/// Types of version conflicts\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub enum ConflictType {\n    ModifyModify,   // Both versions modified the same field\n    ModifyDelete,   // One version modified, other deleted\n    DeleteModify,   // One version deleted, other modified\n    AddAdd,         // Both versions added the same field with different values\n}\n\n/// Version merge strategy\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub enum MergeStrategy {\n    LatestWins,     // Latest version takes precedence\n    ManualResolve,  // Require manual conflict resolution\n    FieldLevel,     // Merge at field level with specific rules\n    Custom(String), // Custom merge strategy\n}\n\n/// Version comparison result\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub struct VersionComparison {\n    pub from_version: Uuid,\n    pub to_version: Uuid,\n    pub changes: Vec<FieldChange>,\n    pub conflicts: Vec<VersionConflict>,\n    pub is_mergeable: bool,\n}\n\n/// Individual field change\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub struct FieldChange {\n    pub field_path: String,\n    pub change_type: ChangeType,\n    pub old_value: Option<serde_json::Value>,\n    pub new_value: Option<serde_json::Value>,\n}\n\n/// Types of field changes\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub enum ChangeType {\n    Added,\n    Modified,\n    Deleted,\n    Moved,\n}\n\n/// Version branch information\n#[derive(Clone, Debug, Serialize, Deserialize)]\npub struct VersionBranch {\n    pub branch_id: Uuid,\n    pub branch_name: String,\n    pub base_version_id: Uuid,\n    pub head_version_id: Uuid,\n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n    pub is_active: bool,\n}\n"}