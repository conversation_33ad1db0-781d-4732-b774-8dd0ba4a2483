{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000003_create_projects.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\nuse sea_orm_migration::prelude::extension::postgres::Type;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Create project_type enum\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(ProjectType::Table)\n                    .values([\n                        ProjectType::LandDevelopment,\n                        ProjectType::ResidentialBuilding,\n                        ProjectType::CommercialBuilding,\n                        ProjectType::MixedUse,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create project_status enum\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(ProjectStatus::Table)\n                    .values([\n                        ProjectStatus::Planning,\n                        ProjectStatus::InProgress,\n                        ProjectStatus::OnHold,\n                        ProjectStatus::Completed,\n                        ProjectStatus::Cancelled,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create projects table\n        manager\n            .create_table(\n                Table::create()\n                    .table(Projects::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(Projects::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(Projects::Name).string().not_null())\n                    .col(ColumnDef::new(Projects::Description).text())\n                    .col(\n                        ColumnDef::new(Projects::ProjectType)\n                            .enumeration(ProjectType::Table, [\n                                ProjectType::LandDevelopment,\n                                ProjectType::ResidentialBuilding,\n                                ProjectType::CommercialBuilding,\n                                ProjectType::MixedUse,\n                            ])\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(Projects::Status)\n                            .enumeration(ProjectStatus::Table, [\n                                ProjectStatus::Planning,\n                                ProjectStatus::InProgress,\n                                ProjectStatus::OnHold,\n                                ProjectStatus::Completed,\n                                ProjectStatus::Cancelled,\n                            ])\n                            .not_null()\n                            .default(\"planning\"),\n                    )\n                    .col(ColumnDef::new(Projects::OrganizationId).uuid().not_null())\n                    .col(ColumnDef::new(Projects::CreatedBy).uuid().not_null())\n                    .col(ColumnDef::new(Projects::StartDate).date())\n                    .col(ColumnDef::new(Projects::EndDate).date())\n                    .col(ColumnDef::new(Projects::Budget).decimal_len(15, 2))\n                    .col(ColumnDef::new(Projects::CompletionPercentage).integer())\n                    .col(ColumnDef::new(Projects::CreatedAt).timestamp().not_null())\n                    .col(ColumnDef::new(Projects::UpdatedAt).timestamp().not_null())\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_projects_organization\")\n                            .from(Projects::Table, Projects::OrganizationId)\n                            .to(Organizations::Table, Organizations::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_projects_created_by\")\n                            .from(Projects::Table, Projects::CreatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Projects::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(ProjectStatus::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(ProjectType::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Projects {\n    Table,\n    Id,\n    Name,\n    Description,\n    ProjectType,\n    Status,\n    OrganizationId,\n    CreatedBy,\n    StartDate,\n    EndDate,\n    Budget,\n    CompletionPercentage,\n    CreatedAt,\n    UpdatedAt,\n}\n\n#[derive(Iden)]\nenum ProjectType {\n    Table,\n    LandDevelopment,\n    ResidentialBuilding,\n    CommercialBuilding,\n    MixedUse,\n}\n\n#[derive(Iden)]\nenum ProjectStatus {\n    Table,\n    Planning,\n    InProgress,\n    OnHold,\n    Completed,\n    Cancelled,\n}\n\n#[derive(Iden)]\nenum Organizations {\n    Table,\n    Id,\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n}\n"}