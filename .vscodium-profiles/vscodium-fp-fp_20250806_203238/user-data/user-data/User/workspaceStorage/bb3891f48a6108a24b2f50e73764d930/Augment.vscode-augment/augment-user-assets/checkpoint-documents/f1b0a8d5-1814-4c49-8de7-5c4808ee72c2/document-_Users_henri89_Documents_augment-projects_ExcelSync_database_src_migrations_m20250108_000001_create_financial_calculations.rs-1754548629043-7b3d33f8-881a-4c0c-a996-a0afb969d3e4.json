{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250108_000001_create_financial_calculations.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_table(\n                Table::create()\n                    .table(FinancialCalculations::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(FinancialCalculations::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::ProjectId)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::CalculationType)\n                            .string()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::InputData)\n                            .json()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::Results)\n                            .json()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::CalculatedBy)\n                            .uuid()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::CalculatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::ValidUntil)\n                            .timestamp_with_time_zone()\n                            .null(),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(FinancialCalculations::UpdatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_financial_calculations_project_id\")\n                            .from(FinancialCalculations::Table, FinancialCalculations::ProjectId)\n                            .to(Projects::Table, Projects::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_financial_calculations_calculated_by\")\n                            .from(FinancialCalculations::Table, FinancialCalculations::CalculatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create indexes for better query performance\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_financial_calculations_project_id\")\n                    .table(FinancialCalculations::Table)\n                    .col(FinancialCalculations::ProjectId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_financial_calculations_calculated_by\")\n                    .table(FinancialCalculations::Table)\n                    .col(FinancialCalculations::CalculatedBy)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_financial_calculations_calculated_at\")\n                    .table(FinancialCalculations::Table)\n                    .col(FinancialCalculations::CalculatedAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .name(\"idx_financial_calculations_type_project\")\n                    .table(FinancialCalculations::Table)\n                    .col(FinancialCalculations::CalculationType)\n                    .col(FinancialCalculations::ProjectId)\n                    .to_owned(),\n            )\n            .await?;\n\n        Ok(())\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(FinancialCalculations::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(DeriveIden)]\nenum FinancialCalculations {\n    Table,\n    Id,\n    ProjectId,\n    CalculationType,\n    InputData,\n    Results,\n    CalculatedBy,\n    CalculatedAt,\n    ValidUntil,\n    CreatedAt,\n    UpdatedAt,\n}\n\n#[derive(DeriveIden)]\nenum Projects {\n    Table,\n    Id,\n}\n\n#[derive(DeriveIden)]\nenum Users {\n    Table,\n    Id,\n}\n"}