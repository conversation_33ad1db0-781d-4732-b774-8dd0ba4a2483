{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "excelsync-auth/Cargo.toml"}, "modifiedCode": "[package]\nname = \"excelsync-auth\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Authentication - JWT and password security\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\njsonwebtoken = { workspace = true }\nargon2 = { workspace = true }\nuuid = { workspace = true }\nchrono = { workspace = true }\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nrand = { workspace = true }\n\n# Additional dependencies for auth\nbase64 = \"0.21\"\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n"}