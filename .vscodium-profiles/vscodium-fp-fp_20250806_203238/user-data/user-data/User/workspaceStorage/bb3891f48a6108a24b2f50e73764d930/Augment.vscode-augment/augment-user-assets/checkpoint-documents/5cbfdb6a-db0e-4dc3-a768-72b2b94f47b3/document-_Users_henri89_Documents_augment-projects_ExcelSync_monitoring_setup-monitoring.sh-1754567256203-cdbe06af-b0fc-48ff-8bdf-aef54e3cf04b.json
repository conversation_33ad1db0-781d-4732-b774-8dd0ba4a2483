{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/setup-monitoring.sh"}, "modifiedCode": "#!/bin/bash\n\n# ExcelSync Monitoring Setup Script\n# This script sets up the complete monitoring stack for ExcelSync\n\nset -e\n\necho \"🚀 Setting up ExcelSync Monitoring Stack...\"\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Function to print colored output\nprint_status() {\n    echo -e \"${GREEN}[INFO]${NC} $1\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}[WARN]${NC} $1\"\n}\n\nprint_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\nprint_header() {\n    echo -e \"${BLUE}=== $1 ===${NC}\"\n}\n\n# Check if Docker is installed\ncheck_docker() {\n    if ! command -v docker &> /dev/null; then\n        print_error \"Docker is not installed. Please install Docker first.\"\n        exit 1\n    fi\n    \n    if ! command -v docker-compose &> /dev/null; then\n        print_error \"Docker Compose is not installed. Please install Docker Compose first.\"\n        exit 1\n    fi\n    \n    print_status \"Docker and Docker Compose are installed\"\n}\n\n# Create necessary directories\ncreate_directories() {\n    print_header \"Creating Monitoring Directories\"\n    \n    mkdir -p monitoring/data/prometheus\n    mkdir -p monitoring/data/grafana\n    mkdir -p monitoring/data/alertmanager\n    mkdir -p monitoring/data/loki\n    mkdir -p monitoring/logs\n    \n    print_status \"Created monitoring directories\"\n}\n\n# Set proper permissions\nset_permissions() {\n    print_header \"Setting Permissions\"\n    \n    # Grafana needs specific user permissions\n    sudo chown -R 472:472 monitoring/data/grafana 2>/dev/null || {\n        print_warning \"Could not set Grafana permissions. You may need to run with sudo.\"\n    }\n    \n    # Prometheus permissions\n    sudo chown -R 65534:65534 monitoring/data/prometheus 2>/dev/null || {\n        print_warning \"Could not set Prometheus permissions. You may need to run with sudo.\"\n    }\n    \n    print_status \"Set directory permissions\"\n}\n\n# Start monitoring stack\nstart_monitoring() {\n    print_header \"Starting Monitoring Stack\"\n    \n    cd monitoring\n    \n    # Pull latest images\n    print_status \"Pulling Docker images...\"\n    docker-compose pull\n    \n    # Start services\n    print_status \"Starting monitoring services...\"\n    docker-compose up -d\n    \n    # Wait for services to be ready\n    print_status \"Waiting for services to start...\"\n    sleep 30\n    \n    cd ..\n}\n\n# Check service health\ncheck_services() {\n    print_header \"Checking Service Health\"\n    \n    services=(\n        \"prometheus:9090\"\n        \"grafana:3001\"\n        \"alertmanager:9093\"\n        \"node-exporter:9100\"\n    )\n    \n    for service in \"${services[@]}\"; do\n        name=$(echo $service | cut -d: -f1)\n        port=$(echo $service | cut -d: -f2)\n        \n        if curl -s \"http://localhost:$port\" > /dev/null; then\n            print_status \"$name is running on port $port\"\n        else\n            print_warning \"$name may not be ready yet on port $port\"\n        fi\n    done\n}\n\n# Display access information\nshow_access_info() {\n    print_header \"Access Information\"\n    \n    echo \"\"\n    echo \"📊 Monitoring Services:\"\n    echo \"  • Prometheus:    http://localhost:9090\"\n    echo \"  • Grafana:       http://localhost:3001 (admin/admin123)\"\n    echo \"  • Alertmanager:  http://localhost:9093\"\n    echo \"  • Node Exporter: http://localhost:9100\"\n    echo \"\"\n    echo \"🔍 ExcelSync API Endpoints:\"\n    echo \"  • Health Check:  http://localhost:3000/health\"\n    echo \"  • Readiness:     http://localhost:3000/ready\"\n    echo \"  • Metrics:       http://localhost:3000/metrics\"\n    echo \"\"\n    echo \"📈 Default Dashboards:\"\n    echo \"  • ExcelSync API Dashboard will be available in Grafana\"\n    echo \"  • Import additional dashboards from monitoring/grafana/dashboards/\"\n    echo \"\"\n}\n\n# Create systemd service (optional)\ncreate_systemd_service() {\n    if [[ \"$1\" == \"--systemd\" ]]; then\n        print_header \"Creating Systemd Service\"\n        \n        cat > /tmp/excelsync-monitoring.service << EOF\n[Unit]\nDescription=ExcelSync Monitoring Stack\nRequires=docker.service\nAfter=docker.service\n\n[Service]\nType=oneshot\nRemainAfterExit=yes\nWorkingDirectory=$(pwd)/monitoring\nExecStart=/usr/bin/docker-compose up -d\nExecStop=/usr/bin/docker-compose down\nTimeoutStartSec=0\n\n[Install]\nWantedBy=multi-user.target\nEOF\n        \n        sudo mv /tmp/excelsync-monitoring.service /etc/systemd/system/\n        sudo systemctl daemon-reload\n        sudo systemctl enable excelsync-monitoring.service\n        \n        print_status \"Created systemd service. Use 'sudo systemctl start excelsync-monitoring' to start\"\n    fi\n}\n\n# Main execution\nmain() {\n    print_header \"ExcelSync Monitoring Setup\"\n    \n    check_docker\n    create_directories\n    set_permissions\n    start_monitoring\n    sleep 10\n    check_services\n    show_access_info\n    create_systemd_service \"$1\"\n    \n    print_status \"Monitoring setup complete! 🎉\"\n    print_warning \"Remember to configure your database and Redis connection strings in the monitoring configuration.\"\n}\n\n# Help function\nshow_help() {\n    echo \"ExcelSync Monitoring Setup Script\"\n    echo \"\"\n    echo \"Usage: $0 [OPTIONS]\"\n    echo \"\"\n    echo \"Options:\"\n    echo \"  --systemd    Create systemd service for auto-start\"\n    echo \"  --help       Show this help message\"\n    echo \"\"\n    echo \"This script will:\"\n    echo \"  1. Check Docker installation\"\n    echo \"  2. Create necessary directories\"\n    echo \"  3. Set proper permissions\"\n    echo \"  4. Start the monitoring stack\"\n    echo \"  5. Verify service health\"\n    echo \"\"\n}\n\n# Parse command line arguments\ncase \"$1\" in\n    --help)\n        show_help\n        exit 0\n        ;;\n    --systemd)\n        main --systemd\n        ;;\n    \"\")\n        main\n        ;;\n    *)\n        print_error \"Unknown option: $1\"\n        show_help\n        exit 1\n        ;;\nesac\n"}