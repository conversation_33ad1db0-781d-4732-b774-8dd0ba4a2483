{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/docs/mod.rs"}, "originalCode": "use utoipa::OpenApi;\nuse utoipa_swagger_ui::SwaggerUi;\nuse axum::Router;\n\nuse crate::{\n    dto::{auth::*, user::*, organization::*, project::*, template::*, common::*},\n    handlers::error::*,\n    AppState,\n};\n\n/// OpenAPI documentation configuration\n#[derive(OpenApi)]\n#[openapi(\n    info(\n        title = \"ExcelSync API\",\n        version = \"1.0.0\",\n        description = \"Real Estate Project Management API with Excel Integration\",\n        contact(\n            name = \"ExcelSync Team\",\n            email = \"<EMAIL>\",\n            url = \"https://excelsync.com\"\n        ),\n        license(\n            name = \"MIT\",\n            url = \"https://opensource.org/licenses/MIT\"\n        )\n    ),\n    servers(\n        (url = \"http://localhost:3000\", description = \"Development server\"),\n        (url = \"https://api.excelsync.com\", description = \"Production server\")\n    ),\n    paths(\n        // Note: Paths will be automatically discovered from route handlers with #[utoipa::path] annotations\n    ),\n    components(\n        schemas(\n            // Core DTOs\n            LoginRequest,\n            LoginResponse,\n            RegisterRequest,\n            RefreshTokenRequest,\n            TokenResponse,\n            UserResponse,\n            CreateUserRequest,\n            UpdateUserRequest,\n            OrganizationResponse,\n            CreateOrganizationRequest,\n            UpdateOrganizationRequest,\n            ProjectResponse,\n            CreateProjectRequest,\n            UpdateProjectRequest,\n            TemplateResponse,\n            CreateTemplateRequest,\n            UpdateTemplateRequest,\n\n            // Error DTOs\n            ErrorResponse,\n            ErrorInfo,\n        )\n    ),\n    tags(\n        (name = \"Authentication\", description = \"User authentication and authorization\"),\n        (name = \"Users\", description = \"User management operations\"),\n        (name = \"Organizations\", description = \"Organization management operations\"),\n        (name = \"Projects\", description = \"Project management operations\"),\n        (name = \"Templates\", description = \"Template management operations\"),\n        (name = \"Financial\", description = \"Financial calculations and analysis\"),\n        (name = \"Versioning\", description = \"Data versioning and conflict resolution\"),\n        (name = \"Audit\", description = \"Audit logging and compliance\"),\n        (name = \"Validation\", description = \"Data validation and quality checks\"),\n    )\n)]\npub struct ApiDoc;\n\n/// Create Swagger UI router\npub fn create_swagger_ui() -> SwaggerUi {\n    SwaggerUi::new(\"/swagger-ui\")\n        .url(\"/api-docs/openapi.json\", ApiDoc::openapi())\n}\n\n/// Create documentation router\npub fn create_docs_router() -> Router<AppState> {\n    Router::new()\n        .merge(create_swagger_ui())\n        .route(\"/api-docs/openapi.json\", axum::routing::get(|| async {\n            axum::Json(ApiDoc::openapi())\n        }))\n}\n"}