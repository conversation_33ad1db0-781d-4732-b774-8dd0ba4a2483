{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/versioning.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\n\n/// Data version information\n#[derive(Debug, Serialize, Deserialize)]\npub struct VersionInfo {\n    pub version_id: String,\n    pub version_number: u32,\n    pub parent_version_id: Option<String>,\n    pub created_by: String,\n    pub created_at: String,\n    pub comment: Option<String>,\n    pub is_current: bool,\n    pub tags: Vec<String>,\n}\n\n/// Create version request\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateVersionRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub data: Value,\n    \n    #[validate(length(max = 500))]\n    pub comment: Option<String>,\n    \n    pub tags: Option<Vec<String>>,\n    pub force_create: Option<bool>,\n}\n\n/// Versionable entity types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum VersionableEntityType {\n    Project,\n    Template,\n    ProjectData,\n    FinancialCalculation,\n    UserSettings,\n}\n\n/// Version comparison request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionComparisonRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub version_a: String,\n    pub version_b: String,\n    pub comparison_type: ComparisonType,\n}\n\n/// Comparison types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ComparisonType {\n    FullDiff,\n    FieldChanges,\n    Summary,\n    SideBySide,\n}\n\n/// Version comparison response\n#[derive(Debug, Serialize)]\npub struct VersionComparisonResponse {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub version_a: VersionInfo,\n    pub version_b: VersionInfo,\n    pub differences: Vec<FieldDifference>,\n    pub summary: ComparisonSummary,\n}\n\n/// Field difference\n#[derive(Debug, Serialize)]\npub struct FieldDifference {\n    pub field_path: String,\n    pub field_name: String,\n    pub change_type: ChangeType,\n    pub old_value: Option<Value>,\n    pub new_value: Option<Value>,\n    pub data_type: String,\n}\n\n/// Change types\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ChangeType {\n    Added,\n    Modified,\n    Deleted,\n    Moved,\n    TypeChanged,\n}\n\n/// Comparison summary\n#[derive(Debug, Serialize)]\npub struct ComparisonSummary {\n    pub total_changes: u32,\n    pub additions: u32,\n    pub modifications: u32,\n    pub deletions: u32,\n    pub major_changes: Vec<String>,\n    pub compatibility_status: CompatibilityStatus,\n}\n\n/// Compatibility status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum CompatibilityStatus {\n    FullyCompatible,\n    MinorChanges,\n    MajorChanges,\n    BreakingChanges,\n}\n\n/// Version merge request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionMergeRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub source_version: String,\n    pub target_version: String,\n    pub merge_strategy: MergeStrategy,\n    pub conflict_resolutions: Option<Vec<ConflictResolution>>,\n    \n    #[validate(length(max = 500))]\n    pub merge_comment: Option<String>,\n}\n\n/// Merge strategies\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum MergeStrategy {\n    AutoMerge,\n    ManualReview,\n    SourceWins,\n    TargetWins,\n    FieldByField,\n}\n\n/// Conflict resolution\n#[derive(Debug, Deserialize, Serialize)]\npub struct ConflictResolution {\n    pub field_path: String,\n    pub resolution_type: ResolutionType,\n    pub resolved_value: Option<Value>,\n}\n\n/// Resolution types\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ResolutionType {\n    UseSource,\n    UseTarget,\n    UseCustom,\n    Skip,\n}\n\n/// Version merge response\n#[derive(Debug, Serialize)]\npub struct VersionMergeResponse {\n    pub merge_id: String,\n    pub result_version: VersionInfo,\n    pub conflicts_detected: Vec<MergeConflict>,\n    pub merge_status: MergeStatus,\n    pub merge_summary: MergeSummary,\n}\n\n/// Merge conflict\n#[derive(Debug, Serialize)]\npub struct MergeConflict {\n    pub field_path: String,\n    pub conflict_type: ConflictType,\n    pub source_value: Value,\n    pub target_value: Value,\n    pub suggested_resolution: Option<Value>,\n}\n\n/// Conflict types\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ConflictType {\n    ValueConflict,\n    TypeConflict,\n    StructuralConflict,\n    DependencyConflict,\n}\n\n/// Merge status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum MergeStatus {\n    Success,\n    ConflictsDetected,\n    Failed,\n    RequiresManualReview,\n}\n\n/// Merge summary\n#[derive(Debug, Serialize)]\npub struct MergeSummary {\n    pub fields_merged: u32,\n    pub conflicts_resolved: u32,\n    pub conflicts_remaining: u32,\n    pub merge_duration_ms: u64,\n}\n\n/// Version history request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionHistoryRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    \n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n    \n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n    \n    pub include_data: Option<bool>,\n    pub tag_filter: Option<String>,\n    pub author_filter: Option<String>,\n    pub date_from: Option<String>,\n    pub date_to: Option<String>,\n}\n\n/// Version history response\n#[derive(Debug, Serialize)]\npub struct VersionHistoryResponse {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub versions: Vec<VersionHistoryItem>,\n    pub total_versions: u64,\n    pub current_version: String,\n}\n\n/// Version history item\n#[derive(Debug, Serialize)]\npub struct VersionHistoryItem {\n    pub version_info: VersionInfo,\n    pub data: Option<Value>,\n    pub change_summary: Option<String>,\n    pub file_size: Option<u64>,\n}\n\n/// Version rollback request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionRollbackRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub target_version: String,\n    \n    #[validate(length(max = 500))]\n    pub rollback_reason: String,\n    \n    pub create_backup: Option<bool>,\n}\n\n/// Version rollback response\n#[derive(Debug, Serialize)]\npub struct VersionRollbackResponse {\n    pub rollback_id: String,\n    pub new_current_version: VersionInfo,\n    pub backup_version: Option<VersionInfo>,\n    pub rollback_summary: RollbackSummary,\n}\n\n/// Rollback summary\n#[derive(Debug, Serialize)]\npub struct RollbackSummary {\n    pub fields_reverted: u32,\n    pub data_loss_warnings: Vec<String>,\n    pub rollback_timestamp: String,\n}\n\n/// Version branch request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionBranchRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub source_version: String,\n    \n    #[validate(length(min = 1, max = 100))]\n    pub branch_name: String,\n    \n    #[validate(length(max = 500))]\n    pub branch_description: Option<String>,\n}\n\n/// Version branch response\n#[derive(Debug, Serialize)]\npub struct VersionBranchResponse {\n    pub branch_id: String,\n    pub branch_name: String,\n    pub branch_version: VersionInfo,\n    pub source_version: VersionInfo,\n}\n\n/// Version tag request\n#[derive(Debug, Deserialize, Validate)]\npub struct VersionTagRequest {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub version_id: String,\n    \n    #[validate(length(min = 1, max = 50))]\n    pub tag_name: String,\n    \n    #[validate(length(max = 200))]\n    pub tag_description: Option<String>,\n}\n\n/// Version statistics\n#[derive(Debug, Serialize)]\npub struct VersionStatistics {\n    pub entity_type: VersionableEntityType,\n    pub entity_id: String,\n    pub total_versions: u64,\n    pub total_size_bytes: u64,\n    pub oldest_version_date: String,\n    pub newest_version_date: String,\n    pub most_active_author: String,\n    pub version_frequency: VersionFrequency,\n}\n\n/// Version frequency statistics\n#[derive(Debug, Serialize)]\npub struct VersionFrequency {\n    pub daily_average: f64,\n    pub weekly_average: f64,\n    pub monthly_average: f64,\n    pub peak_activity_period: String,\n}\n"}