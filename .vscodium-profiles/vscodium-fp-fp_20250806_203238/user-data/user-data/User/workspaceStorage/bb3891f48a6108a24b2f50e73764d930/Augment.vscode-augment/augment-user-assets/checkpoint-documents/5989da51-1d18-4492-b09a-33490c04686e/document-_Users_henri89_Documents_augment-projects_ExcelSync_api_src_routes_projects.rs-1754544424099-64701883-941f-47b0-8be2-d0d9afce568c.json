{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/projects.rs"}, "originalCode": "use axum::{\n    extract::{Path, Query, Request, State},\n    response::Json,\n    routing::{delete, get, post, put},\n    Router,\n};\nuse database::entities::{ProjectType, ProjectStatus};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::{CreateProjectRequest, ProjectService, UpdateProjectRequest},\n    AppState,\n};\n\n/// Query parameters for project listing\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectListQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n\n    pub organization_id: Option<Uuid>,\n    pub status: Option<ProjectStatus>,\n    pub project_type: Option<ProjectType>,\n}\n\n/// Project management routes\npub fn project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_projects).post(create_project))\n        .route(\"/:id\", get(get_project).put(update_project).delete(delete_project))\n        .route(\"/:id/data\", get(get_project_data).post(create_project_data).put(update_project_data))\n        .route(\"/:id/data/:data_id\", get(get_project_data_version).delete(delete_project_data_version))\n        .route(\"/:id/clone\", post(clone_project))\n}\n\n/// Create project endpoint\npub async fn create_project(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.create_project(create_request, &claims).await?;\n\n    Ok(json_response(\"Project created successfully\", project))\n}\n\n/// Get project endpoint\npub async fn get_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project retrieved successfully\", project))\n}\n\n/// Update project endpoint\npub async fn update_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.update_project(project_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Project updated successfully\", project))\n}\n\n/// List projects endpoint\npub async fn list_projects(\n    State(state): State<AppState>,\n    Query(query): Query<ProjectListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Validate query parameters\n    query.validate().map_err(|e| ApiError::BadRequest(format!(\"Invalid query parameters: {}\", e)))?;\n\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let page = query.page.unwrap_or(1);\n    let per_page = query.per_page.unwrap_or(20);\n\n    let project_service = ProjectService::new(state.db.clone());\n    let projects = project_service\n        .list_projects(\n            page,\n            per_page,\n            query.organization_id,\n            query.status,\n            query.project_type,\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Projects retrieved successfully\", projects))\n}\n\n/// Delete project endpoint\npub async fn delete_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project deleted successfully\", ()))\n}\n", "modifiedCode": "use axum::{\n    extract::{Path, Query, Request, State},\n    response::Json,\n    routing::{delete, get, post, put},\n    Router,\n};\nuse database::entities::{ProjectType, ProjectStatus};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::{CreateProjectRequest, ProjectService, UpdateProjectRequest},\n    AppState,\n};\n\n/// Query parameters for project listing\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectListQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n\n    pub organization_id: Option<Uuid>,\n    pub status: Option<ProjectStatus>,\n    pub project_type: Option<ProjectType>,\n}\n\n/// Project management routes\npub fn project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_projects).post(create_project))\n        .route(\"/:id\", get(get_project).put(update_project).delete(delete_project))\n        .route(\"/:id/data\", get(get_project_data).post(create_project_data).put(update_project_data))\n        .route(\"/:id/data/:data_id\", get(get_project_data_version).delete(delete_project_data_version))\n        .route(\"/:id/clone\", post(clone_project))\n}\n\n/// Create project endpoint\npub async fn create_project(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.create_project(create_request, &claims).await?;\n\n    Ok(json_response(\"Project created successfully\", project))\n}\n\n/// Get project endpoint\npub async fn get_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project retrieved successfully\", project))\n}\n\n/// Update project endpoint\npub async fn update_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.update_project(project_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Project updated successfully\", project))\n}\n\n/// List projects endpoint\npub async fn list_projects(\n    State(state): State<AppState>,\n    Query(query): Query<ProjectListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Validate query parameters\n    query.validate().map_err(|e| ApiError::BadRequest(format!(\"Invalid query parameters: {}\", e)))?;\n\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let page = query.page.unwrap_or(1);\n    let per_page = query.per_page.unwrap_or(20);\n\n    let project_service = ProjectService::new(state.db.clone());\n    let projects = project_service\n        .list_projects(\n            page,\n            per_page,\n            query.organization_id,\n            query.status,\n            query.project_type,\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Projects retrieved successfully\", projects))\n}\n\n/// Get project data endpoint\npub async fn get_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data(project_id, &claims).await?;\n\n    Ok(json_response(\"Project data retrieved successfully\", project_data))\n}\n\n/// Create project data endpoint\npub async fn create_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.create_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data created successfully\", project_data))\n}\n\n/// Update project data endpoint\npub async fn update_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.update_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data updated successfully\", project_data))\n}\n\n/// Get project data version endpoint\npub async fn get_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version retrieved successfully\", project_data))\n}\n\n/// Delete project data version endpoint\npub async fn delete_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version deleted successfully\", ()))\n}\n\n/// Clone project endpoint\npub async fn clone_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually for clone options\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let new_name = clone_request.get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(\"Copy of Project\");\n\n    let project_service = ProjectService::new(state.db.clone());\n    let cloned_project = project_service.clone_project(project_id, new_name.to_string(), &claims).await?;\n\n    Ok(json_response(\"Project cloned successfully\", cloned_project))\n}\n\n/// Delete project endpoint\npub async fn delete_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project deleted successfully\", ()))\n}\n"}