{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/state.rs"}, "originalCode": "use excelsync_auth::AuthService;\nuse excelsync_core::Config;\nuse excelsync_database::DatabaseConnection;\nuse std::sync::Arc;\n\n/// Application state shared across all handlers\n#[derive(Clone)]\npub struct AppState {\n    pub auth_service: Arc<AuthService>,\n    pub db: Arc<DatabaseConnection>,\n    pub config: Arc<Config>,\n}\n\nimpl AppState {\n    /// Create new application state\n    pub fn new(\n        auth_service: Arc<AuthService>,\n        db: Arc<DatabaseConnection>,\n        config: Arc<Config>,\n    ) -> Self {\n        Self {\n            auth_service,\n            db,\n            config,\n        }\n    }\n}\n", "modifiedCode": "use auth::AuthService;\nuse core::Config;\nuse database::DatabaseConnection;\nuse std::sync::Arc;\n\n/// Application state shared across all handlers\n#[derive(Clone)]\npub struct AppState {\n    pub auth_service: Arc<AuthService>,\n    pub db: Arc<DatabaseConnection>,\n    pub config: Arc<Config>,\n}\n\nimpl AppState {\n    /// Create new application state\n    pub fn new(\n        auth_service: Arc<AuthService>,\n        db: Arc<DatabaseConnection>,\n        config: Arc<Config>,\n    ) -> Self {\n        Self {\n            auth_service,\n            db,\n            config,\n        }\n    }\n}\n"}