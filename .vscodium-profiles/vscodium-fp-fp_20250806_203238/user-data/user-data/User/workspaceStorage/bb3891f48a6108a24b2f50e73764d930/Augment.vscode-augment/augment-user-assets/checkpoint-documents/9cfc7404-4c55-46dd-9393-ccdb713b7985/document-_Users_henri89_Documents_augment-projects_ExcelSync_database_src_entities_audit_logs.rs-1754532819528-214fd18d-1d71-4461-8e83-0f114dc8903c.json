{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/audit_logs.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum AuditAction {\n    Create,\n    Update,\n    Delete,\n    Login,\n    Logout,\n    Export,\n    Import,\n}\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"audit_logs\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub user_id: Uuid,\n    pub action: AuditAction,\n    pub entity_type: String,\n    pub entity_id: Option<Uuid>,\n    \n    // Audit details\n    pub old_values: Option<Json>,\n    pub new_values: Option<Json>,\n    pub ip_address: String,\n    pub user_agent: String,\n    \n    // Metadata\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, En<PERSON>I<PERSON>, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::UserId\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, EnumIter, DeriveActiveEnum)]\n#[sea_orm(rs_type = \"String\", db_type = \"Enum\", enum_name = \"audit_action\")]\npub enum AuditAction {\n    #[sea_orm(string_value = \"create\")]\n    Create,\n    #[sea_orm(string_value = \"update\")]\n    Update,\n    #[sea_orm(string_value = \"delete\")]\n    Delete,\n    #[sea_orm(string_value = \"login\")]\n    Login,\n    #[sea_orm(string_value = \"logout\")]\n    Logout,\n    #[sea_orm(string_value = \"export\")]\n    Export,\n    #[sea_orm(string_value = \"import\")]\n    Import,\n}\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"audit_logs\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub user_id: Uuid,\n    pub action: AuditAction,\n    pub entity_type: String,\n    pub entity_id: Option<Uuid>,\n    \n    // Audit details\n    pub old_values: Option<Json>,\n    pub new_values: Option<Json>,\n    pub ip_address: String,\n    pub user_agent: String,\n    \n    // Metadata\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::UserId\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n"}