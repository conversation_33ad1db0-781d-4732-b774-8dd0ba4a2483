{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/mod.rs"}, "originalCode": "pub mod user_service;\npub mod organization_service;\npub mod project_service;\npub mod template_service;\npub mod validation_service;\npub mod vietnamese_tax;\npub mod financial_calculator;\npub mod financial_service;\npub mod versioning_service;\npub mod audit_service;\npub mod websocket_service;\npub mod file_service;\n\npub use user_service::*;\npub use organization_service::*;\npub use project_service::*;\npub use template_service::*;\npub use validation_service::*;\npub use financial_service::FinancialService;\npub use versioning_service::VersioningService;\npub use audit_service::AuditService;\npub use websocket_service::WebSocketService;\npub use file_service::FileService;\n", "modifiedCode": "pub mod user_service;\npub mod organization_service;\npub mod project_service;\npub mod template_service;\npub mod validation_service;\npub mod vietnamese_tax;\npub mod financial_calculator;\npub mod financial_service;\npub mod versioning_service;\npub mod audit_service;\npub mod websocket_service;\npub mod file_service;\npub mod report_service;\n\npub use user_service::*;\npub use organization_service::*;\npub use project_service::*;\npub use template_service::*;\npub use validation_service::*;\npub use financial_service::FinancialService;\npub use versioning_service::VersioningService;\npub use audit_service::AuditService;\npub use websocket_service::WebSocketService;\npub use file_service::FileService;\n"}