{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/financial_calculations.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"financial_calculations\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub project_id: Uuid,\n    pub calculation_type: String, // JSON serialized FinancialCalculationType\n    pub input_data: <PERSON><PERSON>,         // JSON serialized FinancialInputData\n    pub results: Json,            // JSON serialized FinancialResults\n    \n    pub calculated_by: Uuid,\n    pub calculated_at: DateTime<Utc>,\n    pub valid_until: Option<DateTime<Utc>>,\n    \n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::projects::Entity\",\n        from = \"Column::ProjectId\",\n        to = \"super::projects::Column::Id\"\n    )]\n    Project,\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CalculatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Project.def()\n    }\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            ..ActiveModelTrait::default()\n        }\n    }\n\n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"financial_calculations\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub project_id: Uuid,\n    pub calculation_type: String, // JSON serialized FinancialCalculationType\n    pub input_data: <PERSON><PERSON>,         // JSON serialized FinancialInputData\n    pub results: Json,            // JSON serialized FinancialResults\n    \n    pub calculated_by: Uuid,\n    pub calculated_at: DateTime<Utc>,\n    pub valid_until: Option<DateTime<Utc>>,\n    \n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumI<PERSON>, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::projects::Entity\",\n        from = \"Column::ProjectId\",\n        to = \"super::projects::Column::Id\"\n    )]\n    Project,\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CalculatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Project.def()\n    }\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n"}