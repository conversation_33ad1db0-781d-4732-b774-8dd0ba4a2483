{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/audit_service.rs"}, "originalCode": "use anyhow::Result;\nuse sea_orm::{\n    DatabaseConnection, EntityTrait, Set, ActiveModelTrait, QueryFilter, ColumnTrait,\n    QueryOrder, PaginatorTrait, TransactionTrait, DatabaseTransaction,\n};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse serde_json::{Value, json};\nuse thiserror::Error;\n\nuse crate::handlers::ApiError;\nuse database::entities::{audit_logs, users};\nuse crate::dto::audit::*;\n\n#[derive(Error, Debug)]\npub enum AuditError {\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Serialization error: {0}\")]\n    SerializationError(String),\n    #[error(\"Invalid audit data: {0}\")]\n    InvalidData(String),\n    #[error(\"Permission denied: {0}\")]\n    PermissionDenied(String),\n}\n\nimpl From<AuditError> for ApiError {\n    fn from(err: AuditError) -> Self {\n        match err {\n            AuditError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            AuditError::SerializationError(msg) => ApiError::BadRequest(msg),\n            AuditError::InvalidData(msg) => ApiError::BadRequest(msg),\n            AuditError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n        }\n    }\n}\n\npub struct AuditService {\n    db: DatabaseConnection,\n}\n\nimpl AuditService {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self { db }\n    }\n\n    /// Log an audit event\n    pub async fn log_event(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        entity_type: &str,\n        entity_id: Option<Uuid>,\n        old_values: Option<Value>,\n        new_values: Option<Value>,\n        ip_address: &str,\n        user_agent: &str,\n    ) -> Result<Uuid, AuditError> {\n        let audit_id = Uuid::new_v4();\n        \n        let audit_log = audit_logs::ActiveModel {\n            id: Set(audit_id),\n            user_id: Set(user_id),\n            action: Set(action),\n            entity_type: Set(entity_type.to_string()),\n            entity_id: Set(entity_id),\n            old_values: Set(old_values),\n            new_values: Set(new_values),\n            ip_address: Set(ip_address.to_string()),\n            user_agent: Set(user_agent.to_string()),\n            created_at: Set(Utc::now()),\n        };\n\n        audit_log.insert(&self.db).await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        Ok(audit_id)\n    }\n\n    /// Log a user authentication event\n    pub async fn log_auth_event(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        ip_address: &str,\n        user_agent: &str,\n        success: bool,\n    ) -> Result<Uuid, AuditError> {\n        let metadata = json!({\n            \"success\": success,\n            \"timestamp\": Utc::now().to_rfc3339()\n        });\n\n        self.log_event(\n            user_id,\n            action,\n            \"authentication\",\n            Some(user_id),\n            None,\n            Some(metadata),\n            ip_address,\n            user_agent,\n        ).await\n    }\n\n    /// Log a data modification event\n    pub async fn log_data_change(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        entity_type: &str,\n        entity_id: Uuid,\n        old_data: Option<&Value>,\n        new_data: Option<&Value>,\n        ip_address: &str,\n        user_agent: &str,\n    ) -> Result<Uuid, AuditError> {\n        self.log_event(\n            user_id,\n            action,\n            entity_type,\n            Some(entity_id),\n            old_data.cloned(),\n            new_data.cloned(),\n            ip_address,\n            user_agent,\n        ).await\n    }\n\n    /// Query audit logs with filters\n    pub async fn query_audit_logs(\n        &self,\n        request: &AuditQueryRequest,\n    ) -> Result<AuditQueryResponse, AuditError> {\n        let page = request.page.unwrap_or(1).max(1);\n        let per_page = request.per_page.unwrap_or(50).min(100).max(1);\n\n        let mut query = audit_logs::Entity::find();\n\n        // Apply filters\n        if let Some(user_id) = &request.user_id {\n            if let Ok(uuid) = Uuid::parse_str(user_id) {\n                query = query.filter(audit_logs::Column::UserId.eq(uuid));\n            }\n        }\n\n        if let Some(entity_type) = &request.entity_type {\n            query = query.filter(audit_logs::Column::EntityType.eq(entity_type));\n        }\n\n        if let Some(entity_id) = &request.entity_id {\n            if let Ok(uuid) = Uuid::parse_str(entity_id) {\n                query = query.filter(audit_logs::Column::EntityId.eq(uuid));\n            }\n        }\n\n        if let Some(action) = &request.action {\n            let db_action = match action {\n                AuditAction::Create => audit_logs::AuditAction::Create,\n                AuditAction::Update => audit_logs::AuditAction::Update,\n                AuditAction::Delete => audit_logs::AuditAction::Delete,\n                AuditAction::Login => audit_logs::AuditAction::Login,\n                AuditAction::Logout => audit_logs::AuditAction::Logout,\n                AuditAction::Export => audit_logs::AuditAction::Export,\n                AuditAction::Import => audit_logs::AuditAction::Import,\n                _ => return Err(AuditError::InvalidData(\"Unsupported action type\".to_string())),\n            };\n            query = query.filter(audit_logs::Column::Action.eq(db_action));\n        }\n\n        // Apply date filters\n        if let Some(start_date) = &request.start_date {\n            if let Ok(date) = DateTime::parse_from_rfc3339(start_date) {\n                query = query.filter(audit_logs::Column::CreatedAt.gte(date.with_timezone(&Utc)));\n            }\n        }\n\n        if let Some(end_date) = &request.end_date {\n            if let Ok(date) = DateTime::parse_from_rfc3339(end_date) {\n                query = query.filter(audit_logs::Column::CreatedAt.lte(date.with_timezone(&Utc)));\n            }\n        }\n\n        // Apply sorting\n        query = match request.sort_order.as_ref().unwrap_or(&SortOrder::Desc) {\n            SortOrder::Asc => query.order_by_asc(audit_logs::Column::CreatedAt),\n            SortOrder::Desc => query.order_by_desc(audit_logs::Column::CreatedAt),\n        };\n\n        let paginator = query.paginate(&self.db, per_page);\n        let total = paginator.num_items().await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        let logs = paginator.fetch_page(page - 1).await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        let entries = logs.into_iter().map(|log| self.convert_to_audit_entry(log)).collect();\n\n        Ok(AuditQueryResponse {\n            entries,\n            total,\n            page,\n            per_page,\n            total_pages: (total + per_page - 1) / per_page,\n            query_metadata: AuditQueryMetadata {\n                query_duration_ms: 0, // Would measure actual query time\n                filters_applied: vec![], // Would list applied filters\n                result_summary: AuditResultSummary {\n                    event_type_counts: vec![],\n                    action_counts: vec![],\n                    severity_counts: vec![],\n                    top_users: vec![],\n                    date_range: DateRange {\n                        start_date: \"\".to_string(),\n                        end_date: \"\".to_string(),\n                    },\n                },\n            },\n        })\n    }\n\n    /// Get audit trail for a specific entity\n    pub async fn get_audit_trail(\n        &self,\n        entity_type: &str,\n        entity_id: Uuid,\n        start_date: Option<DateTime<Utc>>,\n        end_date: Option<DateTime<Utc>>,\n    ) -> Result<Vec<audit_logs::Model>, AuditError> {\n        let mut query = audit_logs::Entity::find()\n            .filter(audit_logs::Column::EntityType.eq(entity_type))\n            .filter(audit_logs::Column::EntityId.eq(entity_id));\n\n        if let Some(start) = start_date {\n            query = query.filter(audit_logs::Column::CreatedAt.gte(start));\n        }\n\n        if let Some(end) = end_date {\n            query = query.filter(audit_logs::Column::CreatedAt.lte(end));\n        }\n\n        query.order_by_asc(audit_logs::Column::CreatedAt)\n            .all(&self.db)\n            .await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))\n    }\n\n    /// Convert database model to DTO\n    fn convert_to_audit_entry(&self, log: audit_logs::Model) -> AuditLogEntry {\n        AuditLogEntry {\n            id: log.id.to_string(),\n            event_type: AuditEventType::DataModification, // Default, can be enhanced\n            entity_type: log.entity_type,\n            entity_id: log.entity_id.map(|id| id.to_string()).unwrap_or_default(),\n            user_id: log.user_id.to_string(),\n            user_email: \"\".to_string(), // Would need to join with users table\n            organization_id: None,\n            action: self.convert_action(&log.action),\n            changes: self.build_changes(&log.old_values, &log.new_values),\n            metadata: None,\n            ip_address: Some(log.ip_address),\n            user_agent: Some(log.user_agent),\n            session_id: None,\n            timestamp: log.created_at.to_rfc3339(),\n            severity: AuditSeverity::Info, // Default, can be enhanced\n        }\n    }\n\n    fn convert_action(&self, action: &audit_logs::AuditAction) -> AuditAction {\n        match action {\n            audit_logs::AuditAction::Create => AuditAction::Create,\n            audit_logs::AuditAction::Update => AuditAction::Update,\n            audit_logs::AuditAction::Delete => AuditAction::Delete,\n            audit_logs::AuditAction::Login => AuditAction::Login,\n            audit_logs::AuditAction::Logout => AuditAction::Logout,\n            audit_logs::AuditAction::Export => AuditAction::Export,\n            audit_logs::AuditAction::Import => AuditAction::Import,\n        }\n    }\n\n    fn build_changes(&self, old_values: &Option<Value>, new_values: &Option<Value>) -> Option<AuditChanges> {\n        if old_values.is_none() && new_values.is_none() {\n            return None;\n        }\n\n        Some(AuditChanges {\n            fields_changed: vec![], // Would need to implement field-level diff\n            before_snapshot: old_values.clone(),\n            after_snapshot: new_values.clone(),\n            change_summary: \"Data modified\".to_string(),\n        })\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use uuid::Uuid;\n    use chrono::Utc;\n    use serde_json::json;\n\n    #[test]\n    fn test_audit_action_variants() {\n        use database::entities::audit_logs::AuditAction;\n\n        // Test that all variants can be created\n        let _create = AuditAction::Create;\n        let _update = AuditAction::Update;\n        let _delete = AuditAction::Delete;\n\n        // Test equality\n        assert_eq!(AuditAction::Create, AuditAction::Create);\n        assert_ne!(AuditAction::Create, AuditAction::Update);\n    }\n\n    #[test]\n    fn test_convert_action() {\n        let db = create_mock_db();\n        let audit_service = AuditService::new(db);\n\n        assert!(matches!(\n            audit_service.convert_action(&audit_logs::AuditAction::Create),\n            AuditAction::Create\n        ));\n\n        assert!(matches!(\n            audit_service.convert_action(&audit_logs::AuditAction::Update),\n            AuditAction::Update\n        ));\n\n        assert!(matches!(\n            audit_service.convert_action(&audit_logs::AuditAction::Delete),\n            AuditAction::Delete\n        ));\n    }\n\n    #[test]\n    fn test_build_changes() {\n        let db = create_mock_db();\n        let audit_service = AuditService::new(db);\n\n        let old_data = json!({\"name\": \"Old\", \"value\": 100});\n        let new_data = json!({\"name\": \"New\", \"value\": 200});\n\n        let changes = audit_service.build_changes(&Some(old_data.clone()), &Some(new_data.clone()));\n\n        assert!(changes.is_some());\n        let changes = changes.unwrap();\n        assert_eq!(changes.before_snapshot, Some(old_data));\n        assert_eq!(changes.after_snapshot, Some(new_data));\n        assert_eq!(changes.change_summary, \"Data modified\");\n    }\n\n    #[test]\n    fn test_build_changes_no_data() {\n        let db = create_mock_db();\n        let audit_service = AuditService::new(db);\n\n        let changes = audit_service.build_changes(&None, &None);\n        assert!(changes.is_none());\n    }\n}\n", "modifiedCode": "use anyhow::Result;\nuse sea_orm::{\n    DatabaseConnection, EntityTrait, Set, ActiveModelTrait, QueryFilter, ColumnTrait,\n    QueryOrder, PaginatorTrait, TransactionTrait, DatabaseTransaction,\n};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse serde_json::{Value, json};\nuse thiserror::Error;\n\nuse crate::handlers::ApiError;\nuse database::entities::{audit_logs, users};\nuse crate::dto::audit::*;\n\n#[derive(Error, Debug)]\npub enum AuditError {\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Serialization error: {0}\")]\n    SerializationError(String),\n    #[error(\"Invalid audit data: {0}\")]\n    InvalidData(String),\n    #[error(\"Permission denied: {0}\")]\n    PermissionDenied(String),\n}\n\nimpl From<AuditError> for ApiError {\n    fn from(err: AuditError) -> Self {\n        match err {\n            AuditError::DatabaseError(msg) => ApiError::DatabaseError(msg),\n            AuditError::SerializationError(msg) => ApiError::BadRequest(msg),\n            AuditError::InvalidData(msg) => ApiError::BadRequest(msg),\n            AuditError::PermissionDenied(msg) => ApiError::Forbidden(msg),\n        }\n    }\n}\n\npub struct AuditService {\n    db: DatabaseConnection,\n}\n\nimpl AuditService {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self { db }\n    }\n\n    /// Log an audit event\n    pub async fn log_event(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        entity_type: &str,\n        entity_id: Option<Uuid>,\n        old_values: Option<Value>,\n        new_values: Option<Value>,\n        ip_address: &str,\n        user_agent: &str,\n    ) -> Result<Uuid, AuditError> {\n        let audit_id = Uuid::new_v4();\n        \n        let audit_log = audit_logs::ActiveModel {\n            id: Set(audit_id),\n            user_id: Set(user_id),\n            action: Set(action),\n            entity_type: Set(entity_type.to_string()),\n            entity_id: Set(entity_id),\n            old_values: Set(old_values),\n            new_values: Set(new_values),\n            ip_address: Set(ip_address.to_string()),\n            user_agent: Set(user_agent.to_string()),\n            created_at: Set(Utc::now()),\n        };\n\n        audit_log.insert(&self.db).await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        Ok(audit_id)\n    }\n\n    /// Log a user authentication event\n    pub async fn log_auth_event(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        ip_address: &str,\n        user_agent: &str,\n        success: bool,\n    ) -> Result<Uuid, AuditError> {\n        let metadata = json!({\n            \"success\": success,\n            \"timestamp\": Utc::now().to_rfc3339()\n        });\n\n        self.log_event(\n            user_id,\n            action,\n            \"authentication\",\n            Some(user_id),\n            None,\n            Some(metadata),\n            ip_address,\n            user_agent,\n        ).await\n    }\n\n    /// Log a data modification event\n    pub async fn log_data_change(\n        &self,\n        user_id: Uuid,\n        action: audit_logs::AuditAction,\n        entity_type: &str,\n        entity_id: Uuid,\n        old_data: Option<&Value>,\n        new_data: Option<&Value>,\n        ip_address: &str,\n        user_agent: &str,\n    ) -> Result<Uuid, AuditError> {\n        self.log_event(\n            user_id,\n            action,\n            entity_type,\n            Some(entity_id),\n            old_data.cloned(),\n            new_data.cloned(),\n            ip_address,\n            user_agent,\n        ).await\n    }\n\n    /// Query audit logs with filters\n    pub async fn query_audit_logs(\n        &self,\n        request: &AuditQueryRequest,\n    ) -> Result<AuditQueryResponse, AuditError> {\n        let page = request.page.unwrap_or(1).max(1);\n        let per_page = request.per_page.unwrap_or(50).min(100).max(1);\n\n        let mut query = audit_logs::Entity::find();\n\n        // Apply filters\n        if let Some(user_id) = &request.user_id {\n            if let Ok(uuid) = Uuid::parse_str(user_id) {\n                query = query.filter(audit_logs::Column::UserId.eq(uuid));\n            }\n        }\n\n        if let Some(entity_type) = &request.entity_type {\n            query = query.filter(audit_logs::Column::EntityType.eq(entity_type));\n        }\n\n        if let Some(entity_id) = &request.entity_id {\n            if let Ok(uuid) = Uuid::parse_str(entity_id) {\n                query = query.filter(audit_logs::Column::EntityId.eq(uuid));\n            }\n        }\n\n        if let Some(action) = &request.action {\n            let db_action = match action {\n                AuditAction::Create => audit_logs::AuditAction::Create,\n                AuditAction::Update => audit_logs::AuditAction::Update,\n                AuditAction::Delete => audit_logs::AuditAction::Delete,\n                AuditAction::Login => audit_logs::AuditAction::Login,\n                AuditAction::Logout => audit_logs::AuditAction::Logout,\n                AuditAction::Export => audit_logs::AuditAction::Export,\n                AuditAction::Import => audit_logs::AuditAction::Import,\n                _ => return Err(AuditError::InvalidData(\"Unsupported action type\".to_string())),\n            };\n            query = query.filter(audit_logs::Column::Action.eq(db_action));\n        }\n\n        // Apply date filters\n        if let Some(start_date) = &request.start_date {\n            if let Ok(date) = DateTime::parse_from_rfc3339(start_date) {\n                query = query.filter(audit_logs::Column::CreatedAt.gte(date.with_timezone(&Utc)));\n            }\n        }\n\n        if let Some(end_date) = &request.end_date {\n            if let Ok(date) = DateTime::parse_from_rfc3339(end_date) {\n                query = query.filter(audit_logs::Column::CreatedAt.lte(date.with_timezone(&Utc)));\n            }\n        }\n\n        // Apply sorting\n        query = match request.sort_order.as_ref().unwrap_or(&SortOrder::Desc) {\n            SortOrder::Asc => query.order_by_asc(audit_logs::Column::CreatedAt),\n            SortOrder::Desc => query.order_by_desc(audit_logs::Column::CreatedAt),\n        };\n\n        let paginator = query.paginate(&self.db, per_page);\n        let total = paginator.num_items().await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        let logs = paginator.fetch_page(page - 1).await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))?;\n\n        let entries = logs.into_iter().map(|log| self.convert_to_audit_entry(log)).collect();\n\n        Ok(AuditQueryResponse {\n            entries,\n            total,\n            page,\n            per_page,\n            total_pages: (total + per_page - 1) / per_page,\n            query_metadata: AuditQueryMetadata {\n                query_duration_ms: 0, // Would measure actual query time\n                filters_applied: vec![], // Would list applied filters\n                result_summary: AuditResultSummary {\n                    event_type_counts: vec![],\n                    action_counts: vec![],\n                    severity_counts: vec![],\n                    top_users: vec![],\n                    date_range: DateRange {\n                        start_date: \"\".to_string(),\n                        end_date: \"\".to_string(),\n                    },\n                },\n            },\n        })\n    }\n\n    /// Get audit trail for a specific entity\n    pub async fn get_audit_trail(\n        &self,\n        entity_type: &str,\n        entity_id: Uuid,\n        start_date: Option<DateTime<Utc>>,\n        end_date: Option<DateTime<Utc>>,\n    ) -> Result<Vec<audit_logs::Model>, AuditError> {\n        let mut query = audit_logs::Entity::find()\n            .filter(audit_logs::Column::EntityType.eq(entity_type))\n            .filter(audit_logs::Column::EntityId.eq(entity_id));\n\n        if let Some(start) = start_date {\n            query = query.filter(audit_logs::Column::CreatedAt.gte(start));\n        }\n\n        if let Some(end) = end_date {\n            query = query.filter(audit_logs::Column::CreatedAt.lte(end));\n        }\n\n        query.order_by_asc(audit_logs::Column::CreatedAt)\n            .all(&self.db)\n            .await\n            .map_err(|e| AuditError::DatabaseError(e.to_string()))\n    }\n\n    /// Convert database model to DTO\n    fn convert_to_audit_entry(&self, log: audit_logs::Model) -> AuditLogEntry {\n        AuditLogEntry {\n            id: log.id.to_string(),\n            event_type: AuditEventType::DataModification, // Default, can be enhanced\n            entity_type: log.entity_type,\n            entity_id: log.entity_id.map(|id| id.to_string()).unwrap_or_default(),\n            user_id: log.user_id.to_string(),\n            user_email: \"\".to_string(), // Would need to join with users table\n            organization_id: None,\n            action: self.convert_action(&log.action),\n            changes: self.build_changes(&log.old_values, &log.new_values),\n            metadata: None,\n            ip_address: Some(log.ip_address),\n            user_agent: Some(log.user_agent),\n            session_id: None,\n            timestamp: log.created_at.to_rfc3339(),\n            severity: AuditSeverity::Info, // Default, can be enhanced\n        }\n    }\n\n    fn convert_action(&self, action: &audit_logs::AuditAction) -> AuditAction {\n        match action {\n            audit_logs::AuditAction::Create => AuditAction::Create,\n            audit_logs::AuditAction::Update => AuditAction::Update,\n            audit_logs::AuditAction::Delete => AuditAction::Delete,\n            audit_logs::AuditAction::Login => AuditAction::Login,\n            audit_logs::AuditAction::Logout => AuditAction::Logout,\n            audit_logs::AuditAction::Export => AuditAction::Export,\n            audit_logs::AuditAction::Import => AuditAction::Import,\n        }\n    }\n\n    fn build_changes(&self, old_values: &Option<Value>, new_values: &Option<Value>) -> Option<AuditChanges> {\n        if old_values.is_none() && new_values.is_none() {\n            return None;\n        }\n\n        Some(AuditChanges {\n            fields_changed: vec![], // Would need to implement field-level diff\n            before_snapshot: old_values.clone(),\n            after_snapshot: new_values.clone(),\n            change_summary: \"Data modified\".to_string(),\n        })\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use uuid::Uuid;\n    use chrono::Utc;\n    use serde_json::json;\n\n    #[test]\n    fn test_audit_action_variants() {\n        use database::entities::audit_logs::AuditAction;\n\n        // Test that all variants can be created\n        let _create = AuditAction::Create;\n        let _update = AuditAction::Update;\n        let _delete = AuditAction::Delete;\n\n        // Test equality\n        assert_eq!(AuditAction::Create, AuditAction::Create);\n        assert_ne!(AuditAction::Create, AuditAction::Update);\n    }\n\n\n}\n"}