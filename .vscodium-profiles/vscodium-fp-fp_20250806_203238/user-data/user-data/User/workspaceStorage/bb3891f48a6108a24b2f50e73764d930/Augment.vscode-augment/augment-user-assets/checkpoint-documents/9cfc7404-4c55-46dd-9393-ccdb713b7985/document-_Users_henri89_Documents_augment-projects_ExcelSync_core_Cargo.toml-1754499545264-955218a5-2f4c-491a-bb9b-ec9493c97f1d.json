{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/Cargo.toml"}, "originalCode": "[package]\nname = \"excelsync-core\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Core - Configuration and shared utilities\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nconfig = { workspace = true }\ndotenvy = { workspace = true }\nvalidator = { workspace = true }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n", "modifiedCode": "[package]\nname = \"core\"\nversion = \"0.1.0\"\nedition = \"2021\"\nauthors = [\"ExcelSync Team\"]\ndescription = \"ExcelSync Core - Configuration and shared utilities\"\nlicense = \"MIT\"\n\n[dependencies]\n# Workspace dependencies\nserde = { workspace = true }\nserde_json = { workspace = true }\nanyhow = { workspace = true }\nthiserror = { workspace = true }\nconfig = { workspace = true }\ndotenvy = { workspace = true }\nvalidator = { workspace = true }\n\n[dev-dependencies]\ntokio-test = { workspace = true }\n"}