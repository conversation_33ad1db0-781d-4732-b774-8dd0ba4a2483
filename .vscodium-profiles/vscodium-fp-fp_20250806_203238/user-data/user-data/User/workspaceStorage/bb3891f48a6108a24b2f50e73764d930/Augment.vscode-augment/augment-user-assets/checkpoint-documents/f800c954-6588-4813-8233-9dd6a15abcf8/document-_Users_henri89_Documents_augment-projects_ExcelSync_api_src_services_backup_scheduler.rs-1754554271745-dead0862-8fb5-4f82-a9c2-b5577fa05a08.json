{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/backup_scheduler.rs"}, "modifiedCode": "use anyhow::Result;\nuse chrono::{DateTime, Utc, Duration};\nuse serde::{Deserialize, Serialize};\nuse std::sync::Arc;\nuse tokio::sync::RwLock;\nuse tokio::time::{interval, sleep};\nuse tracing::{info, warn, error};\n\nuse database::{BackupService, BackupConfig, BackupMetadata, BackupStats};\n\n/// Backup scheduler configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupSchedulerConfig {\n    /// Enable automatic backups\n    pub enabled: bool,\n    /// Backup interval in hours\n    pub backup_interval_hours: u64,\n    /// Enable cleanup of old backups\n    pub auto_cleanup: bool,\n    /// Cleanup interval in hours\n    pub cleanup_interval_hours: u64,\n    /// Enable backup verification\n    pub verify_backups: bool,\n    /// Verification interval in hours\n    pub verification_interval_hours: u64,\n}\n\nimpl Default for BackupSchedulerConfig {\n    fn default() -> Self {\n        Self {\n            enabled: true,\n            backup_interval_hours: 24, // Daily backups\n            auto_cleanup: true,\n            cleanup_interval_hours: 168, // Weekly cleanup\n            verify_backups: true,\n            verification_interval_hours: 72, // Verify every 3 days\n        }\n    }\n}\n\n/// Backup job status\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupJobStatus {\n    pub job_type: BackupJobType,\n    pub status: JobStatus,\n    pub started_at: Option<DateTime<Utc>>,\n    pub completed_at: Option<DateTime<Utc>>,\n    pub error_message: Option<String>,\n    pub backup_id: Option<String>,\n}\n\n/// Types of backup jobs\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum BackupJobType {\n    FullBackup,\n    BaseBackup,\n    Cleanup,\n    Verification,\n}\n\n/// Job execution status\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum JobStatus {\n    Pending,\n    Running,\n    Completed,\n    Failed,\n}\n\n/// Backup scheduler service\npub struct BackupScheduler {\n    config: BackupSchedulerConfig,\n    backup_service: Arc<BackupService>,\n    current_jobs: Arc<RwLock<Vec<BackupJobStatus>>>,\n    is_running: Arc<RwLock<bool>>,\n}\n\nimpl BackupScheduler {\n    /// Create a new backup scheduler\n    pub fn new(config: BackupSchedulerConfig, backup_config: BackupConfig) -> Result<Self> {\n        let backup_service = Arc::new(BackupService::new(backup_config)?);\n        \n        Ok(Self {\n            config,\n            backup_service,\n            current_jobs: Arc::new(RwLock::new(Vec::new())),\n            is_running: Arc::new(RwLock::new(false)),\n        })\n    }\n\n    /// Start the backup scheduler\n    pub async fn start(&self) -> Result<()> {\n        if !self.config.enabled {\n            info!(\"Backup scheduler is disabled\");\n            return Ok(());\n        }\n\n        let mut is_running = self.is_running.write().await;\n        if *is_running {\n            warn!(\"Backup scheduler is already running\");\n            return Ok(());\n        }\n        *is_running = true;\n        drop(is_running);\n\n        info!(\"Starting backup scheduler\");\n\n        // Start backup task\n        let backup_task = self.start_backup_task();\n        \n        // Start cleanup task\n        let cleanup_task = self.start_cleanup_task();\n        \n        // Start verification task\n        let verification_task = self.start_verification_task();\n\n        // Run all tasks concurrently\n        tokio::try_join!(backup_task, cleanup_task, verification_task)?;\n\n        Ok(())\n    }\n\n    /// Stop the backup scheduler\n    pub async fn stop(&self) {\n        let mut is_running = self.is_running.write().await;\n        *is_running = false;\n        info!(\"Backup scheduler stopped\");\n    }\n\n    /// Check if scheduler is running\n    pub async fn is_running(&self) -> bool {\n        *self.is_running.read().await\n    }\n\n    /// Get current job statuses\n    pub async fn get_job_statuses(&self) -> Vec<BackupJobStatus> {\n        self.current_jobs.read().await.clone()\n    }\n\n    /// Trigger a manual backup\n    pub async fn trigger_backup(&self, backup_type: BackupJobType) -> Result<String> {\n        let job_id = uuid::Uuid::new_v4().to_string();\n        \n        let mut job_status = BackupJobStatus {\n            job_type: backup_type.clone(),\n            status: JobStatus::Running,\n            started_at: Some(Utc::now()),\n            completed_at: None,\n            error_message: None,\n            backup_id: None,\n        };\n\n        // Add job to current jobs\n        {\n            let mut jobs = self.current_jobs.write().await;\n            jobs.push(job_status.clone());\n        }\n\n        // Execute the backup\n        let result = match backup_type {\n            BackupJobType::FullBackup => {\n                self.backup_service.create_full_backup().await\n            }\n            BackupJobType::BaseBackup => {\n                self.backup_service.create_base_backup().await\n            }\n            _ => {\n                return Err(anyhow::anyhow!(\"Manual trigger not supported for this job type\"));\n            }\n        };\n\n        // Update job status\n        {\n            let mut jobs = self.current_jobs.write().await;\n            if let Some(job) = jobs.iter_mut().find(|j| j.started_at == job_status.started_at) {\n                match result {\n                    Ok(ref metadata) => {\n                        job.status = JobStatus::Completed;\n                        job.completed_at = Some(Utc::now());\n                        job.backup_id = Some(metadata.backup_id.clone());\n                    }\n                    Err(ref e) => {\n                        job.status = JobStatus::Failed;\n                        job.completed_at = Some(Utc::now());\n                        job.error_message = Some(e.to_string());\n                    }\n                }\n            }\n        }\n\n        match result {\n            Ok(metadata) => Ok(metadata.backup_id),\n            Err(e) => Err(e),\n        }\n    }\n\n    /// Get backup statistics\n    pub async fn get_backup_stats(&self) -> Result<BackupStats> {\n        self.backup_service.get_backup_stats().await\n    }\n\n    /// List available backups\n    pub async fn list_backups(&self) -> Result<Vec<BackupMetadata>> {\n        self.backup_service.list_backups().await\n    }\n\n    /// Verify a specific backup\n    pub async fn verify_backup(&self, backup_id: &str) -> Result<bool> {\n        self.backup_service.verify_backup(backup_id).await\n    }\n\n    /// Restore from backup\n    pub async fn restore_backup(&self, backup_id: &str, target_database: Option<&str>) -> Result<()> {\n        self.backup_service.restore_backup(backup_id, target_database).await\n    }\n\n    // Private methods for scheduled tasks\n    async fn start_backup_task(&self) -> Result<()> {\n        let mut interval = interval(std::time::Duration::from_secs(\n            self.config.backup_interval_hours * 3600\n        ));\n\n        loop {\n            interval.tick().await;\n            \n            if !*self.is_running.read().await {\n                break;\n            }\n\n            info!(\"Starting scheduled backup\");\n            \n            let job_status = BackupJobStatus {\n                job_type: BackupJobType::FullBackup,\n                status: JobStatus::Running,\n                started_at: Some(Utc::now()),\n                completed_at: None,\n                error_message: None,\n                backup_id: None,\n            };\n\n            // Add job to current jobs\n            {\n                let mut jobs = self.current_jobs.write().await;\n                jobs.push(job_status.clone());\n            }\n\n            // Execute backup\n            let result = self.backup_service.create_full_backup().await;\n            \n            // Update job status\n            {\n                let mut jobs = self.current_jobs.write().await;\n                if let Some(job) = jobs.iter_mut().find(|j| j.started_at == job_status.started_at) {\n                    match result {\n                        Ok(ref metadata) => {\n                            job.status = JobStatus::Completed;\n                            job.completed_at = Some(Utc::now());\n                            job.backup_id = Some(metadata.backup_id.clone());\n                            info!(\"Scheduled backup completed: {}\", metadata.backup_id);\n                        }\n                        Err(ref e) => {\n                            job.status = JobStatus::Failed;\n                            job.completed_at = Some(Utc::now());\n                            job.error_message = Some(e.to_string());\n                            error!(\"Scheduled backup failed: {}\", e);\n                        }\n                    }\n                }\n            }\n        }\n\n        Ok(())\n    }\n\n    async fn start_cleanup_task(&self) -> Result<()> {\n        if !self.config.auto_cleanup {\n            return Ok(());\n        }\n\n        let mut interval = interval(std::time::Duration::from_secs(\n            self.config.cleanup_interval_hours * 3600\n        ));\n\n        loop {\n            interval.tick().await;\n            \n            if !*self.is_running.read().await {\n                break;\n            }\n\n            info!(\"Starting scheduled cleanup\");\n            \n            let job_status = BackupJobStatus {\n                job_type: BackupJobType::Cleanup,\n                status: JobStatus::Running,\n                started_at: Some(Utc::now()),\n                completed_at: None,\n                error_message: None,\n                backup_id: None,\n            };\n\n            // Add job to current jobs\n            {\n                let mut jobs = self.current_jobs.write().await;\n                jobs.push(job_status.clone());\n            }\n\n            // Execute cleanup\n            let result = self.backup_service.cleanup_old_backups().await;\n            \n            // Update job status\n            {\n                let mut jobs = self.current_jobs.write().await;\n                if let Some(job) = jobs.iter_mut().find(|j| j.started_at == job_status.started_at) {\n                    match result {\n                        Ok(_) => {\n                            job.status = JobStatus::Completed;\n                            job.completed_at = Some(Utc::now());\n                            info!(\"Scheduled cleanup completed\");\n                        }\n                        Err(ref e) => {\n                            job.status = JobStatus::Failed;\n                            job.completed_at = Some(Utc::now());\n                            job.error_message = Some(e.to_string());\n                            error!(\"Scheduled cleanup failed: {}\", e);\n                        }\n                    }\n                }\n            }\n        }\n\n        Ok(())\n    }\n\n    async fn start_verification_task(&self) -> Result<()> {\n        if !self.config.verify_backups {\n            return Ok(());\n        }\n\n        let mut interval = interval(std::time::Duration::from_secs(\n            self.config.verification_interval_hours * 3600\n        ));\n\n        loop {\n            interval.tick().await;\n            \n            if !*self.is_running.read().await {\n                break;\n            }\n\n            info!(\"Starting scheduled backup verification\");\n            \n            let job_status = BackupJobStatus {\n                job_type: BackupJobType::Verification,\n                status: JobStatus::Running,\n                started_at: Some(Utc::now()),\n                completed_at: None,\n                error_message: None,\n                backup_id: None,\n            };\n\n            // Add job to current jobs\n            {\n                let mut jobs = self.current_jobs.write().await;\n                jobs.push(job_status.clone());\n            }\n\n            // Verify all backups\n            let result = self.verify_all_backups().await;\n            \n            // Update job status\n            {\n                let mut jobs = self.current_jobs.write().await;\n                if let Some(job) = jobs.iter_mut().find(|j| j.started_at == job_status.started_at) {\n                    match result {\n                        Ok(_) => {\n                            job.status = JobStatus::Completed;\n                            job.completed_at = Some(Utc::now());\n                            info!(\"Scheduled verification completed\");\n                        }\n                        Err(ref e) => {\n                            job.status = JobStatus::Failed;\n                            job.completed_at = Some(Utc::now());\n                            job.error_message = Some(e.to_string());\n                            error!(\"Scheduled verification failed: {}\", e);\n                        }\n                    }\n                }\n            }\n        }\n\n        Ok(())\n    }\n\n    async fn verify_all_backups(&self) -> Result<()> {\n        let backups = self.backup_service.list_backups().await?;\n        \n        for backup in backups {\n            match self.backup_service.verify_backup(&backup.backup_id).await {\n                Ok(true) => {\n                    info!(\"Backup {} verified successfully\", backup.backup_id);\n                }\n                Ok(false) => {\n                    warn!(\"Backup {} verification failed\", backup.backup_id);\n                }\n                Err(e) => {\n                    error!(\"Error verifying backup {}: {}\", backup.backup_id, e);\n                }\n            }\n        }\n\n        Ok(())\n    }\n}\n"}