{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/lib.rs"}, "originalCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::*;\npub use middleware::*;\npub use handlers::*;\npub use dto::*;\npub use state::*;\n\nuse axum::{\n    routing::{get, post},\n    Router,\n};\nuse tower::ServiceBuilder;\nuse tower_http::{\n    cors::CorsLayer,\n    trace::TraceLayer,\n    limit::RequestBodyLimitLayer,\n};\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(logging_layer())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n\n", "modifiedCode": "pub mod routes;\npub mod middleware;\npub mod handlers;\npub mod dto;\npub mod state;\n\npub use routes::*;\npub use middleware::*;\npub use handlers::*;\npub use dto::*;\npub use state::*;\n\nuse axum::Router;\nuse tower::ServiceBuilder;\nuse tower_http::limit::RequestBodyLimitLayer;\n\n/// Create the main application with all routes and middleware\npub fn create_app(state: AppState) -> Router {\n    Router::new()\n        .merge(create_routes())\n        .layer(\n            ServiceBuilder::new()\n                .layer(logging_layer())\n                .layer(RequestBodyLimitLayer::new(1024 * 1024)) // 1MB limit\n                .layer(cors_layer())\n        )\n        .with_state(state)\n}\n\n\n"}