{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/lib.rs"}, "originalCode": "pub mod entities;\npub mod migrations;\npub mod connection;\npub mod services;\n\npub use connection::DatabaseConnection;\npub use entities::*;\npub use services::*;\n\nuse anyhow::Result;\nuse sea_orm::{Database, DatabaseConnection as SeaOrmConnection, DbErr};\nuse sea_orm_migration::MigratorTrait;\n\n/// Initialize database with migrations\npub async fn initialize_database(database_url: &str) -> Result<SeaOrmConnection> {\n    let db = Database::connect(database_url).await?;\n    \n    // Run migrations\n    migrations::Migrator::up(&db, None).await?;\n    \n    Ok(db)\n}\n\n/// Health check for database connection\npub async fn health_check(db: &SeaOrmConnection) -> Result<(), DbErr> {\n    db.ping().await\n}\n", "modifiedCode": "pub mod entities;\npub mod migrations;\npub mod connection;\npub mod services;\npub mod backup;\n\npub use connection::DatabaseConnection;\npub use entities::*;\npub use services::*;\n\nuse anyhow::Result;\nuse sea_orm::{Database, DatabaseConnection as SeaOrmConnection, DbErr};\nuse sea_orm_migration::MigratorTrait;\n\n/// Initialize database with migrations\npub async fn initialize_database(database_url: &str) -> Result<SeaOrmConnection> {\n    let db = Database::connect(database_url).await?;\n    \n    // Run migrations\n    migrations::Migrator::up(&db, None).await?;\n    \n    Ok(db)\n}\n\n/// Health check for database connection\npub async fn health_check(db: &SeaOrmConnection) -> Result<(), DbErr> {\n    db.ping().await\n}\n"}