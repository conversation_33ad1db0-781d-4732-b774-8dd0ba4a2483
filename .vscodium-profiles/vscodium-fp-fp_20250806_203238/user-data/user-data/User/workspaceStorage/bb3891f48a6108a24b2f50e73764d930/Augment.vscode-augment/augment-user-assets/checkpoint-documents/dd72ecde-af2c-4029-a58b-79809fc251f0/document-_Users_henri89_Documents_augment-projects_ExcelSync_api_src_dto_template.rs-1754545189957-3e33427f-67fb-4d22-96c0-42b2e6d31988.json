{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/template.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\nuse super::common::{PaginatedResponse, FileMetadata};\n\n/// Template creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: Option<String>,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_default: Option<bool>,\n}\n\n/// Template update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub version: Option<String>,\n    pub schema: Option<Value>,\n    pub business_rules: Option<Value>,\n    pub validation_rules: Option<Value>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct TemplateResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: String,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: String,\n    pub created_at: String,\n    pub updated_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse serde_json::Value;\nuse validator::Validate;\nuse super::common::{PaginatedResponse, FileMetadata};\n\n/// Template creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: Option<String>,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_default: Option<bool>,\n}\n\n/// Template update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateTemplateRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub version: Option<String>,\n    pub schema: Option<Value>,\n    pub business_rules: Option<Value>,\n    pub validation_rules: Option<Value>,\n    pub is_active: Option<bool>,\n    pub is_default: Option<bool>,\n}\n\n/// Template response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct TemplateResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub template_type: String,\n    pub version: String,\n    pub schema: Value,\n    pub business_rules: Value,\n    pub validation_rules: Value,\n    pub is_active: bool,\n    pub is_default: bool,\n    pub created_by: String,\n    pub created_at: String,\n    pub updated_at: String,\n}\n\n/// Template list response with pagination\npub type TemplateListResponse = PaginatedResponse<TemplateResponse>;\n\n/// Template data processing request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct TemplateDataProcessingRequest {\n    pub template_id: String,\n    pub input_data: Value,\n    pub processing_options: Option<ProcessingOptions>,\n    pub validation_level: Option<ValidationLevel>,\n}\n\n/// Processing options\n#[derive(Debug, Deserialize)]\npub struct ProcessingOptions {\n    pub auto_calculate: Option<bool>,\n    pub validate_business_rules: Option<bool>,\n    pub generate_reports: Option<bool>,\n    pub save_intermediate_results: Option<bool>,\n}\n\n/// Validation levels\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ValidationLevel {\n    Basic,\n    Standard,\n    Strict,\n    Custom,\n}\n\n/// Template data processing response DTO\n#[derive(Debug, Serialize)]\npub struct TemplateDataProcessingResponse {\n    pub processing_id: String,\n    pub template_id: String,\n    pub processed_data: Value,\n    pub validation_results: Option<Value>,\n    pub calculated_fields: Option<Vec<CalculatedField>>,\n    pub processing_summary: ProcessingSummary,\n    pub warnings: Vec<ProcessingWarning>,\n    pub errors: Vec<ProcessingError>,\n}\n\n/// Calculated field\n#[derive(Debug, Serialize)]\npub struct CalculatedField {\n    pub field_name: String,\n    pub calculated_value: Value,\n    pub formula: String,\n    pub dependencies: Vec<String>,\n}\n\n/// Processing summary\n#[derive(Debug, Serialize)]\npub struct ProcessingSummary {\n    pub fields_processed: u32,\n    pub calculations_performed: u32,\n    pub validation_rules_applied: u32,\n    pub processing_time_ms: u64,\n    pub success_rate: f64,\n}\n\n/// Processing warning\n#[derive(Debug, Serialize)]\npub struct ProcessingWarning {\n    pub field_name: String,\n    pub warning_code: String,\n    pub message: String,\n    pub suggestion: Option<String>,\n}\n\n/// Processing error\n#[derive(Debug, Serialize)]\npub struct ProcessingError {\n    pub field_name: String,\n    pub error_code: String,\n    pub message: String,\n    pub severity: ErrorSeverity,\n}\n\n/// Error severity\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ErrorSeverity {\n    Warning,\n    Error,\n    Critical,\n}\n\n/// Template import request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct TemplateImportRequest {\n    pub file_metadata: FileMetadata,\n    pub import_options: Option<ImportOptions>,\n    pub validation_options: Option<ValidationOptions>,\n}\n\n/// Import options\n#[derive(Debug, Deserialize)]\npub struct ImportOptions {\n    pub overwrite_existing: Option<bool>,\n    pub create_backup: Option<bool>,\n    pub validate_schema: Option<bool>,\n    pub auto_activate: Option<bool>,\n}\n\n/// Validation options\n#[derive(Debug, Deserialize)]\npub struct ValidationOptions {\n    pub validate_business_rules: Option<bool>,\n    pub check_compatibility: Option<bool>,\n    pub validate_data_types: Option<bool>,\n    pub strict_validation: Option<bool>,\n}\n\n/// Template import response DTO\n#[derive(Debug, Serialize)]\npub struct TemplateImportResponse {\n    pub import_id: String,\n    pub template_id: Option<String>,\n    pub import_status: ImportStatus,\n    pub validation_results: Option<Value>,\n    pub import_summary: ImportSummary,\n    pub warnings: Vec<String>,\n    pub errors: Vec<String>,\n}\n\n/// Import status\n#[derive(Debug, Serialize)]\n#[serde(rename_all = \"snake_case\")]\npub enum ImportStatus {\n    Success,\n    PartialSuccess,\n    Failed,\n    ValidationFailed,\n}\n\n/// Import summary\n#[derive(Debug, Serialize)]\npub struct ImportSummary {\n    pub templates_imported: u32,\n    pub templates_updated: u32,\n    pub templates_skipped: u32,\n    pub validation_errors: u32,\n    pub import_time_ms: u64,\n}\n\n/// Template export request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct TemplateExportRequest {\n    pub template_ids: Vec<String>,\n    pub export_format: ExportFormat,\n    pub include_data: Option<bool>,\n    pub include_business_rules: Option<bool>,\n    pub include_validation_rules: Option<bool>,\n}\n\n/// Export format\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum ExportFormat {\n    Json,\n    Excel,\n    Csv,\n    Xml,\n}\n\n/// Template usage statistics DTO\n#[derive(Debug, Serialize)]\npub struct TemplateUsageStatistics {\n    pub template_id: String,\n    pub usage_count: u64,\n    pub unique_users: u64,\n    pub last_used: Option<String>,\n    pub average_processing_time: f64,\n    pub success_rate: f64,\n    pub most_common_errors: Vec<String>,\n}\n"}