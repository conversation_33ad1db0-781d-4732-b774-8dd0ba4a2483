{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/project.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Project creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub organization_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n}\n\n/// Project update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub status: Option<String>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n}\n\n/// Project response DTO\n#[derive(Debug, Serialize)]\npub struct ProjectResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub status: String,\n    pub organization_id: String,\n    pub created_by: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse super::common::{PaginatedResponse, DateRangeFilter};\n\n/// Project creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub organization_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n}\n\n/// Project update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub status: Option<String>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n}\n\n/// Project response DTO\n#[derive(Debug, Serialize)]\npub struct ProjectResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub status: String,\n    pub organization_id: String,\n    pub created_by: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n"}