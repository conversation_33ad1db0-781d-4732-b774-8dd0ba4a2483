{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/sessions.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"sessions\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub user_id: Uuid,\n    pub session_token: String,\n    pub ip_address: String,\n    pub user_agent: String,\n    \n    // Session metadata\n    pub is_active: bool,\n    pub expires_at: DateTime<Utc>,\n    pub last_activity: DateTime<Utc>,\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::UserId\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            last_activity: Set(Utc::now()),\n            is_active: Set(true),\n            expires_at: Set(Utc::now() + chrono::Duration::hours(24)),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"sessions\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub user_id: Uuid,\n    pub session_token: String,\n    pub ip_address: String,\n    pub user_agent: String,\n    \n    // Session metadata\n    pub is_active: bool,\n    pub expires_at: DateTime<Utc>,\n    pub last_activity: DateTime<Utc>,\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::UserId\",\n        to = \"super::users::Column::Id\"\n    )]\n    User,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::User.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            last_activity: Set(Utc::now()),\n            is_active: Set(true),\n            expires_at: Set(Utc::now() + chrono::Duration::hours(24)),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n"}