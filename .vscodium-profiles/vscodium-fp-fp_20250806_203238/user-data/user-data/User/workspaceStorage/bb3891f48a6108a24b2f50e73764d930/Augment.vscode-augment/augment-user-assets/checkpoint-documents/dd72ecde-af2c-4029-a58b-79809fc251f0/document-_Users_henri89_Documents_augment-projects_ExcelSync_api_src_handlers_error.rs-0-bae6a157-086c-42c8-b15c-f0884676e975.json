{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/error.rs"}, "originalCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    Json,\n};\nuse serde_json::json;\n\n/// API error types\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let (status, error_message) = match self {\n            ApiError::InternalServerError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Internal server error\"),\n            ApiError::BadRequest(_) => (StatusCode::BAD_REQUEST, \"Bad request\"),\n            ApiError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, \"Unauthorized\"),\n            ApiError::Forbidden(_) => (StatusCode::FORBIDDEN, \"Forbidden\"),\n            ApiError::NotFound(_) => (StatusCode::NOT_FOUND, \"Not found\"),\n            ApiError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Database error\"),\n            ApiError::AuthError(_) => (StatusCode::UNAUTHORIZED, \"Authentication error\"),\n        };\n\n        let body = Json(json!({\n            \"error\": error_message,\n            \"message\": self.to_string()\n        }));\n\n        (status, body).into_response()\n    }\n}\n", "modifiedCode": "use axum::{\n    http::StatusCode,\n    response::{IntoResponse, Response},\n    Json,\n};\nuse serde_json::json;\n\n/// API error types\n#[derive(Debug, thiserror::Error)]\npub enum ApiError {\n    #[error(\"Internal server error: {0}\")]\n    InternalServerError(String),\n    #[error(\"Bad request: {0}\")]\n    BadRequest(String),\n    #[error(\"Unauthorized: {0}\")]\n    Unauthorized(String),\n    #[error(\"Forbidden: {0}\")]\n    Forbidden(String),\n    #[error(\"Not found: {0}\")]\n    NotFound(String),\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Authentication error: {0}\")]\n    AuthError(String),\n}\n\nimpl IntoResponse for ApiError {\n    fn into_response(self) -> Response {\n        let (status, error_message) = match self {\n            ApiError::InternalServerError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Internal server error\"),\n            ApiError::BadRequest(_) => (StatusCode::BAD_REQUEST, \"Bad request\"),\n            ApiError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, \"Unauthorized\"),\n            ApiError::Forbidden(_) => (StatusCode::FORBIDDEN, \"Forbidden\"),\n            ApiError::NotFound(_) => (StatusCode::NOT_FOUND, \"Not found\"),\n            ApiError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, \"Database error\"),\n            ApiError::AuthError(_) => (StatusCode::UNAUTHORIZED, \"Authentication error\"),\n        };\n\n        let body = Json(json!({\n            \"error\": error_message,\n            \"message\": self.to_string()\n        }));\n\n        (status, body).into_response()\n    }\n}\n"}