{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/backup.rs"}, "originalCode": "use anyhow::{Result, Context};\nuse chrono::{DateTime, Utc, Duration};\nuse serde::{Deserialize, Serialize};\nuse std::path::{Path, PathBuf};\nuse std::process::Command;\nuse tokio::fs;\nuse tracing::{info, warn, error};\n\n/// Backup configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupConfig {\n    /// Database connection URL\n    pub database_url: String,\n    /// Backup storage directory\n    pub backup_dir: PathBuf,\n    /// Maximum number of backups to retain\n    pub max_backups: usize,\n    /// Backup compression level (0-9)\n    pub compression_level: u8,\n    /// Enable point-in-time recovery\n    pub enable_pitr: bool,\n    /// WAL archive directory for PITR\n    pub wal_archive_dir: Option<PathBuf>,\n    /// Backup schedule (cron expression)\n    pub schedule: Option<String>,\n}\n\nimpl Default for BackupConfig {\n    fn default() -> Self {\n        Self {\n            database_url: \"postgresql://localhost:5432/excelsync\".to_string(),\n            backup_dir: PathBuf::from(\"./backups\"),\n            max_backups: 7, // Keep 7 days of backups\n            compression_level: 6,\n            enable_pitr: true,\n            wal_archive_dir: Some(PathBuf::from(\"./wal_archive\")),\n            schedule: Some(\"0 2 * * *\".to_string()), // Daily at 2 AM\n        }\n    }\n}\n\n/// Backup metadata\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupMetadata {\n    pub backup_id: String,\n    pub created_at: DateTime<Utc>,\n    pub backup_type: BackupType,\n    pub file_path: PathBuf,\n    pub file_size: u64,\n    pub database_name: String,\n    pub pg_version: String,\n    pub checksum: String,\n    pub compression: bool,\n    pub wal_start_lsn: Option<String>,\n    pub wal_end_lsn: Option<String>,\n}\n\n/// Types of backups\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum BackupType {\n    Full,\n    Incremental,\n    Differential,\n}\n\n/// Backup service for PostgreSQL\npub struct BackupService {\n    config: BackupConfig,\n}\n\nimpl BackupService {\n    /// Create a new backup service\n    pub fn new(config: BackupConfig) -> Result<Self> {\n        // Create backup directory if it doesn't exist\n        std::fs::create_dir_all(&config.backup_dir)\n            .context(\"Failed to create backup directory\")?;\n\n        if let Some(wal_dir) = &config.wal_archive_dir {\n            std::fs::create_dir_all(wal_dir)\n                .context(\"Failed to create WAL archive directory\")?;\n        }\n\n        Ok(Self { config })\n    }\n\n    /// Create a full database backup\n    pub async fn create_full_backup(&self) -> Result<BackupMetadata> {\n        let backup_id = format!(\"full_{}\", Utc::now().format(\"%Y%m%d_%H%M%S\"));\n        let backup_filename = format!(\"{}.sql.gz\", backup_id);\n        let backup_path = self.config.backup_dir.join(&backup_filename);\n\n        info!(\"Starting full backup: {}\", backup_id);\n\n        // Use pg_dump to create the backup\n        let mut cmd = Command::new(\"pg_dump\");\n        cmd.arg(&self.config.database_url)\n            .arg(\"--verbose\")\n            .arg(\"--no-password\")\n            .arg(\"--format=custom\")\n            .arg(\"--compress\")\n            .arg(&format!(\"{}\", self.config.compression_level))\n            .arg(\"--file\")\n            .arg(&backup_path);\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_dump\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_dump failed: {}\", error_msg));\n        }\n\n        // Get file size\n        let file_size = fs::metadata(&backup_path).await\n            .context(\"Failed to get backup file metadata\")?\n            .len();\n\n        // Calculate checksum\n        let checksum = self.calculate_file_checksum(&backup_path).await?;\n\n        // Get PostgreSQL version\n        let pg_version = self.get_postgres_version().await?;\n\n        let metadata = BackupMetadata {\n            backup_id: backup_id.clone(),\n            created_at: Utc::now(),\n            backup_type: BackupType::Full,\n            file_path: backup_path,\n            file_size,\n            database_name: self.extract_database_name(&self.config.database_url),\n            pg_version,\n            checksum,\n            compression: true,\n            wal_start_lsn: None,\n            wal_end_lsn: None,\n        };\n\n        // Save metadata\n        self.save_backup_metadata(&metadata).await?;\n\n        info!(\"Full backup completed: {} ({} bytes)\", backup_id, file_size);\n        Ok(metadata)\n    }\n\n    /// Create a base backup for point-in-time recovery\n    pub async fn create_base_backup(&self) -> Result<BackupMetadata> {\n        if !self.config.enable_pitr {\n            return Err(anyhow::anyhow!(\"Point-in-time recovery is not enabled\"));\n        }\n\n        let backup_id = format!(\"base_{}\", Utc::now().format(\"%Y%m%d_%H%M%S\"));\n        let backup_dir = self.config.backup_dir.join(&backup_id);\n\n        info!(\"Starting base backup for PITR: {}\", backup_id);\n\n        // Create backup directory\n        fs::create_dir_all(&backup_dir).await\n            .context(\"Failed to create base backup directory\")?;\n\n        // Use pg_basebackup to create the base backup\n        let mut cmd = Command::new(\"pg_basebackup\");\n        cmd.arg(\"-D\")\n            .arg(&backup_dir)\n            .arg(\"-Ft\") // tar format\n            .arg(\"-z\") // compress\n            .arg(\"-P\") // progress\n            .arg(\"-v\") // verbose\n            .arg(\"-W\") // force password prompt\n            .arg(&format!(\"--dbname={}\", self.config.database_url));\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_basebackup\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_basebackup failed: {}\", error_msg));\n        }\n\n        // Calculate total size of backup directory\n        let file_size = self.calculate_directory_size(&backup_dir).await?;\n\n        // Calculate checksum of the main backup file\n        let base_tar = backup_dir.join(\"base.tar.gz\");\n        let checksum = if base_tar.exists() {\n            self.calculate_file_checksum(&base_tar).await?\n        } else {\n            \"unknown\".to_string()\n        };\n\n        let pg_version = self.get_postgres_version().await?;\n\n        let metadata = BackupMetadata {\n            backup_id: backup_id.clone(),\n            created_at: Utc::now(),\n            backup_type: BackupType::Full,\n            file_path: backup_dir,\n            file_size,\n            database_name: self.extract_database_name(&self.config.database_url),\n            pg_version,\n            checksum,\n            compression: true,\n            wal_start_lsn: None, // Would be populated by parsing backup_label\n            wal_end_lsn: None,\n        };\n\n        self.save_backup_metadata(&metadata).await?;\n\n        info!(\"Base backup completed: {} ({} bytes)\", backup_id, file_size);\n        Ok(metadata)\n    }\n\n    /// Restore from a backup\n    pub async fn restore_backup(&self, backup_id: &str, target_database: Option<&str>) -> Result<()> {\n        let metadata = self.load_backup_metadata(backup_id).await?;\n        \n        info!(\"Starting restore from backup: {}\", backup_id);\n\n        let target_db = target_database.unwrap_or(&metadata.database_name);\n\n        match metadata.backup_type {\n            BackupType::Full => {\n                if metadata.file_path.is_file() {\n                    // Restore from pg_dump backup\n                    self.restore_from_dump(&metadata.file_path, target_db).await?;\n                } else {\n                    // Restore from base backup\n                    self.restore_from_base_backup(&metadata.file_path, target_db).await?;\n                }\n            }\n            _ => {\n                return Err(anyhow::anyhow!(\"Incremental and differential restores not yet implemented\"));\n            }\n        }\n\n        info!(\"Restore completed successfully\");\n        Ok(())\n    }\n\n    /// Restore from pg_dump backup file\n    async fn restore_from_dump(&self, backup_path: &Path, target_database: &str) -> Result<()> {\n        let mut cmd = Command::new(\"pg_restore\");\n        cmd.arg(\"--verbose\")\n            .arg(\"--clean\")\n            .arg(\"--if-exists\")\n            .arg(\"--no-owner\")\n            .arg(\"--no-privileges\")\n            .arg(&format!(\"--dbname={}\", self.build_database_url(target_database)))\n            .arg(backup_path);\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_restore\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_restore failed: {}\", error_msg));\n        }\n\n        Ok(())\n    }\n\n    /// Restore from base backup (for PITR)\n    async fn restore_from_base_backup(&self, backup_path: &Path, _target_database: &str) -> Result<()> {\n        // This is a simplified implementation\n        // In a real scenario, you would:\n        // 1. Stop the PostgreSQL server\n        // 2. Clear the data directory\n        // 3. Extract the base backup\n        // 4. Configure recovery.conf or postgresql.conf for recovery\n        // 5. Start PostgreSQL in recovery mode\n        \n        warn!(\"Base backup restore is a complex operation that requires manual intervention\");\n        warn!(\"Backup location: {}\", backup_path.display());\n        warn!(\"Please follow PostgreSQL PITR documentation for complete restore\");\n        \n        Ok(())\n    }\n\n    /// List available backups\n    pub async fn list_backups(&self) -> Result<Vec<BackupMetadata>> {\n        let mut backups = Vec::new();\n        let mut entries = fs::read_dir(&self.config.backup_dir).await\n            .context(\"Failed to read backup directory\")?;\n\n        while let Some(entry) = entries.next_entry().await? {\n            let path = entry.path();\n            if path.extension().and_then(|s| s.to_str()) == Some(\"json\") {\n                if let Ok(metadata) = self.load_backup_metadata_from_file(&path).await {\n                    backups.push(metadata);\n                }\n            }\n        }\n\n        // Sort by creation date (newest first)\n        backups.sort_by(|a, b| b.created_at.cmp(&a.created_at));\n        Ok(backups)\n    }\n\n    /// Clean up old backups\n    pub async fn cleanup_old_backups(&self) -> Result<()> {\n        let backups = self.list_backups().await?;\n        \n        if backups.len() <= self.config.max_backups {\n            return Ok(());\n        }\n\n        let backups_to_remove = &backups[self.config.max_backups..];\n        \n        for backup in backups_to_remove {\n            info!(\"Removing old backup: {}\", backup.backup_id);\n            \n            // Remove backup file/directory\n            if backup.file_path.is_file() {\n                fs::remove_file(&backup.file_path).await\n                    .context(\"Failed to remove backup file\")?;\n            } else if backup.file_path.is_dir() {\n                fs::remove_dir_all(&backup.file_path).await\n                    .context(\"Failed to remove backup directory\")?;\n            }\n\n            // Remove metadata file\n            let metadata_path = self.get_metadata_path(&backup.backup_id);\n            if metadata_path.exists() {\n                fs::remove_file(&metadata_path).await\n                    .context(\"Failed to remove metadata file\")?;\n            }\n        }\n\n        info!(\"Cleaned up {} old backups\", backups_to_remove.len());\n        Ok(())\n    }\n", "modifiedCode": "use anyhow::{Result, Context};\nuse chrono::{DateTime, Utc, Duration};\nuse serde::{Deserialize, Serialize};\nuse std::path::{Path, PathBuf};\nuse std::process::Command;\nuse tokio::fs;\nuse tracing::{info, warn, error};\n\n/// Backup configuration\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupConfig {\n    /// Database connection URL\n    pub database_url: String,\n    /// Backup storage directory\n    pub backup_dir: PathBuf,\n    /// Maximum number of backups to retain\n    pub max_backups: usize,\n    /// Backup compression level (0-9)\n    pub compression_level: u8,\n    /// Enable point-in-time recovery\n    pub enable_pitr: bool,\n    /// WAL archive directory for PITR\n    pub wal_archive_dir: Option<PathBuf>,\n    /// Backup schedule (cron expression)\n    pub schedule: Option<String>,\n}\n\nimpl Default for BackupConfig {\n    fn default() -> Self {\n        Self {\n            database_url: \"postgresql://localhost:5432/excelsync\".to_string(),\n            backup_dir: PathBuf::from(\"./backups\"),\n            max_backups: 7, // Keep 7 days of backups\n            compression_level: 6,\n            enable_pitr: true,\n            wal_archive_dir: Some(PathBuf::from(\"./wal_archive\")),\n            schedule: Some(\"0 2 * * *\".to_string()), // Daily at 2 AM\n        }\n    }\n}\n\n/// Backup metadata\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct BackupMetadata {\n    pub backup_id: String,\n    pub created_at: DateTime<Utc>,\n    pub backup_type: BackupType,\n    pub file_path: PathBuf,\n    pub file_size: u64,\n    pub database_name: String,\n    pub pg_version: String,\n    pub checksum: String,\n    pub compression: bool,\n    pub wal_start_lsn: Option<String>,\n    pub wal_end_lsn: Option<String>,\n}\n\n/// Types of backups\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum BackupType {\n    Full,\n    Incremental,\n    Differential,\n}\n\n/// Backup service for PostgreSQL\npub struct BackupService {\n    config: BackupConfig,\n}\n\nimpl BackupService {\n    /// Create a new backup service\n    pub fn new(config: BackupConfig) -> Result<Self> {\n        // Create backup directory if it doesn't exist\n        std::fs::create_dir_all(&config.backup_dir)\n            .context(\"Failed to create backup directory\")?;\n\n        if let Some(wal_dir) = &config.wal_archive_dir {\n            std::fs::create_dir_all(wal_dir)\n                .context(\"Failed to create WAL archive directory\")?;\n        }\n\n        Ok(Self { config })\n    }\n\n    /// Create a full database backup\n    pub async fn create_full_backup(&self) -> Result<BackupMetadata> {\n        let backup_id = format!(\"full_{}\", Utc::now().format(\"%Y%m%d_%H%M%S\"));\n        let backup_filename = format!(\"{}.sql.gz\", backup_id);\n        let backup_path = self.config.backup_dir.join(&backup_filename);\n\n        info!(\"Starting full backup: {}\", backup_id);\n\n        // Use pg_dump to create the backup\n        let mut cmd = Command::new(\"pg_dump\");\n        cmd.arg(&self.config.database_url)\n            .arg(\"--verbose\")\n            .arg(\"--no-password\")\n            .arg(\"--format=custom\")\n            .arg(\"--compress\")\n            .arg(&format!(\"{}\", self.config.compression_level))\n            .arg(\"--file\")\n            .arg(&backup_path);\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_dump\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_dump failed: {}\", error_msg));\n        }\n\n        // Get file size\n        let file_size = fs::metadata(&backup_path).await\n            .context(\"Failed to get backup file metadata\")?\n            .len();\n\n        // Calculate checksum\n        let checksum = self.calculate_file_checksum(&backup_path).await?;\n\n        // Get PostgreSQL version\n        let pg_version = self.get_postgres_version().await?;\n\n        let metadata = BackupMetadata {\n            backup_id: backup_id.clone(),\n            created_at: Utc::now(),\n            backup_type: BackupType::Full,\n            file_path: backup_path,\n            file_size,\n            database_name: self.extract_database_name(&self.config.database_url),\n            pg_version,\n            checksum,\n            compression: true,\n            wal_start_lsn: None,\n            wal_end_lsn: None,\n        };\n\n        // Save metadata\n        self.save_backup_metadata(&metadata).await?;\n\n        info!(\"Full backup completed: {} ({} bytes)\", backup_id, file_size);\n        Ok(metadata)\n    }\n\n    /// Create a base backup for point-in-time recovery\n    pub async fn create_base_backup(&self) -> Result<BackupMetadata> {\n        if !self.config.enable_pitr {\n            return Err(anyhow::anyhow!(\"Point-in-time recovery is not enabled\"));\n        }\n\n        let backup_id = format!(\"base_{}\", Utc::now().format(\"%Y%m%d_%H%M%S\"));\n        let backup_dir = self.config.backup_dir.join(&backup_id);\n\n        info!(\"Starting base backup for PITR: {}\", backup_id);\n\n        // Create backup directory\n        fs::create_dir_all(&backup_dir).await\n            .context(\"Failed to create base backup directory\")?;\n\n        // Use pg_basebackup to create the base backup\n        let mut cmd = Command::new(\"pg_basebackup\");\n        cmd.arg(\"-D\")\n            .arg(&backup_dir)\n            .arg(\"-Ft\") // tar format\n            .arg(\"-z\") // compress\n            .arg(\"-P\") // progress\n            .arg(\"-v\") // verbose\n            .arg(\"-W\") // force password prompt\n            .arg(&format!(\"--dbname={}\", self.config.database_url));\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_basebackup\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_basebackup failed: {}\", error_msg));\n        }\n\n        // Calculate total size of backup directory\n        let file_size = self.calculate_directory_size(&backup_dir).await?;\n\n        // Calculate checksum of the main backup file\n        let base_tar = backup_dir.join(\"base.tar.gz\");\n        let checksum = if base_tar.exists() {\n            self.calculate_file_checksum(&base_tar).await?\n        } else {\n            \"unknown\".to_string()\n        };\n\n        let pg_version = self.get_postgres_version().await?;\n\n        let metadata = BackupMetadata {\n            backup_id: backup_id.clone(),\n            created_at: Utc::now(),\n            backup_type: BackupType::Full,\n            file_path: backup_dir,\n            file_size,\n            database_name: self.extract_database_name(&self.config.database_url),\n            pg_version,\n            checksum,\n            compression: true,\n            wal_start_lsn: None, // Would be populated by parsing backup_label\n            wal_end_lsn: None,\n        };\n\n        self.save_backup_metadata(&metadata).await?;\n\n        info!(\"Base backup completed: {} ({} bytes)\", backup_id, file_size);\n        Ok(metadata)\n    }\n\n    /// Restore from a backup\n    pub async fn restore_backup(&self, backup_id: &str, target_database: Option<&str>) -> Result<()> {\n        let metadata = self.load_backup_metadata(backup_id).await?;\n        \n        info!(\"Starting restore from backup: {}\", backup_id);\n\n        let target_db = target_database.unwrap_or(&metadata.database_name);\n\n        match metadata.backup_type {\n            BackupType::Full => {\n                if metadata.file_path.is_file() {\n                    // Restore from pg_dump backup\n                    self.restore_from_dump(&metadata.file_path, target_db).await?;\n                } else {\n                    // Restore from base backup\n                    self.restore_from_base_backup(&metadata.file_path, target_db).await?;\n                }\n            }\n            _ => {\n                return Err(anyhow::anyhow!(\"Incremental and differential restores not yet implemented\"));\n            }\n        }\n\n        info!(\"Restore completed successfully\");\n        Ok(())\n    }\n\n    /// Restore from pg_dump backup file\n    async fn restore_from_dump(&self, backup_path: &Path, target_database: &str) -> Result<()> {\n        let mut cmd = Command::new(\"pg_restore\");\n        cmd.arg(\"--verbose\")\n            .arg(\"--clean\")\n            .arg(\"--if-exists\")\n            .arg(\"--no-owner\")\n            .arg(\"--no-privileges\")\n            .arg(&format!(\"--dbname={}\", self.build_database_url(target_database)))\n            .arg(backup_path);\n\n        let output = cmd.output()\n            .context(\"Failed to execute pg_restore\")?;\n\n        if !output.status.success() {\n            let error_msg = String::from_utf8_lossy(&output.stderr);\n            return Err(anyhow::anyhow!(\"pg_restore failed: {}\", error_msg));\n        }\n\n        Ok(())\n    }\n\n    /// Restore from base backup (for PITR)\n    async fn restore_from_base_backup(&self, backup_path: &Path, _target_database: &str) -> Result<()> {\n        // This is a simplified implementation\n        // In a real scenario, you would:\n        // 1. Stop the PostgreSQL server\n        // 2. Clear the data directory\n        // 3. Extract the base backup\n        // 4. Configure recovery.conf or postgresql.conf for recovery\n        // 5. Start PostgreSQL in recovery mode\n        \n        warn!(\"Base backup restore is a complex operation that requires manual intervention\");\n        warn!(\"Backup location: {}\", backup_path.display());\n        warn!(\"Please follow PostgreSQL PITR documentation for complete restore\");\n        \n        Ok(())\n    }\n\n    /// List available backups\n    pub async fn list_backups(&self) -> Result<Vec<BackupMetadata>> {\n        let mut backups = Vec::new();\n        let mut entries = fs::read_dir(&self.config.backup_dir).await\n            .context(\"Failed to read backup directory\")?;\n\n        while let Some(entry) = entries.next_entry().await? {\n            let path = entry.path();\n            if path.extension().and_then(|s| s.to_str()) == Some(\"json\") {\n                if let Ok(metadata) = self.load_backup_metadata_from_file(&path).await {\n                    backups.push(metadata);\n                }\n            }\n        }\n\n        // Sort by creation date (newest first)\n        backups.sort_by(|a, b| b.created_at.cmp(&a.created_at));\n        Ok(backups)\n    }\n\n    /// Clean up old backups\n    pub async fn cleanup_old_backups(&self) -> Result<()> {\n        let backups = self.list_backups().await?;\n        \n        if backups.len() <= self.config.max_backups {\n            return Ok(());\n        }\n\n        let backups_to_remove = &backups[self.config.max_backups..];\n        \n        for backup in backups_to_remove {\n            info!(\"Removing old backup: {}\", backup.backup_id);\n            \n            // Remove backup file/directory\n            if backup.file_path.is_file() {\n                fs::remove_file(&backup.file_path).await\n                    .context(\"Failed to remove backup file\")?;\n            } else if backup.file_path.is_dir() {\n                fs::remove_dir_all(&backup.file_path).await\n                    .context(\"Failed to remove backup directory\")?;\n            }\n\n            // Remove metadata file\n            let metadata_path = self.get_metadata_path(&backup.backup_id);\n            if metadata_path.exists() {\n                fs::remove_file(&metadata_path).await\n                    .context(\"Failed to remove metadata file\")?;\n            }\n        }\n\n        info!(\"Cleaned up {} old backups\", backups_to_remove.len());\n        Ok(())\n    }\n\n    /// Verify backup integrity\n    pub async fn verify_backup(&self, backup_id: &str) -> Result<bool> {\n        let metadata = self.load_backup_metadata(backup_id).await?;\n\n        // Check if backup file exists\n        if !metadata.file_path.exists() {\n            error!(\"Backup file not found: {}\", metadata.file_path.display());\n            return Ok(false);\n        }\n\n        // Verify checksum\n        let current_checksum = self.calculate_file_checksum(&metadata.file_path).await?;\n        if current_checksum != metadata.checksum {\n            error!(\"Backup checksum mismatch for {}: expected {}, got {}\",\n                   backup_id, metadata.checksum, current_checksum);\n            return Ok(false);\n        }\n\n        // Verify file size\n        let current_size = if metadata.file_path.is_file() {\n            fs::metadata(&metadata.file_path).await?.len()\n        } else {\n            self.calculate_directory_size(&metadata.file_path).await?\n        };\n\n        if current_size != metadata.file_size {\n            error!(\"Backup size mismatch for {}: expected {}, got {}\",\n                   backup_id, metadata.file_size, current_size);\n            return Ok(false);\n        }\n\n        info!(\"Backup {} verified successfully\", backup_id);\n        Ok(true)\n    }\n\n    /// Get backup statistics\n    pub async fn get_backup_stats(&self) -> Result<BackupStats> {\n        let backups = self.list_backups().await?;\n\n        let total_backups = backups.len();\n        let total_size: u64 = backups.iter().map(|b| b.file_size).sum();\n\n        let full_backups = backups.iter().filter(|b| matches!(b.backup_type, BackupType::Full)).count();\n        let incremental_backups = backups.iter().filter(|b| matches!(b.backup_type, BackupType::Incremental)).count();\n\n        let oldest_backup = backups.last().map(|b| b.created_at);\n        let newest_backup = backups.first().map(|b| b.created_at);\n\n        Ok(BackupStats {\n            total_backups,\n            total_size,\n            full_backups,\n            incremental_backups,\n            oldest_backup,\n            newest_backup,\n        })\n    }\n\n    // Helper methods\n    async fn save_backup_metadata(&self, metadata: &BackupMetadata) -> Result<()> {\n        let metadata_path = self.get_metadata_path(&metadata.backup_id);\n        let json = serde_json::to_string_pretty(metadata)\n            .context(\"Failed to serialize backup metadata\")?;\n\n        fs::write(&metadata_path, json).await\n            .context(\"Failed to write backup metadata\")?;\n\n        Ok(())\n    }\n\n    async fn load_backup_metadata(&self, backup_id: &str) -> Result<BackupMetadata> {\n        let metadata_path = self.get_metadata_path(backup_id);\n        self.load_backup_metadata_from_file(&metadata_path).await\n    }\n\n    async fn load_backup_metadata_from_file(&self, path: &Path) -> Result<BackupMetadata> {\n        let json = fs::read_to_string(path).await\n            .context(\"Failed to read backup metadata\")?;\n\n        let metadata: BackupMetadata = serde_json::from_str(&json)\n            .context(\"Failed to deserialize backup metadata\")?;\n\n        Ok(metadata)\n    }\n\n    fn get_metadata_path(&self, backup_id: &str) -> PathBuf {\n        self.config.backup_dir.join(format!(\"{}.json\", backup_id))\n    }\n\n    async fn calculate_file_checksum(&self, path: &Path) -> Result<String> {\n        use sha2::{Sha256, Digest};\n\n        let content = fs::read(path).await\n            .context(\"Failed to read file for checksum calculation\")?;\n\n        let mut hasher = Sha256::new();\n        hasher.update(&content);\n        let result = hasher.finalize();\n\n        Ok(format!(\"{:x}\", result))\n    }\n\n    async fn calculate_directory_size(&self, dir: &Path) -> Result<u64> {\n        let mut total_size = 0;\n        let mut entries = fs::read_dir(dir).await?;\n\n        while let Some(entry) = entries.next_entry().await? {\n            let metadata = entry.metadata().await?;\n            if metadata.is_file() {\n                total_size += metadata.len();\n            } else if metadata.is_dir() {\n                total_size += self.calculate_directory_size(&entry.path()).await?;\n            }\n        }\n\n        Ok(total_size)\n    }\n\n    async fn get_postgres_version(&self) -> Result<String> {\n        let mut cmd = Command::new(\"psql\");\n        cmd.arg(&self.config.database_url)\n            .arg(\"-t\")\n            .arg(\"-c\")\n            .arg(\"SELECT version();\");\n\n        let output = cmd.output()\n            .context(\"Failed to get PostgreSQL version\")?;\n\n        if output.status.success() {\n            let version = String::from_utf8_lossy(&output.stdout)\n                .trim()\n                .to_string();\n            Ok(version)\n        } else {\n            Ok(\"unknown\".to_string())\n        }\n    }\n\n    fn extract_database_name(&self, database_url: &str) -> String {\n        // Simple extraction from URL - in production, use a proper URL parser\n        database_url.split('/').last().unwrap_or(\"unknown\").to_string()\n    }\n\n    fn build_database_url(&self, database_name: &str) -> String {\n        // Replace the database name in the URL\n        let mut parts: Vec<&str> = self.config.database_url.split('/').collect();\n        if let Some(last) = parts.last_mut() {\n            *last = database_name;\n        }\n        parts.join(\"/\")\n    }\n}\n\n/// Backup statistics\n#[derive(Debug, Serialize, Deserialize)]\npub struct BackupStats {\n    pub total_backups: usize,\n    pub total_size: u64,\n    pub full_backups: usize,\n    pub incremental_backups: usize,\n    pub oldest_backup: Option<DateTime<Utc>>,\n    pub newest_backup: Option<DateTime<Utc>>,\n}\n"}