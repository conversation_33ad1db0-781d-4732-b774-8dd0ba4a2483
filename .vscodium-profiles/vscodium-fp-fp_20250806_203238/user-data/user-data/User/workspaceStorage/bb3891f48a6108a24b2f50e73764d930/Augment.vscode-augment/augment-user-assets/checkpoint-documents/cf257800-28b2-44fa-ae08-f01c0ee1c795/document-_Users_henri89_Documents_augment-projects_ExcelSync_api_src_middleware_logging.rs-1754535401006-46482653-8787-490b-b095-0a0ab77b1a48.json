{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/logging.rs"}, "originalCode": "use tower_http::trace::TraceLayer;\n\n/// Create logging middleware layer\npub fn logging_layer() -> TraceLayer {\n    TraceLayer::new_for_http()\n}\n", "modifiedCode": "use tower_http::trace::TraceLayer;\n\n/// Create logging middleware layer\npub fn logging_layer() -> TraceLayer<tower_http::trace::DefaultMakeSpan, tower_http::trace::DefaultOnResponse> {\n    TraceLayer::new_for_http()\n}\n"}