{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/project_data.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"project_data\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub project_id: Uuid,\n    pub template_id: Uuid,\n    pub version: i32,\n    \n    // Data storage\n    pub data: Json,\n    pub encrypted_data: Option<String>,\n    pub checksum: String,\n    \n    // Metadata\n    pub is_current: bool,\n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::projects::Entity\",\n        from = \"Column::ProjectId\",\n        to = \"super::projects::Column::Id\"\n    )]\n    Project,\n    \n    #[sea_orm(\n        belongs_to = \"super::templates::Entity\",\n        from = \"Column::TemplateId\",\n        to = \"super::templates::Column::Id\"\n    )]\n    Template,\n    \n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CreatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    Creator,\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Project.def()\n    }\n}\n\nimpl Related<super::templates::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Template.def()\n    }\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Creator.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            version: Set(1),\n            is_current: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"project_data\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub project_id: Uuid,\n    pub template_id: Uuid,\n    pub version: i32,\n    \n    // Data storage\n    pub data: Json,\n    pub encrypted_data: Option<String>,\n    pub checksum: String,\n    \n    // Metadata\n    pub is_current: bool,\n    pub created_by: Uuid,\n    pub created_at: DateTime<Utc>,\n}\n\n#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::projects::Entity\",\n        from = \"Column::ProjectId\",\n        to = \"super::projects::Column::Id\"\n    )]\n    Project,\n    \n    #[sea_orm(\n        belongs_to = \"super::templates::Entity\",\n        from = \"Column::TemplateId\",\n        to = \"super::templates::Column::Id\"\n    )]\n    Template,\n    \n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::CreatedBy\",\n        to = \"super::users::Column::Id\"\n    )]\n    Creator,\n}\n\nimpl Related<super::projects::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Project.def()\n    }\n}\n\nimpl Related<super::templates::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Template.def()\n    }\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Creator.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            version: Set(1),\n            is_current: Set(true),\n            ..ActiveModelTrait::default()\n        }\n    }\n}\n"}