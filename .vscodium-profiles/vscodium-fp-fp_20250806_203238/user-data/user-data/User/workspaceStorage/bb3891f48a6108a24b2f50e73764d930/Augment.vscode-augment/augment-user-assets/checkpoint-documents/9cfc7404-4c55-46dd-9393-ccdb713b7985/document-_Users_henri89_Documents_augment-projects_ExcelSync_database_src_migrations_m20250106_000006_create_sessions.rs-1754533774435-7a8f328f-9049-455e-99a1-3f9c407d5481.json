{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000006_create_sessions.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_table(\n                Table::create()\n                    .table(Sessions::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(Sessions::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(Sessions::UserId).uuid().not_null())\n                    .col(ColumnDef::new(Sessions::SessionToken).string().not_null().unique_key())\n                    .col(ColumnDef::new(Sessions::IpAddress).string().not_null())\n                    .col(ColumnDef::new(Sessions::UserAgent).text().not_null())\n                    .col(ColumnDef::new(Sessions::IsActive).boolean().not_null().default(true))\n                    .col(ColumnDef::new(Sessions::ExpiresAt).timestamp().not_null())\n                    .col(ColumnDef::new(Sessions::LastActivity).timestamp().not_null())\n                    .col(ColumnDef::new(Sessions::CreatedAt).timestamp().not_null())\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_sessions_user\")\n                            .from(Sessions::Table, Sessions::UserId)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Sessions::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Sessions {\n    Table,\n    Id,\n    UserId,\n    SessionToken,\n    IpAddress,\n    UserAgent,\n    IsActive,\n    ExpiresAt,\n    LastActivity,\n    CreatedAt,\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n}\n"}