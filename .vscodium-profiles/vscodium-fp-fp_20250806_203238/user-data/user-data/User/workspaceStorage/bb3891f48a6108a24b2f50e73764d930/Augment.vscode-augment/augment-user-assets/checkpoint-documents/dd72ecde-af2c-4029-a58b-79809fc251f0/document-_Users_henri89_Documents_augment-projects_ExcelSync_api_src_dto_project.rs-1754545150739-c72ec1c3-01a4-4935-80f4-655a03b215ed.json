{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/project.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse super::common::{PaginatedResponse, DateRangeFilter};\n\n/// Project creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub organization_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n}\n\n/// Project update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub status: Option<String>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n}\n\n/// Project response DTO\n#[derive(Debug, Serialize)]\npub struct ProjectResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub status: String,\n    pub organization_id: String,\n    pub created_by: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse super::common::{PaginatedResponse, DateRangeFilter};\n\n/// Project creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub organization_id: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n}\n\n/// Project update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateProjectRequest {\n    #[validate(length(min = 2))]\n    pub name: Option<String>,\n    pub description: Option<String>,\n    pub status: Option<String>,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n}\n\n/// Project response DTO\n#[derive(Debug, Serialize)]\npub struct ProjectResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: String,\n    pub status: String,\n    pub organization_id: String,\n    pub created_by: String,\n    pub start_date: Option<String>,\n    pub end_date: Option<String>,\n    pub budget: Option<f64>,\n    pub completion_percentage: Option<i32>,\n    pub created_at: String,\n    pub updated_at: String,\n\n    // Real estate specific fields\n    pub land_area: Option<f64>,\n    pub land_location: Option<String>,\n    pub land_use_type: Option<String>,\n    pub total_investment: Option<f64>,\n    pub expected_revenue: Option<f64>,\n    pub roi_percentage: Option<f64>,\n}\n\n/// Project list response with pagination\npub type ProjectListResponse = PaginatedResponse<ProjectResponse>;\n\n/// Project summary DTO\n#[derive(Debug, Serialize)]\npub struct ProjectSummary {\n    pub id: String,\n    pub name: String,\n    pub project_type: String,\n    pub status: String,\n    pub completion_percentage: Option<i32>,\n    pub budget: Option<f64>,\n    pub roi_percentage: Option<f64>,\n    pub last_updated: String,\n}\n\n/// Project statistics DTO\n#[derive(Debug, Serialize)]\npub struct ProjectStatistics {\n    pub total_projects: u64,\n    pub active_projects: u64,\n    pub completed_projects: u64,\n    pub total_investment: f64,\n    pub total_expected_revenue: f64,\n    pub average_roi: f64,\n    pub projects_by_type: Vec<ProjectTypeCount>,\n    pub projects_by_status: Vec<ProjectStatusCount>,\n}\n\n/// Project type count\n#[derive(Debug, Serialize)]\npub struct ProjectTypeCount {\n    pub project_type: String,\n    pub count: u64,\n    pub total_investment: f64,\n}\n\n/// Project status count\n#[derive(Debug, Serialize)]\npub struct ProjectStatusCount {\n    pub status: String,\n    pub count: u64,\n    pub percentage: f64,\n}\n\n/// Project filter request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectFilterRequest {\n    pub project_type: Option<String>,\n    pub status: Option<String>,\n    pub organization_id: Option<String>,\n    pub created_by: Option<String>,\n    pub date_range: Option<DateRangeFilter>,\n    pub budget_min: Option<f64>,\n    pub budget_max: Option<f64>,\n    pub roi_min: Option<f64>,\n    pub roi_max: Option<f64>,\n}\n\n/// Project data export request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectExportRequest {\n    pub project_ids: Option<Vec<String>>,\n    pub filters: Option<ProjectFilterRequest>,\n    pub export_format: ExportFormat,\n    pub include_financial_data: Option<bool>,\n    pub include_templates: Option<bool>,\n}\n\n/// Export formats\n#[derive(Debug, Deserialize, Serialize)]\n#[serde(rename_all = \"lowercase\")]\npub enum ExportFormat {\n    Json,\n    Csv,\n    Excel,\n    Pdf,\n}\n"}