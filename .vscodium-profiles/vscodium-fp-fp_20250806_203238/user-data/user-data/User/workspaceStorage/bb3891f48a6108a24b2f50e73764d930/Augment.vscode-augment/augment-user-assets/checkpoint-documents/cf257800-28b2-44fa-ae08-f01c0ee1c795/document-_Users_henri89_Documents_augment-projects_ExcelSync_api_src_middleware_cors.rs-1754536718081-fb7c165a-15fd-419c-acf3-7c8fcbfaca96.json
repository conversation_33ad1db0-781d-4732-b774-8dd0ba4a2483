{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/cors.rs"}, "originalCode": "use tower_http::cors::{Any, CorsLayer};\n\n/// Create CORS middleware layer\npub fn cors_layer() -> CorsLayer {\n    CorsLayer::new()\n        .allow_origin(Any)\n        .allow_methods(Any)\n        .allow_headers(Any)\n}\n", "modifiedCode": "use tower_http::cors::{Any, CorsLayer};\nuse http::{HeaderValue, Method};\n\n/// Create CORS middleware layer for development\npub fn cors_layer() -> CorsLayer {\n    CorsLayer::new()\n        .allow_origin(Any)\n        .allow_methods(Any)\n        .allow_headers(Any)\n}\n\n/// Create production CORS middleware layer with strict settings\npub fn production_cors_layer() -> CorsLayer {\n    CorsLayer::new()\n        .allow_origin([\n            \"https://excelsync.com\".parse::<HeaderValue>().unwrap(),\n            \"https://app.excelsync.com\".parse::<HeaderValue>().unwrap(),\n            \"https://admin.excelsync.com\".parse::<HeaderValue>().unwrap(),\n        ])\n        .allow_methods([\n            Method::GET,\n            Method::POST,\n            Method::PUT,\n            Method::DELETE,\n            Method::OPTIONS,\n        ])\n        .allow_headers([\n            \"authorization\",\n            \"content-type\",\n            \"x-requested-with\",\n            \"accept\",\n            \"origin\",\n            \"user-agent\",\n        ])\n        .allow_credentials(true)\n        .max_age(std::time::Duration::from_secs(3600)) // 1 hour\n}\n\n/// Create development CORS middleware layer with localhost support\npub fn development_cors_layer() -> CorsLayer {\n    CorsLayer::new()\n        .allow_origin([\n            \"http://localhost:3000\".parse::<HeaderValue>().unwrap(),\n            \"http://localhost:3001\".parse::<HeaderValue>().unwrap(),\n            \"http://127.0.0.1:3000\".parse::<HeaderValue>().unwrap(),\n            \"http://127.0.0.1:3001\".parse::<HeaderValue>().unwrap(),\n        ])\n        .allow_methods([\n            Method::GET,\n            Method::POST,\n            Method::PUT,\n            Method::DELETE,\n            Method::OPTIONS,\n            Method::PATCH,\n        ])\n        .allow_headers(Any)\n        .allow_credentials(true)\n        .max_age(std::time::Duration::from_secs(300)) // 5 minutes\n}\n"}