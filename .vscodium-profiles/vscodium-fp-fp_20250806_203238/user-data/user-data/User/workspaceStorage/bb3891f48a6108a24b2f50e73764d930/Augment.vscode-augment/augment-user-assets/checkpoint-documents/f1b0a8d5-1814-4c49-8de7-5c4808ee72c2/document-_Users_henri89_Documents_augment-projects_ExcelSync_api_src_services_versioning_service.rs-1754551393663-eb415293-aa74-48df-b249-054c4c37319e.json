{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/versioning_service.rs"}, "modifiedCode": "use anyhow::Result;\nuse sea_orm::{\n    DatabaseConnection, EntityTrait, Set, ActiveModelTrait, QueryFilter, ColumnTrait,\n    QueryOrder, PaginatorTrait, TransactionTrait, DatabaseTransaction,\n};\nuse uuid::Uuid;\nuse chrono::Utc;\nuse serde_json::{Value, json};\nuse thiserror::Error;\n\nuse crate::handlers::ApiError;\nuse database::entities::{data_versions, projects};\nuse database::entities::data_versions::{\n    VersionConflict, ConflictType, MergeStrategy, VersionComparison, \n    FieldChange, ChangeType, VersionBranch\n};\n\n/// Versioning service errors\n#[derive(Error, Debug)]\npub enum VersioningError {\n    #[error(\"Version not found: {0}\")]\n    VersionNotFound(String),\n    #[error(\"Conflict detected: {0}\")]\n    ConflictDetected(String),\n    #[error(\"Invalid merge strategy: {0}\")]\n    InvalidMergeStrategy(String),\n    #[error(\"Database error: {0}\")]\n    DatabaseError(String),\n    #[error(\"Serialization error: {0}\")]\n    SerializationError(String),\n    #[error(\"Permission denied: {0}\")]\n    PermissionDenied(String),\n}\n\n/// Data versioning service for managing entity versions and conflict resolution\npub struct VersioningService {\n    db: DatabaseConnection,\n}\n\nimpl VersioningService {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self { db }\n    }\n\n    /// Create a new version of an entity\n    pub async fn create_version(\n        &self,\n        project_id: Uuid,\n        entity_type: &str,\n        entity_id: Uuid,\n        data: &Value,\n        change_summary: &str,\n        created_by: Uuid,\n        parent_version_id: Option<Uuid>,\n    ) -> Result<Uuid, VersioningError> {\n        let txn = self.db.begin().await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        // Get the next version number\n        let version_number = self.get_next_version_number(&txn, project_id, entity_type, entity_id).await?;\n\n        // Calculate changes from parent version\n        let changes = if let Some(parent_id) = parent_version_id {\n            self.calculate_changes(&txn, parent_id, data).await?\n        } else {\n            json!({\n                \"type\": \"initial\",\n                \"changes\": []\n            })\n        };\n\n        // Mark previous current version as non-current\n        self.unset_current_version(&txn, project_id, entity_type, entity_id).await?;\n\n        // Create new version\n        let version_id = Uuid::new_v4();\n        let new_version = data_versions::ActiveModel {\n            id: Set(version_id),\n            project_id: Set(project_id),\n            entity_type: Set(entity_type.to_string()),\n            entity_id: Set(entity_id),\n            version_number: Set(version_number),\n            parent_version_id: Set(parent_version_id),\n            data_snapshot: Set(data.clone()),\n            changes: Set(changes),\n            change_summary: Set(change_summary.to_string()),\n            created_by: Set(created_by),\n            created_at: Set(Utc::now()),\n            is_current: Set(true),\n            is_merged: Set(false),\n            ..Default::default()\n        };\n\n        new_version.insert(&txn).await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        txn.commit().await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok(version_id)\n    }\n\n    /// Get the current version of an entity\n    pub async fn get_current_version(\n        &self,\n        project_id: Uuid,\n        entity_type: &str,\n        entity_id: Uuid,\n    ) -> Result<Option<data_versions::Model>, VersioningError> {\n        let version = data_versions::Entity::find()\n            .filter(data_versions::Column::ProjectId.eq(project_id))\n            .filter(data_versions::Column::EntityType.eq(entity_type))\n            .filter(data_versions::Column::EntityId.eq(entity_id))\n            .filter(data_versions::Column::IsCurrent.eq(true))\n            .one(&self.db)\n            .await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok(version)\n    }\n\n    /// Get version history for an entity\n    pub async fn get_version_history(\n        &self,\n        project_id: Uuid,\n        entity_type: &str,\n        entity_id: Uuid,\n        page: u64,\n        per_page: u64,\n    ) -> Result<(Vec<data_versions::Model>, u64), VersioningError> {\n        let page = page.max(1);\n        let per_page = per_page.min(100).max(1);\n\n        let paginator = data_versions::Entity::find()\n            .filter(data_versions::Column::ProjectId.eq(project_id))\n            .filter(data_versions::Column::EntityType.eq(entity_type))\n            .filter(data_versions::Column::EntityId.eq(entity_id))\n            .order_by_desc(data_versions::Column::VersionNumber)\n            .paginate(&self.db, per_page);\n\n        let total = paginator.num_items().await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        let versions = paginator.fetch_page(page - 1).await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok((versions, total))\n    }\n\n    /// Compare two versions and detect conflicts\n    pub async fn compare_versions(\n        &self,\n        from_version_id: Uuid,\n        to_version_id: Uuid,\n    ) -> Result<VersionComparison, VersioningError> {\n        let from_version = self.get_version_by_id(from_version_id).await?;\n        let to_version = self.get_version_by_id(to_version_id).await?;\n\n        let changes = self.calculate_field_changes(&from_version.data_snapshot, &to_version.data_snapshot)?;\n        let conflicts = self.detect_conflicts(&from_version, &to_version).await?;\n        let is_mergeable = conflicts.is_empty();\n\n        Ok(VersionComparison {\n            from_version: from_version_id,\n            to_version: to_version_id,\n            changes,\n            conflicts,\n            is_mergeable,\n        })\n    }\n\n    /// Merge two versions with conflict resolution\n    pub async fn merge_versions(\n        &self,\n        base_version_id: Uuid,\n        source_version_id: Uuid,\n        target_version_id: Uuid,\n        merge_strategy: MergeStrategy,\n        manual_resolutions: Option<Value>,\n        created_by: Uuid,\n    ) -> Result<Uuid, VersioningError> {\n        let txn = self.db.begin().await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        let base_version = self.get_version_by_id(base_version_id).await?;\n        let source_version = self.get_version_by_id(source_version_id).await?;\n        let target_version = self.get_version_by_id(target_version_id).await?;\n\n        // Detect conflicts\n        let conflicts = self.detect_conflicts(&source_version, &target_version).await?;\n\n        // Resolve conflicts based on strategy\n        let merged_data = match merge_strategy {\n            MergeStrategy::LatestWins => {\n                if target_version.created_at > source_version.created_at {\n                    target_version.data_snapshot.clone()\n                } else {\n                    source_version.data_snapshot.clone()\n                }\n            }\n            MergeStrategy::ManualResolve => {\n                if !conflicts.is_empty() && manual_resolutions.is_none() {\n                    return Err(VersioningError::ConflictDetected(\n                        \"Manual resolution required but not provided\".to_string()\n                    ));\n                }\n                self.apply_manual_resolutions(\n                    &base_version.data_snapshot,\n                    &source_version.data_snapshot,\n                    &target_version.data_snapshot,\n                    manual_resolutions.as_ref(),\n                )?\n            }\n            MergeStrategy::FieldLevel => {\n                self.merge_at_field_level(\n                    &base_version.data_snapshot,\n                    &source_version.data_snapshot,\n                    &target_version.data_snapshot,\n                )?\n            }\n            MergeStrategy::Custom(strategy) => {\n                return Err(VersioningError::InvalidMergeStrategy(\n                    format!(\"Custom strategy '{}' not implemented\", strategy)\n                ));\n            }\n        };\n\n        // Create merge version\n        let merge_version_id = self.create_version(\n            base_version.project_id,\n            &base_version.entity_type,\n            base_version.entity_id,\n            &merged_data,\n            &format!(\"Merged versions {} and {}\", source_version_id, target_version_id),\n            created_by,\n            Some(target_version_id),\n        ).await?;\n\n        // Mark as merged\n        let mut merge_version: data_versions::ActiveModel = data_versions::Entity::find_by_id(merge_version_id)\n            .one(&txn)\n            .await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?\n            .ok_or_else(|| VersioningError::VersionNotFound(merge_version_id.to_string()))?\n            .into();\n\n        merge_version.is_merged = Set(true);\n        merge_version.conflict_resolution_strategy = Set(Some(serde_json::to_string(&merge_strategy)\n            .map_err(|e| VersioningError::SerializationError(e.to_string()))?));\n\n        if !conflicts.is_empty() {\n            merge_version.conflict_metadata = Set(Some(serde_json::to_value(&conflicts)\n                .map_err(|e| VersioningError::SerializationError(e.to_string()))?));\n        }\n\n        merge_version.update(&txn).await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        txn.commit().await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok(merge_version_id)\n    }\n\n    /// Revert to a previous version\n    pub async fn revert_to_version(\n        &self,\n        version_id: Uuid,\n        created_by: Uuid,\n    ) -> Result<Uuid, VersioningError> {\n        let version = self.get_version_by_id(version_id).await?;\n\n        let new_version_id = self.create_version(\n            version.project_id,\n            &version.entity_type,\n            version.entity_id,\n            &version.data_snapshot,\n            &format!(\"Reverted to version {}\", version.version_number),\n            created_by,\n            Some(version_id),\n        ).await?;\n\n        Ok(new_version_id)\n    }\n\n    /// Get a specific version by ID\n    async fn get_version_by_id(&self, version_id: Uuid) -> Result<data_versions::Model, VersioningError> {\n        data_versions::Entity::find_by_id(version_id)\n            .one(&self.db)\n            .await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?\n            .ok_or_else(|| VersioningError::VersionNotFound(version_id.to_string()))\n    }\n\n    /// Get the next version number for an entity\n    async fn get_next_version_number(\n        &self,\n        txn: &DatabaseTransaction,\n        project_id: Uuid,\n        entity_type: &str,\n        entity_id: Uuid,\n    ) -> Result<i32, VersioningError> {\n        let latest_version = data_versions::Entity::find()\n            .filter(data_versions::Column::ProjectId.eq(project_id))\n            .filter(data_versions::Column::EntityType.eq(entity_type))\n            .filter(data_versions::Column::EntityId.eq(entity_id))\n            .order_by_desc(data_versions::Column::VersionNumber)\n            .one(txn)\n            .await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok(latest_version.map(|v| v.version_number + 1).unwrap_or(1))\n    }\n\n    /// Unset the current version flag for an entity\n    async fn unset_current_version(\n        &self,\n        txn: &DatabaseTransaction,\n        project_id: Uuid,\n        entity_type: &str,\n        entity_id: Uuid,\n    ) -> Result<(), VersioningError> {\n        data_versions::Entity::update_many()\n            .filter(data_versions::Column::ProjectId.eq(project_id))\n            .filter(data_versions::Column::EntityType.eq(entity_type))\n            .filter(data_versions::Column::EntityId.eq(entity_id))\n            .filter(data_versions::Column::IsCurrent.eq(true))\n            .col_expr(data_versions::Column::IsCurrent, Expr::value(false))\n            .exec(txn)\n            .await\n            .map_err(|e| VersioningError::DatabaseError(e.to_string()))?;\n\n        Ok(())\n    }\n\n    /// Calculate changes between two data snapshots\n    async fn calculate_changes(\n        &self,\n        _txn: &DatabaseTransaction,\n        parent_version_id: Uuid,\n        new_data: &Value,\n    ) -> Result<Value, VersioningError> {\n        let parent_version = self.get_version_by_id(parent_version_id).await?;\n        let changes = self.calculate_field_changes(&parent_version.data_snapshot, new_data)?;\n        \n        Ok(json!({\n            \"type\": \"incremental\",\n            \"parent_version\": parent_version_id,\n            \"changes\": changes\n        }))\n    }\n\n    /// Calculate field-level changes between two JSON values\n    fn calculate_field_changes(&self, old_data: &Value, new_data: &Value) -> Result<Vec<FieldChange>, VersioningError> {\n        let mut changes = Vec::new();\n        \n        // This is a simplified implementation - in production, you'd want a more sophisticated diff algorithm\n        if let (Value::Object(old_obj), Value::Object(new_obj)) = (old_data, new_data) {\n            // Check for added and modified fields\n            for (key, new_value) in new_obj {\n                match old_obj.get(key) {\n                    Some(old_value) if old_value != new_value => {\n                        changes.push(FieldChange {\n                            field_path: key.clone(),\n                            change_type: ChangeType::Modified,\n                            old_value: Some(old_value.clone()),\n                            new_value: Some(new_value.clone()),\n                        });\n                    }\n                    None => {\n                        changes.push(FieldChange {\n                            field_path: key.clone(),\n                            change_type: ChangeType::Added,\n                            old_value: None,\n                            new_value: Some(new_value.clone()),\n                        });\n                    }\n                    _ => {} // No change\n                }\n            }\n\n            // Check for deleted fields\n            for (key, old_value) in old_obj {\n                if !new_obj.contains_key(key) {\n                    changes.push(FieldChange {\n                        field_path: key.clone(),\n                        change_type: ChangeType::Deleted,\n                        old_value: Some(old_value.clone()),\n                        new_value: None,\n                    });\n                }\n            }\n        }\n\n        Ok(changes)\n    }\n\n    /// Detect conflicts between two versions\n    async fn detect_conflicts(\n        &self,\n        version1: &data_versions::Model,\n        version2: &data_versions::Model,\n    ) -> Result<Vec<VersionConflict>, VersioningError> {\n        let mut conflicts = Vec::new();\n\n        // Find common ancestor (simplified - assumes direct parent relationship)\n        let base_data = if let Some(parent_id) = version1.parent_version_id {\n            let parent = self.get_version_by_id(parent_id).await?;\n            parent.data_snapshot\n        } else {\n            json!({})\n        };\n\n        let changes1 = self.calculate_field_changes(&base_data, &version1.data_snapshot)?;\n        let changes2 = self.calculate_field_changes(&base_data, &version2.data_snapshot)?;\n\n        // Find conflicting changes\n        for change1 in &changes1 {\n            for change2 in &changes2 {\n                if change1.field_path == change2.field_path {\n                    let conflict_type = match (&change1.change_type, &change2.change_type) {\n                        (ChangeType::Modified, ChangeType::Modified) => ConflictType::ModifyModify,\n                        (ChangeType::Modified, ChangeType::Deleted) => ConflictType::ModifyDelete,\n                        (ChangeType::Deleted, ChangeType::Modified) => ConflictType::DeleteModify,\n                        (ChangeType::Added, ChangeType::Added) => ConflictType::AddAdd,\n                        _ => continue,\n                    };\n\n                    conflicts.push(VersionConflict {\n                        field_path: change1.field_path.clone(),\n                        base_value: change1.old_value.clone().unwrap_or(Value::Null),\n                        current_value: change1.new_value.clone().unwrap_or(Value::Null),\n                        incoming_value: change2.new_value.clone().unwrap_or(Value::Null),\n                        conflict_type,\n                    });\n                }\n            }\n        }\n\n        Ok(conflicts)\n    }\n\n    /// Apply manual conflict resolutions\n    fn apply_manual_resolutions(\n        &self,\n        _base_data: &Value,\n        _source_data: &Value,\n        target_data: &Value,\n        resolutions: Option<&Value>,\n    ) -> Result<Value, VersioningError> {\n        // Simplified implementation - start with target data and apply resolutions\n        let mut result = target_data.clone();\n        \n        if let Some(Value::Object(resolution_map)) = resolutions {\n            if let Value::Object(ref mut result_obj) = result {\n                for (field_path, resolution_value) in resolution_map {\n                    result_obj.insert(field_path.clone(), resolution_value.clone());\n                }\n            }\n        }\n\n        Ok(result)\n    }\n\n    /// Merge data at field level\n    fn merge_at_field_level(\n        &self,\n        _base_data: &Value,\n        source_data: &Value,\n        target_data: &Value,\n    ) -> Result<Value, VersioningError> {\n        // Simplified field-level merge - in production, this would be more sophisticated\n        let mut result = target_data.clone();\n        \n        if let (Value::Object(source_obj), Value::Object(ref mut result_obj)) = (source_data, &mut result) {\n            for (key, source_value) in source_obj {\n                if !result_obj.contains_key(key) {\n                    result_obj.insert(key.clone(), source_value.clone());\n                }\n            }\n        }\n\n        Ok(result)\n    }\n}\n"}