{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/vietnamese_tax.rs"}, "originalCode": "use rust_decimal::Decimal;\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse std::str::FromStr;\nuse anyhow::Result;\nuse thiserror::Error;\n\n/// Vietnamese tax calculation errors\n#[derive(Error, Debug)]\npub enum TaxCalculationError {\n    #[error(\"Invalid land area: {0}\")]\n    InvalidLandArea(String),\n    #[error(\"Invalid land location: {0}\")]\n    InvalidLandLocation(String),\n    #[error(\"Invalid land use type: {0}\")]\n    InvalidLandUseType(String),\n    #[error(\"Invalid investment amount: {0}\")]\n    InvalidInvestmentAmount(String),\n    #[error(\"Tax calculation failed: {0}\")]\n    CalculationFailed(String),\n}\n\n/// Land use types in Vietnam\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum LandUseType {\n    Residential,\n    Commercial,\n    Industrial,\n    Agricultural,\n    Mixed,\n}\n\n/// Land location categories for tax calculation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum LandLocationCategory {\n    UrbanClass1, // Major cities like Ho Chi Minh City, Hanoi\n    UrbanClass2, // Provincial cities\n    UrbanClass3, // District towns\n    Rural,       // Rural areas\n}\n\n/// Vietnamese tax calculation input\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxCalculationInput {\n    pub land_area: Decimal,           // in square meters\n    pub land_value_per_sqm: Decimal,  // VND per square meter\n    pub land_location: LandLocationCategory,\n    pub land_use_type: LandUseType,\n    pub total_investment: Decimal,    // Total project investment in VND\n    pub construction_area: Option<Decimal>, // Built area in square meters\n    pub project_duration_months: u32,\n    pub is_foreign_investor: bool,\n}\n\n/// Vietnamese tax calculation results\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxCalculationResult {\n    pub land_use_fee: Decimal,\n    pub registration_fee: Decimal,\n    pub notarization_fee: Decimal,\n    pub property_transfer_tax: Decimal,\n    pub vat: Decimal,\n    pub rental_income_tax: Option<RentalIncomeTax>,\n    pub total_taxes_and_fees: Decimal,\n    pub breakdown: TaxBreakdown,\n}\n\n/// Rental income tax calculation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RentalIncomeTax {\n    pub annual_rental_income: Decimal,\n    pub tax_free_threshold: Decimal,\n    pub vat_rate: Decimal,\n    pub personal_income_tax_rate: Decimal,\n    pub business_license_tax: Decimal,\n    pub total_rental_tax: Decimal,\n}\n\n/// Detailed tax breakdown\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxBreakdown {\n    pub land_use_fee_details: LandUseFeeDetails,\n    pub registration_fee_details: RegistrationFeeDetails,\n    pub notarization_fee_details: NotarizationFeeDetails,\n    pub vat_details: VatDetails,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct LandUseFeeDetails {\n    pub base_rate: Decimal,\n    pub location_multiplier: Decimal,\n    pub use_type_multiplier: Decimal,\n    pub calculated_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RegistrationFeeDetails {\n    pub base_fee: Decimal,\n    pub area_based_fee: Decimal,\n    pub total_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct NotarizationFeeDetails {\n    pub asset_value: Decimal,\n    pub fee_rate: Decimal,\n    pub calculated_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct VatDetails {\n    pub taxable_amount: Decimal,\n    pub vat_rate: Decimal,\n    pub calculated_vat: Decimal,\n}\n\n/// Vietnamese tax calculator service\npub struct VietnameseTaxCalculator {\n    tax_rates: TaxRateConfig,\n}\n\n/// Tax rate configuration based on Vietnamese law\n#[derive(Debug, Clone)]\npub struct TaxRateConfig {\n    pub land_use_fee_rates: HashMap<(LandLocationCategory, LandUseType), Decimal>,\n    pub registration_fee_base: Decimal,\n    pub notarization_fee_rates: Vec<(Decimal, Decimal)>, // (threshold, rate) pairs\n    pub vat_rate: Decimal,\n    pub property_transfer_tax_rate: Decimal,\n    pub rental_income_tax_free_threshold: Decimal,\n    pub rental_vat_rate: Decimal,\n    pub rental_pit_rate: Decimal,\n}\n\nimpl Default for TaxRateConfig {\n    fn default() -> Self {\n        let mut land_use_fee_rates = HashMap::new();\n        \n        // Land use fee rates based on Vietnamese regulations (VND per sqm per year)\n        // Urban Class 1 (Major cities)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Residential), Decimal::from(150000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Commercial), Decimal::from(300000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Industrial), Decimal::from(200000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Mixed), Decimal::from(250000));\n        \n        // Urban Class 2 (Provincial cities)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Residential), Decimal::from(100000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Commercial), Decimal::from(200000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Industrial), Decimal::from(150000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Mixed), Decimal::from(175000));\n        \n        // Urban Class 3 (District towns)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Residential), Decimal::from(50000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Commercial), Decimal::from(100000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Industrial), Decimal::from(75000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Mixed), Decimal::from(87500));\n        \n        // Rural areas\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Residential), Decimal::from(20000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Commercial), Decimal::from(40000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Industrial), Decimal::from(30000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Agricultural), Decimal::from(5000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Mixed), Decimal::from(35000));\n\n        // Notarization fee rates based on asset value (threshold, rate)\n        let notarization_fee_rates = vec![\n            (Decimal::from(50_000_000), Decimal::from(50_000)),      // Below 50M VND\n            (Decimal::from(100_000_000), Decimal::from(100_000)),    // 50M-100M VND\n            (Decimal::from(1_000_000_000), \"0.001\".parse().unwrap()), // 100M-1B VND: 0.1%\n            (Decimal::from(3_000_000_000), \"0.0006\".parse().unwrap()), // 1B-3B VND: 0.06%\n            (Decimal::from(5_000_000_000), \"0.0005\".parse().unwrap()), // 3B-5B VND: 0.05%\n            (Decimal::from(10_000_000_000), \"0.0004\".parse().unwrap()), // 5B-10B VND: 0.04%\n            (Decimal::from(100_000_000_000), \"0.0003\".parse().unwrap()), // 10B-100B VND: 0.03%\n            (Decimal::MAX, \"0.0002\".parse().unwrap()), // Above 100B VND: 0.02%\n        ];\n\n        Self {\n            land_use_fee_rates,\n            registration_fee_base: Decimal::from(500_000), // Base registration fee\n            notarization_fee_rates,\n            vat_rate: \"0.05\".parse().unwrap(), // 5% VAT\n            property_transfer_tax_rate: \"0.02\".parse().unwrap(), // 2% for individuals\n            rental_income_tax_free_threshold: Decimal::from(100_000_000), // 100M VND per year\n            rental_vat_rate: \"0.05\".parse().unwrap(), // 5% VAT on rental\n            rental_pit_rate: \"0.05\".parse().unwrap(), // 5% PIT on rental\n        }\n    }\n}\n\nimpl VietnameseTaxCalculator {\n    pub fn new() -> Self {\n        Self {\n            tax_rates: TaxRateConfig::default(),\n        }\n    }\n\n    pub fn with_custom_rates(tax_rates: TaxRateConfig) -> Self {\n        Self { tax_rates }\n    }\n\n    /// Calculate all applicable taxes and fees for a Vietnamese real estate project\n    pub fn calculate_taxes(&self, input: &TaxCalculationInput) -> Result<TaxCalculationResult, TaxCalculationError> {\n        // Validate input\n        self.validate_input(input)?;\n\n        // Calculate individual tax components\n        let land_use_fee_details = self.calculate_land_use_fee(input)?;\n        let registration_fee_details = self.calculate_registration_fee(input)?;\n        let notarization_fee_details = self.calculate_notarization_fee(input)?;\n        let vat_details = self.calculate_vat(input)?;\n        let property_transfer_tax = self.calculate_property_transfer_tax(input)?;\n\n        // Calculate rental income tax if applicable\n        let rental_income_tax = if input.land_use_type == LandUseType::Commercial || \n                                   input.land_use_type == LandUseType::Mixed {\n            Some(self.calculate_rental_income_tax(input)?)\n        } else {\n            None\n        };\n\n        let total_taxes_and_fees = land_use_fee_details.calculated_fee +\n                                  registration_fee_details.total_fee +\n                                  notarization_fee_details.calculated_fee +\n                                  vat_details.calculated_vat +\n                                  property_transfer_tax +\n                                  rental_income_tax.as_ref().map(|r| r.total_rental_tax).unwrap_or(Decimal::ZERO);\n\n        Ok(TaxCalculationResult {\n            land_use_fee: land_use_fee_details.calculated_fee,\n            registration_fee: registration_fee_details.total_fee,\n            notarization_fee: notarization_fee_details.calculated_fee,\n            property_transfer_tax,\n            vat: vat_details.calculated_vat,\n            rental_income_tax,\n            total_taxes_and_fees,\n            breakdown: TaxBreakdown {\n                land_use_fee_details,\n                registration_fee_details,\n                notarization_fee_details,\n                vat_details,\n            },\n        })\n    }\n\n    fn validate_input(&self, input: &TaxCalculationInput) -> Result<(), TaxCalculationError> {\n        if input.land_area <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidLandArea(\"Land area must be positive\".to_string()));\n        }\n\n        if input.land_value_per_sqm <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidLandArea(\"Land value per sqm must be positive\".to_string()));\n        }\n\n        if input.total_investment <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidInvestmentAmount(\"Total investment must be positive\".to_string()));\n        }\n\n        Ok(())\n    }\n\n    fn calculate_land_use_fee(&self, input: &TaxCalculationInput) -> Result<LandUseFeeDetails, TaxCalculationError> {\n        let base_rate = self.tax_rates.land_use_fee_rates\n            .get(&(input.land_location.clone(), input.land_use_type.clone()))\n            .ok_or_else(|| TaxCalculationError::CalculationFailed(\n                \"No tax rate found for this land location and use type combination\".to_string()\n            ))?;\n\n        let location_multiplier = match input.land_location {\n            LandLocationCategory::UrbanClass1 => \"1.5\".parse().unwrap(),\n            LandLocationCategory::UrbanClass2 => \"1.2\".parse().unwrap(),\n            LandLocationCategory::UrbanClass3 => \"1.0\".parse().unwrap(),\n            LandLocationCategory::Rural => \"0.8\".parse().unwrap(),\n        };\n\n        let use_type_multiplier = match input.land_use_type {\n            LandUseType::Commercial => \"1.3\".parse().unwrap(),\n            LandUseType::Industrial => \"1.1\".parse().unwrap(),\n            LandUseType::Mixed => \"1.2\".parse().unwrap(),\n            _ => Decimal::ONE,\n        };\n\n        let calculated_fee = base_rate * input.land_area * location_multiplier * use_type_multiplier;\n\n        Ok(LandUseFeeDetails {\n            base_rate: *base_rate,\n            location_multiplier,\n            use_type_multiplier,\n            calculated_fee,\n        })\n    }\n\n    fn calculate_registration_fee(&self, input: &TaxCalculationInput) -> Result<RegistrationFeeDetails, TaxCalculationError> {\n        let base_fee = self.tax_rates.registration_fee_base;\n        let area_based_fee = input.land_area * Decimal::from(1000); // 1000 VND per sqm\n        let total_fee = base_fee + area_based_fee;\n\n        Ok(RegistrationFeeDetails {\n            base_fee,\n            area_based_fee,\n            total_fee,\n        })\n    }\n\n    fn calculate_notarization_fee(&self, input: &TaxCalculationInput) -> Result<NotarizationFeeDetails, TaxCalculationError> {\n        let asset_value = input.land_area * input.land_value_per_sqm;\n        \n        let (fee_rate, calculated_fee) = self.calculate_progressive_fee(&asset_value, &self.tax_rates.notarization_fee_rates);\n\n        Ok(NotarizationFeeDetails {\n            asset_value,\n            fee_rate,\n            calculated_fee,\n        })\n    }\n\n    fn calculate_vat(&self, input: &TaxCalculationInput) -> Result<VatDetails, TaxCalculationError> {\n        let taxable_amount = input.total_investment;\n        let vat_rate = self.tax_rates.vat_rate;\n        let calculated_vat = taxable_amount * vat_rate;\n\n        Ok(VatDetails {\n            taxable_amount,\n            vat_rate,\n            calculated_vat,\n        })\n    }\n\n    fn calculate_property_transfer_tax(&self, input: &TaxCalculationInput) -> Result<Decimal, TaxCalculationError> {\n        let transfer_value = input.land_area * input.land_value_per_sqm;\n        let tax_rate = if input.is_foreign_investor {\n            self.tax_rates.property_transfer_tax_rate * Decimal::from_str(\"1.5\").unwrap() // Higher rate for foreign investors\n        } else {\n            self.tax_rates.property_transfer_tax_rate\n        };\n        \n        Ok(transfer_value * tax_rate)\n    }\n\n    fn calculate_rental_income_tax(&self, input: &TaxCalculationInput) -> Result<RentalIncomeTax, TaxCalculationError> {\n        // Estimate annual rental income based on investment and location\n        let estimated_rental_yield = match input.land_location {\n            LandLocationCategory::UrbanClass1 => Decimal::from_str(\"0.06\").unwrap(), // 6% yield\n            LandLocationCategory::UrbanClass2 => Decimal::from_str(\"0.08\").unwrap(), // 8% yield\n            LandLocationCategory::UrbanClass3 => Decimal::from_str(\"0.10\").unwrap(), // 10% yield\n            LandLocationCategory::Rural => Decimal::from_str(\"0.12\").unwrap(), // 12% yield\n        };\n\n        let annual_rental_income = input.total_investment * estimated_rental_yield;\n        let tax_free_threshold = self.tax_rates.rental_income_tax_free_threshold;\n\n        let taxable_income = if annual_rental_income > tax_free_threshold {\n            annual_rental_income - tax_free_threshold\n        } else {\n            Decimal::ZERO\n        };\n\n        let vat = if annual_rental_income > tax_free_threshold {\n            annual_rental_income * self.tax_rates.rental_vat_rate\n        } else {\n            Decimal::ZERO\n        };\n\n        let personal_income_tax = taxable_income * self.tax_rates.rental_pit_rate;\n\n        let business_license_tax = if annual_rental_income > tax_free_threshold {\n            match annual_rental_income {\n                income if income <= Decimal::from(300_000_000) => Decimal::from(300_000),\n                income if income <= Decimal::from(500_000_000) => Decimal::from(500_000),\n                _ => Decimal::from(1_000_000),\n            }\n        } else {\n            Decimal::ZERO\n        };\n\n        let total_rental_tax = vat + personal_income_tax + business_license_tax;\n\n        Ok(RentalIncomeTax {\n            annual_rental_income,\n            tax_free_threshold,\n            vat_rate: self.tax_rates.rental_vat_rate,\n            personal_income_tax_rate: self.tax_rates.rental_pit_rate,\n            business_license_tax,\n            total_rental_tax,\n        })\n    }\n\n    fn calculate_progressive_fee(&self, amount: &Decimal, rate_table: &[(Decimal, Decimal)]) -> (Decimal, Decimal) {\n        for (threshold, rate) in rate_table {\n            if amount <= threshold {\n                if *rate < Decimal::ONE {\n                    // Percentage rate\n                    return (*rate, amount * rate);\n                } else {\n                    // Fixed amount\n                    return (*rate, *rate);\n                }\n            }\n        }\n        \n        // Fallback to last rate\n        let last_rate = rate_table.last().unwrap().1;\n        (last_rate, amount * last_rate)\n    }\n}\n\nimpl Default for VietnameseTaxCalculator {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n// Helper functions for parsing location and use type from strings\nimpl LandLocationCategory {\n    pub fn from_string(location: &str) -> Result<Self, TaxCalculationError> {\n        match location.to_lowercase().as_str() {\n            \"urban_class_1\" | \"major_city\" | \"ho_chi_minh\" | \"hanoi\" => Ok(LandLocationCategory::UrbanClass1),\n            \"urban_class_2\" | \"provincial_city\" => Ok(LandLocationCategory::UrbanClass2),\n            \"urban_class_3\" | \"district_town\" => Ok(LandLocationCategory::UrbanClass3),\n            \"rural\" | \"countryside\" => Ok(LandLocationCategory::Rural),\n            _ => Err(TaxCalculationError::InvalidLandLocation(format!(\"Unknown location: {}\", location))),\n        }\n    }\n}\n\nimpl LandUseType {\n    pub fn from_string(use_type: &str) -> Result<Self, TaxCalculationError> {\n        match use_type.to_lowercase().as_str() {\n            \"residential\" | \"housing\" => Ok(LandUseType::Residential),\n            \"commercial\" | \"business\" => Ok(LandUseType::Commercial),\n            \"industrial\" | \"factory\" => Ok(LandUseType::Industrial),\n            \"agricultural\" | \"farming\" => Ok(LandUseType::Agricultural),\n            \"mixed\" | \"mixed_use\" => Ok(LandUseType::Mixed),\n            _ => Err(TaxCalculationError::InvalidLandUseType(format!(\"Unknown use type: {}\", use_type))),\n        }\n    }\n}\n", "modifiedCode": "use rust_decimal::Decimal;\nuse serde::{Deserialize, Serialize};\nuse std::collections::HashMap;\nuse std::str::FromStr;\nuse anyhow::Result;\nuse thiserror::Error;\n\n/// Vietnamese tax calculation errors\n#[derive(Error, Debug)]\npub enum TaxCalculationError {\n    #[error(\"Invalid land area: {0}\")]\n    InvalidLandArea(String),\n    #[error(\"Invalid land location: {0}\")]\n    InvalidLandLocation(String),\n    #[error(\"Invalid land use type: {0}\")]\n    InvalidLandUseType(String),\n    #[error(\"Invalid investment amount: {0}\")]\n    InvalidInvestmentAmount(String),\n    #[error(\"Tax calculation failed: {0}\")]\n    CalculationFailed(String),\n}\n\n/// Land use types in Vietnam\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum LandUseType {\n    Residential,\n    Commercial,\n    Industrial,\n    Agricultural,\n    Mixed,\n}\n\n/// Land location categories for tax calculation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub enum LandLocationCategory {\n    UrbanClass1, // Major cities like Ho Chi Minh City, Hanoi\n    UrbanClass2, // Provincial cities\n    UrbanClass3, // District towns\n    Rural,       // Rural areas\n}\n\n/// Vietnamese tax calculation input\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxCalculationInput {\n    pub land_area: Decimal,           // in square meters\n    pub land_value_per_sqm: Decimal,  // VND per square meter\n    pub land_location: LandLocationCategory,\n    pub land_use_type: LandUseType,\n    pub total_investment: Decimal,    // Total project investment in VND\n    pub construction_area: Option<Decimal>, // Built area in square meters\n    pub project_duration_months: u32,\n    pub is_foreign_investor: bool,\n}\n\n/// Vietnamese tax calculation results\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxCalculationResult {\n    pub land_use_fee: Decimal,\n    pub registration_fee: Decimal,\n    pub notarization_fee: Decimal,\n    pub property_transfer_tax: Decimal,\n    pub vat: Decimal,\n    pub rental_income_tax: Option<RentalIncomeTax>,\n    pub total_taxes_and_fees: Decimal,\n    pub breakdown: TaxBreakdown,\n}\n\n/// Rental income tax calculation\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RentalIncomeTax {\n    pub annual_rental_income: Decimal,\n    pub tax_free_threshold: Decimal,\n    pub vat_rate: Decimal,\n    pub personal_income_tax_rate: Decimal,\n    pub business_license_tax: Decimal,\n    pub total_rental_tax: Decimal,\n}\n\n/// Detailed tax breakdown\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct TaxBreakdown {\n    pub land_use_fee_details: LandUseFeeDetails,\n    pub registration_fee_details: RegistrationFeeDetails,\n    pub notarization_fee_details: NotarizationFeeDetails,\n    pub vat_details: VatDetails,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct LandUseFeeDetails {\n    pub base_rate: Decimal,\n    pub location_multiplier: Decimal,\n    pub use_type_multiplier: Decimal,\n    pub calculated_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct RegistrationFeeDetails {\n    pub base_fee: Decimal,\n    pub area_based_fee: Decimal,\n    pub total_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct NotarizationFeeDetails {\n    pub asset_value: Decimal,\n    pub fee_rate: Decimal,\n    pub calculated_fee: Decimal,\n}\n\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct VatDetails {\n    pub taxable_amount: Decimal,\n    pub vat_rate: Decimal,\n    pub calculated_vat: Decimal,\n}\n\n/// Vietnamese tax calculator service\npub struct VietnameseTaxCalculator {\n    tax_rates: TaxRateConfig,\n}\n\n/// Tax rate configuration based on Vietnamese law\n#[derive(Debug, Clone)]\npub struct TaxRateConfig {\n    pub land_use_fee_rates: HashMap<(LandLocationCategory, LandUseType), Decimal>,\n    pub registration_fee_base: Decimal,\n    pub notarization_fee_rates: Vec<(Decimal, Decimal)>, // (threshold, rate) pairs\n    pub vat_rate: Decimal,\n    pub property_transfer_tax_rate: Decimal,\n    pub rental_income_tax_free_threshold: Decimal,\n    pub rental_vat_rate: Decimal,\n    pub rental_pit_rate: Decimal,\n}\n\nimpl Default for TaxRateConfig {\n    fn default() -> Self {\n        let mut land_use_fee_rates = HashMap::new();\n        \n        // Land use fee rates based on Vietnamese regulations (VND per sqm per year)\n        // Urban Class 1 (Major cities)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Residential), Decimal::from(150000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Commercial), Decimal::from(300000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Industrial), Decimal::from(200000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Mixed), Decimal::from(250000));\n        \n        // Urban Class 2 (Provincial cities)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Residential), Decimal::from(100000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Commercial), Decimal::from(200000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Industrial), Decimal::from(150000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Mixed), Decimal::from(175000));\n        \n        // Urban Class 3 (District towns)\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Residential), Decimal::from(50000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Commercial), Decimal::from(100000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Industrial), Decimal::from(75000));\n        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Mixed), Decimal::from(87500));\n        \n        // Rural areas\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Residential), Decimal::from(20000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Commercial), Decimal::from(40000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Industrial), Decimal::from(30000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Agricultural), Decimal::from(5000));\n        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Mixed), Decimal::from(35000));\n\n        // Notarization fee rates based on asset value (threshold, rate)\n        let notarization_fee_rates = vec![\n            (Decimal::from(50_000_000), Decimal::from(50_000)),      // Below 50M VND\n            (Decimal::from(100_000_000), Decimal::from(100_000)),    // 50M-100M VND\n            (Decimal::from(1_000_000_000), \"0.001\".parse().unwrap()), // 100M-1B VND: 0.1%\n            (Decimal::from(3_000_000_000), \"0.0006\".parse().unwrap()), // 1B-3B VND: 0.06%\n            (Decimal::from(5_000_000_000), \"0.0005\".parse().unwrap()), // 3B-5B VND: 0.05%\n            (Decimal::from(10_000_000_000), \"0.0004\".parse().unwrap()), // 5B-10B VND: 0.04%\n            (Decimal::from(100_000_000_000), \"0.0003\".parse().unwrap()), // 10B-100B VND: 0.03%\n            (Decimal::MAX, \"0.0002\".parse().unwrap()), // Above 100B VND: 0.02%\n        ];\n\n        Self {\n            land_use_fee_rates,\n            registration_fee_base: Decimal::from(500_000), // Base registration fee\n            notarization_fee_rates,\n            vat_rate: \"0.05\".parse().unwrap(), // 5% VAT\n            property_transfer_tax_rate: \"0.02\".parse().unwrap(), // 2% for individuals\n            rental_income_tax_free_threshold: Decimal::from(100_000_000), // 100M VND per year\n            rental_vat_rate: \"0.05\".parse().unwrap(), // 5% VAT on rental\n            rental_pit_rate: \"0.05\".parse().unwrap(), // 5% PIT on rental\n        }\n    }\n}\n\nimpl VietnameseTaxCalculator {\n    pub fn new() -> Self {\n        Self {\n            tax_rates: TaxRateConfig::default(),\n        }\n    }\n\n    pub fn with_custom_rates(tax_rates: TaxRateConfig) -> Self {\n        Self { tax_rates }\n    }\n\n    /// Calculate all applicable taxes and fees for a Vietnamese real estate project\n    pub fn calculate_taxes(&self, input: &TaxCalculationInput) -> Result<TaxCalculationResult, TaxCalculationError> {\n        // Validate input\n        self.validate_input(input)?;\n\n        // Calculate individual tax components\n        let land_use_fee_details = self.calculate_land_use_fee(input)?;\n        let registration_fee_details = self.calculate_registration_fee(input)?;\n        let notarization_fee_details = self.calculate_notarization_fee(input)?;\n        let vat_details = self.calculate_vat(input)?;\n        let property_transfer_tax = self.calculate_property_transfer_tax(input)?;\n\n        // Calculate rental income tax if applicable\n        let rental_income_tax = if input.land_use_type == LandUseType::Commercial || \n                                   input.land_use_type == LandUseType::Mixed {\n            Some(self.calculate_rental_income_tax(input)?)\n        } else {\n            None\n        };\n\n        let total_taxes_and_fees = land_use_fee_details.calculated_fee +\n                                  registration_fee_details.total_fee +\n                                  notarization_fee_details.calculated_fee +\n                                  vat_details.calculated_vat +\n                                  property_transfer_tax +\n                                  rental_income_tax.as_ref().map(|r| r.total_rental_tax).unwrap_or(Decimal::ZERO);\n\n        Ok(TaxCalculationResult {\n            land_use_fee: land_use_fee_details.calculated_fee,\n            registration_fee: registration_fee_details.total_fee,\n            notarization_fee: notarization_fee_details.calculated_fee,\n            property_transfer_tax,\n            vat: vat_details.calculated_vat,\n            rental_income_tax,\n            total_taxes_and_fees,\n            breakdown: TaxBreakdown {\n                land_use_fee_details,\n                registration_fee_details,\n                notarization_fee_details,\n                vat_details,\n            },\n        })\n    }\n\n    fn validate_input(&self, input: &TaxCalculationInput) -> Result<(), TaxCalculationError> {\n        if input.land_area <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidLandArea(\"Land area must be positive\".to_string()));\n        }\n\n        if input.land_value_per_sqm <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidLandArea(\"Land value per sqm must be positive\".to_string()));\n        }\n\n        if input.total_investment <= Decimal::ZERO {\n            return Err(TaxCalculationError::InvalidInvestmentAmount(\"Total investment must be positive\".to_string()));\n        }\n\n        Ok(())\n    }\n\n    fn calculate_land_use_fee(&self, input: &TaxCalculationInput) -> Result<LandUseFeeDetails, TaxCalculationError> {\n        let base_rate = self.tax_rates.land_use_fee_rates\n            .get(&(input.land_location.clone(), input.land_use_type.clone()))\n            .ok_or_else(|| TaxCalculationError::CalculationFailed(\n                \"No tax rate found for this land location and use type combination\".to_string()\n            ))?;\n\n        let location_multiplier = match input.land_location {\n            LandLocationCategory::UrbanClass1 => \"1.5\".parse().unwrap(),\n            LandLocationCategory::UrbanClass2 => \"1.2\".parse().unwrap(),\n            LandLocationCategory::UrbanClass3 => \"1.0\".parse().unwrap(),\n            LandLocationCategory::Rural => \"0.8\".parse().unwrap(),\n        };\n\n        let use_type_multiplier = match input.land_use_type {\n            LandUseType::Commercial => \"1.3\".parse().unwrap(),\n            LandUseType::Industrial => \"1.1\".parse().unwrap(),\n            LandUseType::Mixed => \"1.2\".parse().unwrap(),\n            _ => Decimal::ONE,\n        };\n\n        let calculated_fee = base_rate * input.land_area * location_multiplier * use_type_multiplier;\n\n        Ok(LandUseFeeDetails {\n            base_rate: *base_rate,\n            location_multiplier,\n            use_type_multiplier,\n            calculated_fee,\n        })\n    }\n\n    fn calculate_registration_fee(&self, input: &TaxCalculationInput) -> Result<RegistrationFeeDetails, TaxCalculationError> {\n        let base_fee = self.tax_rates.registration_fee_base;\n        let area_based_fee = input.land_area * Decimal::from(1000); // 1000 VND per sqm\n        let total_fee = base_fee + area_based_fee;\n\n        Ok(RegistrationFeeDetails {\n            base_fee,\n            area_based_fee,\n            total_fee,\n        })\n    }\n\n    fn calculate_notarization_fee(&self, input: &TaxCalculationInput) -> Result<NotarizationFeeDetails, TaxCalculationError> {\n        let asset_value = input.land_area * input.land_value_per_sqm;\n        \n        let (fee_rate, calculated_fee) = self.calculate_progressive_fee(&asset_value, &self.tax_rates.notarization_fee_rates);\n\n        Ok(NotarizationFeeDetails {\n            asset_value,\n            fee_rate,\n            calculated_fee,\n        })\n    }\n\n    fn calculate_vat(&self, input: &TaxCalculationInput) -> Result<VatDetails, TaxCalculationError> {\n        let taxable_amount = input.total_investment;\n        let vat_rate = self.tax_rates.vat_rate;\n        let calculated_vat = taxable_amount * vat_rate;\n\n        Ok(VatDetails {\n            taxable_amount,\n            vat_rate,\n            calculated_vat,\n        })\n    }\n\n    fn calculate_property_transfer_tax(&self, input: &TaxCalculationInput) -> Result<Decimal, TaxCalculationError> {\n        let transfer_value = input.land_area * input.land_value_per_sqm;\n        let tax_rate = if input.is_foreign_investor {\n            self.tax_rates.property_transfer_tax_rate * \"1.5\".parse::<Decimal>().unwrap() // Higher rate for foreign investors\n        } else {\n            self.tax_rates.property_transfer_tax_rate\n        };\n        \n        Ok(transfer_value * tax_rate)\n    }\n\n    fn calculate_rental_income_tax(&self, input: &TaxCalculationInput) -> Result<RentalIncomeTax, TaxCalculationError> {\n        // Estimate annual rental income based on investment and location\n        let estimated_rental_yield = match input.land_location {\n            LandLocationCategory::UrbanClass1 => Decimal::from_str(\"0.06\").unwrap(), // 6% yield\n            LandLocationCategory::UrbanClass2 => Decimal::from_str(\"0.08\").unwrap(), // 8% yield\n            LandLocationCategory::UrbanClass3 => Decimal::from_str(\"0.10\").unwrap(), // 10% yield\n            LandLocationCategory::Rural => Decimal::from_str(\"0.12\").unwrap(), // 12% yield\n        };\n\n        let annual_rental_income = input.total_investment * estimated_rental_yield;\n        let tax_free_threshold = self.tax_rates.rental_income_tax_free_threshold;\n\n        let taxable_income = if annual_rental_income > tax_free_threshold {\n            annual_rental_income - tax_free_threshold\n        } else {\n            Decimal::ZERO\n        };\n\n        let vat = if annual_rental_income > tax_free_threshold {\n            annual_rental_income * self.tax_rates.rental_vat_rate\n        } else {\n            Decimal::ZERO\n        };\n\n        let personal_income_tax = taxable_income * self.tax_rates.rental_pit_rate;\n\n        let business_license_tax = if annual_rental_income > tax_free_threshold {\n            match annual_rental_income {\n                income if income <= Decimal::from(300_000_000) => Decimal::from(300_000),\n                income if income <= Decimal::from(500_000_000) => Decimal::from(500_000),\n                _ => Decimal::from(1_000_000),\n            }\n        } else {\n            Decimal::ZERO\n        };\n\n        let total_rental_tax = vat + personal_income_tax + business_license_tax;\n\n        Ok(RentalIncomeTax {\n            annual_rental_income,\n            tax_free_threshold,\n            vat_rate: self.tax_rates.rental_vat_rate,\n            personal_income_tax_rate: self.tax_rates.rental_pit_rate,\n            business_license_tax,\n            total_rental_tax,\n        })\n    }\n\n    fn calculate_progressive_fee(&self, amount: &Decimal, rate_table: &[(Decimal, Decimal)]) -> (Decimal, Decimal) {\n        for (threshold, rate) in rate_table {\n            if amount <= threshold {\n                if *rate < Decimal::ONE {\n                    // Percentage rate\n                    return (*rate, amount * rate);\n                } else {\n                    // Fixed amount\n                    return (*rate, *rate);\n                }\n            }\n        }\n        \n        // Fallback to last rate\n        let last_rate = rate_table.last().unwrap().1;\n        (last_rate, amount * last_rate)\n    }\n}\n\nimpl Default for VietnameseTaxCalculator {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n// Helper functions for parsing location and use type from strings\nimpl LandLocationCategory {\n    pub fn from_string(location: &str) -> Result<Self, TaxCalculationError> {\n        match location.to_lowercase().as_str() {\n            \"urban_class_1\" | \"major_city\" | \"ho_chi_minh\" | \"hanoi\" => Ok(LandLocationCategory::UrbanClass1),\n            \"urban_class_2\" | \"provincial_city\" => Ok(LandLocationCategory::UrbanClass2),\n            \"urban_class_3\" | \"district_town\" => Ok(LandLocationCategory::UrbanClass3),\n            \"rural\" | \"countryside\" => Ok(LandLocationCategory::Rural),\n            _ => Err(TaxCalculationError::InvalidLandLocation(format!(\"Unknown location: {}\", location))),\n        }\n    }\n}\n\nimpl LandUseType {\n    pub fn from_string(use_type: &str) -> Result<Self, TaxCalculationError> {\n        match use_type.to_lowercase().as_str() {\n            \"residential\" | \"housing\" => Ok(LandUseType::Residential),\n            \"commercial\" | \"business\" => Ok(LandUseType::Commercial),\n            \"industrial\" | \"factory\" => Ok(LandUseType::Industrial),\n            \"agricultural\" | \"farming\" => Ok(LandUseType::Agricultural),\n            \"mixed\" | \"mixed_use\" => Ok(LandUseType::Mixed),\n            _ => Err(TaxCalculationError::InvalidLandUseType(format!(\"Unknown use type: {}\", use_type))),\n        }\n    }\n}\n"}