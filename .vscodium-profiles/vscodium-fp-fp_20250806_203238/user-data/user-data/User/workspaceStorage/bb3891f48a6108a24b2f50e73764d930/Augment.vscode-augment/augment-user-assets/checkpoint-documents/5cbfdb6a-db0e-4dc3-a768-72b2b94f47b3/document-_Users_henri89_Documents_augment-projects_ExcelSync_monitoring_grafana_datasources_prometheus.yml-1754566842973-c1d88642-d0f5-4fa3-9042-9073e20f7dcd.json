{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/datasources/prometheus.yml"}, "modifiedCode": "apiVersion: 1\n\ndatasources:\n  - name: Prometheus\n    type: prometheus\n    access: proxy\n    url: http://prometheus:9090\n    isDefault: true\n    editable: true\n    jsonData:\n      httpMethod: POST\n      queryTimeout: 60s\n      timeInterval: 15s\n    \n  - name: Loki\n    type: loki\n    access: proxy\n    url: http://loki:3100\n    editable: true\n    jsonData:\n      maxLines: 1000\n      \n  - name: Alertmanager\n    type: alertmanager\n    access: proxy\n    url: http://alertmanager:9093\n    editable: true\n    jsonData:\n      implementation: prometheus\n"}