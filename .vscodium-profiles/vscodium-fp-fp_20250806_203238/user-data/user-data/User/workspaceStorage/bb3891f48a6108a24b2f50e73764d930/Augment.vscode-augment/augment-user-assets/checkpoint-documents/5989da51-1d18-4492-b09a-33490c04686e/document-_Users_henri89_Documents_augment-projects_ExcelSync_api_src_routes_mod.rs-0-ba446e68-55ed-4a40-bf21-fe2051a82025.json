{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/mod.rs"}, "originalCode": "pub mod health;\npub mod auth;\npub mod users;\npub mod projects;\npub mod templates;\n\npub use health::*;\npub use auth::*;\npub use users::*;\npub use projects::*;\npub use templates::*;\n\nuse axum::{middleware, routing::get, Router};\nuse crate::{middleware::auth_middleware, AppState};\n\n/// Create all application routes\npub fn create_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/health\", get(health_check))\n        .nest(\"/api/v1/auth\", auth_routes())\n        .nest(\"/api/v1/users\", user_routes())\n        .nest(\"/api/v1/projects\", project_routes())\n        .nest(\"/api/v1/templates\", template_routes())\n}\n\n\n", "modifiedCode": "pub mod health;\npub mod auth;\npub mod users;\npub mod projects;\npub mod templates;\n\npub use health::*;\npub use auth::*;\npub use users::*;\npub use projects::*;\npub use templates::*;\n\nuse axum::{middleware, routing::get, Router};\nuse crate::{middleware::auth_middleware, AppState};\n\n/// Create all application routes\npub fn create_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/health\", get(health_check))\n        .nest(\"/api/v1/auth\", auth_routes())\n        .nest(\"/api/v1/users\", user_routes())\n        .nest(\"/api/v1/projects\", project_routes())\n        .nest(\"/api/v1/templates\", template_routes())\n}\n\n\n"}