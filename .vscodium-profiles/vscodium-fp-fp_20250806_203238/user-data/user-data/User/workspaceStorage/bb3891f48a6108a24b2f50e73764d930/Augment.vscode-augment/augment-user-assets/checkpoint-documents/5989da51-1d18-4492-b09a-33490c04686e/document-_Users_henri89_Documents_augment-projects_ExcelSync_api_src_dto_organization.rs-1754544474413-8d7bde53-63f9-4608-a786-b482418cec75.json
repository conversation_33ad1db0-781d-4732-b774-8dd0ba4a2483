{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/organization.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// Create organization request DTO\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct CreateOrganizationRequest {\n    #[validate(length(min = 1, max = 255))]\n    pub name: String,\n\n    #[validate(length(max = 1000))]\n    pub description: Option<String>,\n\n    #[validate(length(max = 255))]\n    pub address: Option<String>,\n\n    #[validate(length(max = 20))]\n    pub phone: Option<String>,\n\n    #[validate(email)]\n    pub email: Option<String>,\n\n    #[validate(url)]\n    pub website: Option<String>,\n\n    pub settings: Option<serde_json::Value>,\n}\n\n/// Update organization request DTO\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct UpdateOrganizationRequest {\n    #[validate(length(min = 1, max = 255))]\n    pub name: Option<String>,\n\n    #[validate(length(max = 1000))]\n    pub description: Option<String>,\n\n    #[validate(length(max = 255))]\n    pub address: Option<String>,\n\n    #[validate(length(max = 20))]\n    pub phone: Option<String>,\n\n    #[validate(email)]\n    pub email: Option<String>,\n\n    #[validate(url)]\n    pub website: Option<String>,\n\n    pub settings: Option<serde_json::Value>,\n\n    pub is_active: Option<bool>,\n}\n\n/// Organization response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct OrganizationResponse {\n    pub id: String,\n    pub name: String,\n    pub description: Option<String>,\n    pub address: Option<String>,\n    pub phone: Option<String>,\n    pub email: Option<String>,\n    pub website: Option<String>,\n    pub settings: Option<serde_json::Value>,\n    pub is_active: bool,\n    pub created_at: String,\n    pub updated_at: String,\n}\n\n/// Organization list response with pagination\n#[derive(Debug, Serialize, Deserialize)]\npub struct OrganizationListResponse {\n    pub organizations: Vec<OrganizationResponse>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n}\n"}