{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/mod.rs"}, "originalCode": "pub mod health;\npub mod auth;\npub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod validation;\npub mod financial;\npub mod versioning;\npub mod audit;\npub mod websocket;\npub mod files;\npub mod reports;\npub mod backup;\n\npub use health::*;\npub use auth::*;\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use validation::*;\npub use financial::*;\npub use versioning::*;\npub use audit::*;\npub use websocket::*;\npub use files::*;\npub use reports::*;\npub use backup::*;\n\nuse axum::{middleware, routing::get, Router};\nuse crate::{middleware::auth_middleware, AppState};\n\n/// Create all application routes\npub fn create_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/health\", get(health_check))\n        .nest(\"/api/v1/auth\", auth_routes())\n        .nest(\"/api/v1/users\", user_routes())\n        .nest(\"/api/v1/organizations\", organization_routes())\n        .nest(\"/api/v1/projects\", project_routes())\n        .nest(\"/api/v1/templates\", template_routes())\n        .nest(\"/api/v1/validation\", validation_routes())\n        .nest(\"/api/v1/financial\", financial_routes())\n        .nest(\"/api/v1/versioning\", versioning_routes())\n        .nest(\"/api/v1/audit\", audit_routes())\n        .nest(\"/api/v1/realtime\", websocket_routes())\n        .nest(\"/api/v1/files\", file_routes())\n        .nest(\"/api/v1/reports\", report_routes())\n}\n\n\n", "modifiedCode": "pub mod health;\npub mod auth;\npub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod validation;\npub mod financial;\npub mod versioning;\npub mod audit;\npub mod websocket;\npub mod files;\npub mod reports;\npub mod backup;\n\npub use health::*;\npub use auth::*;\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use validation::*;\npub use financial::*;\npub use versioning::*;\npub use audit::*;\npub use websocket::*;\npub use files::*;\npub use reports::*;\npub use backup::*;\n\nuse axum::{middleware, routing::get, Router};\nuse crate::{middleware::auth_middleware, AppState};\n\n/// Create all application routes\npub fn create_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/health\", get(health_check))\n        .nest(\"/api/v1/auth\", auth_routes())\n        .nest(\"/api/v1/users\", user_routes())\n        .nest(\"/api/v1/organizations\", organization_routes())\n        .nest(\"/api/v1/projects\", project_routes())\n        .nest(\"/api/v1/templates\", template_routes())\n        .nest(\"/api/v1/validation\", validation_routes())\n        .nest(\"/api/v1/financial\", financial_routes())\n        .nest(\"/api/v1/versioning\", versioning_routes())\n        .nest(\"/api/v1/audit\", audit_routes())\n        .nest(\"/api/v1/realtime\", websocket_routes())\n        .nest(\"/api/v1/files\", file_routes())\n        .nest(\"/api/v1/reports\", report_routes())\n        .nest(\"/api/v1/backups\", backup_routes())\n}\n\n\n"}