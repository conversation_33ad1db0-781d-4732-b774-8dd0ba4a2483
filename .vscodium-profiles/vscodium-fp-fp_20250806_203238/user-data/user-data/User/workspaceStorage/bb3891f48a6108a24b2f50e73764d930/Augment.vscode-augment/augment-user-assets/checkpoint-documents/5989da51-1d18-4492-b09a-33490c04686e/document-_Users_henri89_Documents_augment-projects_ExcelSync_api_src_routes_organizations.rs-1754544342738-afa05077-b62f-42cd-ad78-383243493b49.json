{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/organizations.rs"}, "modifiedCode": "use axum::{\n    extract::{Path, Query, Request, State},\n    response::Json,\n    routing::{get, post, put, delete},\n    Router,\n};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::OrganizationService,\n    AppState,\n};\n\n/// Query parameters for organization listing\n#[derive(Debug, Deserialize, Validate)]\npub struct OrganizationListQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n\n    pub is_active: Option<bool>,\n}\n\n/// Organization management routes\npub fn organization_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_organizations).post(create_organization))\n        .route(\"/:id\", get(get_organization).put(update_organization).delete(delete_organization))\n        .route(\"/:id/users\", get(list_organization_users))\n        .route(\"/:id/projects\", get(list_organization_projects))\n}\n\n/// List organizations endpoint\npub async fn list_organizations(\n    State(state): State<AppState>,\n    Query(query): Query<OrganizationListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Validate query parameters\n    query.validate().map_err(|e| ApiError::BadRequest(format!(\"Invalid query parameters: {}\", e)))?;\n\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Only admins can list all organizations\n    if claims.role != \"admin\" {\n        return Err(ApiError::Forbidden(\"Insufficient permissions\".to_string()));\n    }\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let organizations = organization_service\n        .list_organizations(\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n            query.is_active,\n        )\n        .await?;\n\n    Ok(json_response(\"Organizations retrieved successfully\", organizations))\n}\n\n/// Get organization endpoint\npub async fn get_organization(\n    State(state): State<AppState>,\n    Path(organization_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let organization = organization_service.get_organization(organization_id, &claims).await?;\n\n    Ok(json_response(\"Organization retrieved successfully\", organization))\n}\n\n/// Create organization endpoint\npub async fn create_organization(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Only admins can create organizations\n    if claims.role != \"admin\" {\n        return Err(ApiError::Forbidden(\"Insufficient permissions\".to_string()));\n    }\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: crate::dto::organization::CreateOrganizationRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let organization = organization_service.create_organization(create_request, &claims).await?;\n\n    Ok(json_response(\"Organization created successfully\", organization))\n}\n\n/// Update organization endpoint\npub async fn update_organization(\n    State(state): State<AppState>,\n    Path(organization_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: crate::dto::organization::UpdateOrganizationRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let organization = organization_service.update_organization(organization_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Organization updated successfully\", organization))\n}\n\n/// Delete organization endpoint\npub async fn delete_organization(\n    State(state): State<AppState>,\n    Path(organization_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Only admins can delete organizations\n    if claims.role != \"admin\" {\n        return Err(ApiError::Forbidden(\"Insufficient permissions\".to_string()));\n    }\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    organization_service.delete_organization(organization_id, &claims).await?;\n\n    Ok(json_response(\"Organization deleted successfully\", ()))\n}\n\n/// List organization users endpoint\npub async fn list_organization_users(\n    State(state): State<AppState>,\n    Path(organization_id): Path<Uuid>,\n    Query(query): Query<OrganizationListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let users = organization_service\n        .list_organization_users(\n            organization_id,\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Organization users retrieved successfully\", users))\n}\n\n/// List organization projects endpoint\npub async fn list_organization_projects(\n    State(state): State<AppState>,\n    Path(organization_id): Path<Uuid>,\n    Query(query): Query<OrganizationListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let organization_service = OrganizationService::new(state.db.clone());\n    let projects = organization_service\n        .list_organization_projects(\n            organization_id,\n            query.page.unwrap_or(1),\n            query.per_page.unwrap_or(20),\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Organization projects retrieved successfully\", projects))\n}\n"}