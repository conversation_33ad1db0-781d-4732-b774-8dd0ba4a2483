{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/alertmanager/alertmanager.yml"}, "modifiedCode": "# Alertmanager configuration for ExcelSync\nglobal:\n  smtp_smarthost: 'localhost:587'\n  smtp_from: '<EMAIL>'\n  smtp_auth_username: '<EMAIL>'\n  smtp_auth_password: 'your-email-password'\n\n# Route configuration\nroute:\n  group_by: ['alertname']\n  group_wait: 10s\n  group_interval: 10s\n  repeat_interval: 1h\n  receiver: 'web.hook'\n  routes:\n    - match:\n        severity: critical\n      receiver: 'critical-alerts'\n      group_wait: 5s\n      repeat_interval: 30m\n    - match:\n        severity: warning\n      receiver: 'warning-alerts'\n      repeat_interval: 2h\n    - match:\n        severity: info\n      receiver: 'info-alerts'\n      repeat_interval: 24h\n\n# Receivers configuration\nreceivers:\n  - name: 'web.hook'\n    webhook_configs:\n      - url: 'http://localhost:5001/webhook'\n        send_resolved: true\n\n  - name: 'critical-alerts'\n    email_configs:\n      - to: '<EMAIL>'\n        subject: '[CRITICAL] ExcelSync Alert: {{ .GroupLabels.alertname }}'\n        body: |\n          {{ range .Alerts }}\n          Alert: {{ .Annotations.summary }}\n          Description: {{ .Annotations.description }}\n          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}\n          {{ end }}\n        headers:\n          Priority: 'high'\n    slack_configs:\n      - api_url: 'YOUR_SLACK_WEBHOOK_URL'\n        channel: '#alerts-critical'\n        title: 'Critical Alert: {{ .GroupLabels.alertname }}'\n        text: |\n          {{ range .Alerts }}\n          *Alert:* {{ .Annotations.summary }}\n          *Description:* {{ .Annotations.description }}\n          *Labels:* {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}\n          {{ end }}\n        color: 'danger'\n        send_resolved: true\n\n  - name: 'warning-alerts'\n    email_configs:\n      - to: '<EMAIL>'\n        subject: '[WARNING] ExcelSync Alert: {{ .GroupLabels.alertname }}'\n        body: |\n          {{ range .Alerts }}\n          Alert: {{ .Annotations.summary }}\n          Description: {{ .Annotations.description }}\n          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}\n          {{ end }}\n    slack_configs:\n      - api_url: 'YOUR_SLACK_WEBHOOK_URL'\n        channel: '#alerts-warning'\n        title: 'Warning Alert: {{ .GroupLabels.alertname }}'\n        text: |\n          {{ range .Alerts }}\n          *Alert:* {{ .Annotations.summary }}\n          *Description:* {{ .Annotations.description }}\n          {{ end }}\n        color: 'warning'\n        send_resolved: true\n\n  - name: 'info-alerts'\n    email_configs:\n      - to: '<EMAIL>'\n        subject: '[INFO] ExcelSync Alert: {{ .GroupLabels.alertname }}'\n        body: |\n          {{ range .Alerts }}\n          Alert: {{ .Annotations.summary }}\n          Description: {{ .Annotations.description }}\n          {{ end }}\n\n# Inhibit rules\ninhibit_rules:\n  - source_match:\n      severity: 'critical'\n    target_match:\n      severity: 'warning'\n    equal: ['alertname', 'dev', 'instance']\n\n  - source_match:\n      severity: 'warning'\n    target_match:\n      severity: 'info'\n    equal: ['alertname', 'dev', 'instance']\n"}