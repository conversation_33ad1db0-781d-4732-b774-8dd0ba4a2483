{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/password.rs"}, "modifiedCode": "use anyhow::Result;\nuse argon2::{\n    password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},\n    Argon2,\n};\n\nuse crate::AuthError;\n\n/// Password service for hashing and verification using Argon2id\n#[derive(Clone)]\npub struct PasswordService {\n    argon2: Argon2<'static>,\n}\n\nimpl PasswordService {\n    /// Create new password service with default Argon2id configuration\n    pub fn new() -> Self {\n        Self {\n            argon2: Argon2::default(),\n        }\n    }\n\n    /// Hash a password using Argon2id with random salt\n    pub fn hash_password(&self, password: &str) -> Result<String, AuthError> {\n        // Generate a random salt\n        let salt = SaltString::generate(&mut OsRng);\n        \n        // Hash the password\n        let password_hash = self\n            .argon2\n            .hash_password(password.as_bytes(), &salt)\n            .map_err(|_| AuthError::HashingFailed)?;\n        \n        Ok(password_hash.to_string())\n    }\n\n    /// Verify a password against its hash\n    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {\n        // Parse the stored hash\n        let parsed_hash = PasswordHash::new(hash)\n            .map_err(|_| AuthError::InvalidHash)?;\n        \n        // Verify the password\n        match self.argon2.verify_password(password.as_bytes(), &parsed_hash) {\n            Ok(()) => Ok(true),\n            Err(_) => Ok(false),\n        }\n    }\n\n    /// Check if password meets minimum security requirements\n    pub fn validate_password_strength(password: &str) -> Result<(), AuthError> {\n        if password.len() < 8 {\n            return Err(AuthError::InvalidCredentials);\n        }\n        \n        let has_uppercase = password.chars().any(|c| c.is_uppercase());\n        let has_lowercase = password.chars().any(|c| c.is_lowercase());\n        let has_digit = password.chars().any(|c| c.is_ascii_digit());\n        let has_special = password.chars().any(|c| !c.is_alphanumeric());\n        \n        if !has_uppercase || !has_lowercase || !has_digit || !has_special {\n            return Err(AuthError::InvalidCredentials);\n        }\n        \n        Ok(())\n    }\n\n    /// Generate a secure random password\n    pub fn generate_secure_password(length: usize) -> String {\n        use rand::Rng;\n        \n        const CHARSET: &[u8] = b\"ABCDEFGHIJKLMNOPQRSTUVWXYZ\\\n                                abcdefghijklmnopqrstuvwxyz\\\n                                0123456789\\\n                                !@#$%^&*()_+-=[]{}|;:,.<>?\";\n        \n        let mut rng = rand::thread_rng();\n        let password: String = (0..length)\n            .map(|_| {\n                let idx = rng.gen_range(0..CHARSET.len());\n                CHARSET[idx] as char\n            })\n            .collect();\n        \n        password\n    }\n}\n\nimpl Default for PasswordService {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn test_password_hashing_and_verification() {\n        let service = PasswordService::new();\n        let password = \"test-password-123!\";\n        \n        let hash = service.hash_password(password).unwrap();\n        assert!(service.verify_password(password, &hash).unwrap());\n        assert!(!service.verify_password(\"wrong-password\", &hash).unwrap());\n    }\n\n    #[test]\n    fn test_different_hashes_for_same_password() {\n        let service = PasswordService::new();\n        let password = \"test-password-123!\";\n        \n        let hash1 = service.hash_password(password).unwrap();\n        let hash2 = service.hash_password(password).unwrap();\n        \n        // Hashes should be different due to random salt\n        assert_ne!(hash1, hash2);\n        \n        // But both should verify correctly\n        assert!(service.verify_password(password, &hash1).unwrap());\n        assert!(service.verify_password(password, &hash2).unwrap());\n    }\n\n    #[test]\n    fn test_invalid_hash() {\n        let service = PasswordService::new();\n        let result = service.verify_password(\"password\", \"invalid-hash\");\n        assert!(result.is_err());\n    }\n\n    #[test]\n    fn test_password_strength_validation() {\n        // Valid password\n        assert!(PasswordService::validate_password_strength(\"StrongPass123!\").is_ok());\n        \n        // Too short\n        assert!(PasswordService::validate_password_strength(\"Short1!\").is_err());\n        \n        // Missing uppercase\n        assert!(PasswordService::validate_password_strength(\"lowercase123!\").is_err());\n        \n        // Missing lowercase\n        assert!(PasswordService::validate_password_strength(\"UPPERCASE123!\").is_err());\n        \n        // Missing digit\n        assert!(PasswordService::validate_password_strength(\"NoDigits!\").is_err());\n        \n        // Missing special character\n        assert!(PasswordService::validate_password_strength(\"NoSpecial123\").is_err());\n    }\n\n    #[test]\n    fn test_generate_secure_password() {\n        let password = PasswordService::generate_secure_password(16);\n        assert_eq!(password.len(), 16);\n        \n        // Generated password should meet strength requirements\n        assert!(PasswordService::validate_password_strength(&password).is_ok());\n    }\n\n    #[test]\n    fn test_hash_format() {\n        let service = PasswordService::new();\n        let password = \"test-password-123!\";\n        let hash = service.hash_password(password).unwrap();\n        \n        // Argon2 hash should start with $argon2id$\n        assert!(hash.starts_with(\"$argon2id$\"));\n    }\n}\n"}