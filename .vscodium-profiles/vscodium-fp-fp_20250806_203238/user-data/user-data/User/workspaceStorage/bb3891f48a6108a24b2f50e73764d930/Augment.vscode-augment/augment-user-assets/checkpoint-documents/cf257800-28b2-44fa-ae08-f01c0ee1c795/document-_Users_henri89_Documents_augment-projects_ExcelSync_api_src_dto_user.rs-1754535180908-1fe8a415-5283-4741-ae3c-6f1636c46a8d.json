{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/user.rs"}, "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\n\n/// User creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateUserRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n    #[validate(length(min = 2))]\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// User update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateUserRequest {\n    #[validate(email)]\n    pub email: Option<String>,\n    #[validate(length(min = 2))]\n    pub full_name: Option<String>,\n    pub role: Option<String>,\n    pub is_active: Option<bool>,\n}\n\n/// User response DTO\n#[derive(Debug, Serialize)]\npub struct UserResponse {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n    pub is_active: bool,\n    pub last_login: Option<String>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n"}