{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000008_create_indexes.rs"}, "originalCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Users indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_users_email\")\n                    .table(Users::Table)\n                    .col(Users::Email)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_users_organization_id\")\n                    .table(Users::Table)\n                    .col(Users::OrganizationId)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Projects indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_created_by\")\n                    .table(Projects::Table)\n                    .col(Projects::CreatedBy)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_organization_id\")\n                    .table(Projects::Table)\n                    .col(Projects::OrganizationId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_status\")\n                    .table(Projects::Table)\n                    .col(Projects::Status)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Project data indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_project_id\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::ProjectId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_template_id\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::TemplateId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_is_current\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::IsCurrent)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Sessions indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_user_id\")\n                    .table(Sessions::Table)\n                    .col(Sessions::UserId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_token\")\n                    .table(Sessions::Table)\n                    .col(Sessions::SessionToken)\n                    .unique()\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_expires_at\")\n                    .table(Sessions::Table)\n                    .col(Sessions::ExpiresAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Audit logs indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_user_id\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::UserId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_entity_type_id\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::EntityType)\n                    .col(AuditLogs::EntityId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_created_at\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::CreatedAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        Ok(())\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Drop all indexes\n        let indexes = [\n            \"idx_users_email\",\n            \"idx_users_organization_id\",\n            \"idx_projects_owner_id\",\n            \"idx_projects_organization_id\",\n            \"idx_projects_status\",\n            \"idx_project_data_project_id\",\n            \"idx_project_data_template_id\",\n            \"idx_project_data_is_current\",\n            \"idx_sessions_user_id\",\n            \"idx_sessions_token\",\n            \"idx_sessions_expires_at\",\n            \"idx_audit_logs_user_id\",\n            \"idx_audit_logs_entity_type_id\",\n            \"idx_audit_logs_created_at\",\n        ];\n\n        for index_name in indexes {\n            manager\n                .drop_index(Index::drop().name(index_name).to_owned())\n                .await?;\n        }\n\n        Ok(())\n    }\n}\n\n// Table references for indexes\n#[derive(Iden)]\nenum Users {\n    Table,\n    Email,\n    OrganizationId,\n}\n\n#[derive(Iden)]\nenum Projects {\n    Table,\n    OwnerId,\n    OrganizationId,\n    Status,\n}\n\n#[derive(Iden)]\nenum ProjectData {\n    Table,\n    ProjectId,\n    TemplateId,\n    IsCurrent,\n}\n\n#[derive(Iden)]\nenum Sessions {\n    Table,\n    UserId,\n    SessionToken,\n    ExpiresAt,\n}\n\n#[derive(Iden)]\nenum AuditLogs {\n    Table,\n    UserId,\n    EntityType,\n    EntityId,\n    CreatedAt,\n}\n", "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Users indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_users_email\")\n                    .table(Users::Table)\n                    .col(Users::Email)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_users_organization_id\")\n                    .table(Users::Table)\n                    .col(Users::OrganizationId)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Projects indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_created_by\")\n                    .table(Projects::Table)\n                    .col(Projects::CreatedBy)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_organization_id\")\n                    .table(Projects::Table)\n                    .col(Projects::OrganizationId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_projects_status\")\n                    .table(Projects::Table)\n                    .col(Projects::Status)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Project data indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_project_id\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::ProjectId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_template_id\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::TemplateId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_project_data_is_current\")\n                    .table(ProjectData::Table)\n                    .col(ProjectData::IsCurrent)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Sessions indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_user_id\")\n                    .table(Sessions::Table)\n                    .col(Sessions::UserId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_token\")\n                    .table(Sessions::Table)\n                    .col(Sessions::SessionToken)\n                    .unique()\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_sessions_expires_at\")\n                    .table(Sessions::Table)\n                    .col(Sessions::ExpiresAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        // Audit logs indexes\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_user_id\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::UserId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_entity_type_id\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::EntityType)\n                    .col(AuditLogs::EntityId)\n                    .to_owned(),\n            )\n            .await?;\n\n        manager\n            .create_index(\n                Index::create()\n                    .if_not_exists()\n                    .name(\"idx_audit_logs_created_at\")\n                    .table(AuditLogs::Table)\n                    .col(AuditLogs::CreatedAt)\n                    .to_owned(),\n            )\n            .await?;\n\n        Ok(())\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Drop all indexes\n        let indexes = [\n            \"idx_users_email\",\n            \"idx_users_organization_id\",\n            \"idx_projects_created_by\",\n            \"idx_projects_organization_id\",\n            \"idx_projects_status\",\n            \"idx_project_data_project_id\",\n            \"idx_project_data_template_id\",\n            \"idx_project_data_is_current\",\n            \"idx_sessions_user_id\",\n            \"idx_sessions_token\",\n            \"idx_sessions_expires_at\",\n            \"idx_audit_logs_user_id\",\n            \"idx_audit_logs_entity_type_id\",\n            \"idx_audit_logs_created_at\",\n        ];\n\n        for index_name in indexes {\n            manager\n                .drop_index(Index::drop().name(index_name).to_owned())\n                .await?;\n        }\n\n        Ok(())\n    }\n}\n\n// Table references for indexes\n#[derive(Iden)]\nenum Users {\n    Table,\n    Email,\n    OrganizationId,\n}\n\n#[derive(Iden)]\nenum Projects {\n    Table,\n    OwnerId,\n    OrganizationId,\n    Status,\n}\n\n#[derive(Iden)]\nenum ProjectData {\n    Table,\n    ProjectId,\n    TemplateId,\n    IsCurrent,\n}\n\n#[derive(Iden)]\nenum Sessions {\n    Table,\n    UserId,\n    SessionToken,\n    ExpiresAt,\n}\n\n#[derive(Iden)]\nenum AuditLogs {\n    Table,\n    UserId,\n    EntityType,\n    EntityId,\n    CreatedAt,\n}\n"}