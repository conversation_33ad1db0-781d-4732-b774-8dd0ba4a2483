{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "tarpaulin.toml"}, "modifiedCode": "# Tarpaulin configuration for code coverage\n[tool.tarpaulin]\n# Coverage target percentage\ntarget-coverage = 80.0\n\n# Output formats\nout = [\"Html\", \"Lcov\", \"Json\"]\n\n# Exclude files from coverage\nexclude = [\n    \"*/tests/*\",\n    \"*/benches/*\",\n    \"*/examples/*\",\n    \"*/target/*\",\n    \"*/migrations/*\",\n    \"*/bin/*\"\n]\n\n# Include only source files\ninclude = [\n    \"src/*\",\n    \"*/src/*\"\n]\n\n# Timeout for tests\ntimeout = 120\n\n# Run tests in release mode for better performance\nrelease = false\n\n# Count hits in tests\ncount = true\n\n# Ignore panics in coverage\nignore-panics = true\n\n# Follow symbolic links\nfollow-exec = true\n\n# Post test command\npost-test-command = \"echo 'Coverage report generated'\"\n\n# Fail if coverage is below target\nfail-under = 75.0\n\n# Verbose output\nverbose = true\n\n# Run doctests\nrun-doctests = true\n\n# Engine to use\nengine = \"llvm\"\n"}