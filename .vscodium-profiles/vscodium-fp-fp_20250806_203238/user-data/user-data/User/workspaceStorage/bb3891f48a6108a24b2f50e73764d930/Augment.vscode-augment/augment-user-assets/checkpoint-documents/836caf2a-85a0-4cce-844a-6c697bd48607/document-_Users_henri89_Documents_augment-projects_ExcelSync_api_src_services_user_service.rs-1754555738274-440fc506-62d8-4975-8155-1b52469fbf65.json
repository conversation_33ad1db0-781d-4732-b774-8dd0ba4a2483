{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/user_service.rs"}, "originalCode": "use anyhow::Result;\nuse auth::{AuthService, Claims};\nuse chrono::Utc;\nuse database::{\n    entities::{users, UserR<PERSON>},\n    DatabaseConnection,\n};\nuse sea_orm::{\n    ActiveModelTrait, ColumnTrait, EntityTrait, PaginatorTrait, Query<PERSON>ilter, QueryOrder, Set,\n};\nuse serde::{Deserialize, Serialize};\nuse std::sync::Arc;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{handlers::ApiError, AppState};\n\n/// User creation request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct CreateUserRequest {\n    #[validate(email)]\n    pub email: String,\n    \n    #[validate(length(min = 8, max = 128))]\n    pub password: String,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub full_name: String,\n    \n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n}\n\n/// User update request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct UpdateUserRequest {\n    #[validate(email)]\n    pub email: Option<String>,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub full_name: Option<String>,\n    \n    pub organization_id: Option<Option<Uuid>>,\n    pub role: Option<UserRole>,\n    pub is_active: Option<bool>,\n}\n\n/// User password change request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct ChangePasswordRequest {\n    #[validate(length(min = 8, max = 128))]\n    pub current_password: String,\n    \n    #[validate(length(min = 8, max = 128))]\n    pub new_password: String,\n}\n\n/// User response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct UserResponse {\n    pub id: Uuid,\n    pub email: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<String>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n\nimpl From<users::Model> for UserResponse {\n    fn from(user: users::Model) -> Self {\n        Self {\n            id: user.id,\n            email: user.email,\n            full_name: user.full_name,\n            organization_id: user.organization_id,\n            role: user.role,\n            is_active: user.is_active,\n            last_login: user.last_login.map(|dt| dt.to_rfc3339()),\n            created_at: user.created_at.to_rfc3339(),\n            updated_at: user.updated_at.to_rfc3339(),\n        }\n    }\n}\n\n/// Paginated user list response\n#[derive(Debug, Serialize, Deserialize)]\npub struct UserListResponse {\n    pub users: Vec<UserResponse>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n}\n\n/// User service for managing user operations\npub struct UserService {\n    db: Arc<DatabaseConnection>,\n    auth_service: Arc<AuthService>,\n}\n\nimpl UserService {\n    /// Create new user service\n    pub fn new(db: Arc<DatabaseConnection>, auth_service: Arc<AuthService>) -> Self {\n        Self { db, auth_service }\n    }\n\n    /// Create a new user\n    pub async fn create_user(&self, request: CreateUserRequest) -> Result<UserResponse, ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check if user with email already exists\n        let existing_user = users::Entity::find()\n            .filter(users::Column::Email.eq(&request.email))\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to check existing user: {}\", e)))?;\n\n        if existing_user.is_some() {\n            return Err(ApiError::BadRequest(\"User with this email already exists\".to_string()));\n        }\n\n        // Hash password\n        let password_hash = self\n            .auth_service\n            .hash_password(&request.password)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to hash password: {}\", e)))?;\n\n        // Create user\n        let user = users::ActiveModel {\n            email: Set(request.email),\n            password_hash: Set(password_hash),\n            full_name: Set(request.full_name),\n            organization_id: Set(request.organization_id),\n            role: Set(request.role),\n            ..Default::default()\n        };\n\n        let user = user\n            .insert(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to create user: {}\", e)))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Get user by ID\n    pub async fn get_user(&self, user_id: Uuid) -> Result<UserResponse, ApiError> {\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Get user by email\n    pub async fn get_user_by_email(&self, email: &str) -> Result<users::Model, ApiError> {\n        let user = users::Entity::find()\n            .filter(users::Column::Email.eq(email))\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        Ok(user)\n    }\n\n    /// Update user\n    pub async fn update_user(\n        &self,\n        user_id: Uuid,\n        request: UpdateUserRequest,\n        current_user: &Claims,\n    ) -> Result<UserResponse, ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check permissions - users can only update themselves unless they're admin\n        if current_user.sub != user_id.to_string() && current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Insufficient permissions\".to_string()));\n        }\n\n        // Get existing user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Check if email is being changed and if it's already taken\n        if let Some(ref new_email) = request.email {\n            if new_email != &user.email {\n                let existing_user = users::Entity::find()\n                    .filter(users::Column::Email.eq(new_email))\n                    .one(self.db.get_connection())\n                    .await\n                    .map_err(|e| ApiError::DatabaseError(format!(\"Failed to check existing user: {}\", e)))?;\n\n                if existing_user.is_some() {\n                    return Err(ApiError::BadRequest(\"Email already in use\".to_string()));\n                }\n            }\n        }\n\n        // Update user\n        let mut user: users::ActiveModel = user.into();\n        \n        if let Some(email) = request.email {\n            user.email = Set(email);\n        }\n        if let Some(full_name) = request.full_name {\n            user.full_name = Set(full_name);\n        }\n        if let Some(organization_id) = request.organization_id {\n            user.organization_id = Set(organization_id);\n        }\n        if let Some(role) = request.role {\n            // Only admins can change roles\n            if current_user.role != \"admin\" {\n                return Err(ApiError::Forbidden(\"Only admins can change user roles\".to_string()));\n            }\n            user.role = Set(role);\n        }\n        if let Some(is_active) = request.is_active {\n            // Only admins can change active status\n            if current_user.role != \"admin\" {\n                return Err(ApiError::Forbidden(\"Only admins can change user status\".to_string()));\n            }\n            user.is_active = Set(is_active);\n        }\n\n        user.updated_at = Set(Utc::now());\n\n        let user = user\n            .update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update user: {}\", e)))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Change user password\n    pub async fn change_password(\n        &self,\n        user_id: Uuid,\n        request: ChangePasswordRequest,\n        current_user: &Claims,\n    ) -> Result<(), ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check permissions - users can only change their own password\n        if current_user.sub != user_id.to_string() {\n            return Err(ApiError::Forbidden(\"Can only change your own password\".to_string()));\n        }\n\n        // Get user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Verify current password\n        let is_valid = self\n            .auth_service\n            .verify_password(&request.current_password, &user.password_hash)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to verify password: {}\", e)))?;\n\n        if !is_valid {\n            return Err(ApiError::BadRequest(\"Current password is incorrect\".to_string()));\n        }\n\n        // Hash new password\n        let new_password_hash = self\n            .auth_service\n            .hash_password(&request.new_password)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to hash password: {}\", e)))?;\n\n        // Update password\n        let mut user: users::ActiveModel = user.into();\n        user.password_hash = Set(new_password_hash);\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update password: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// List users with pagination\n    pub async fn list_users(\n        &self,\n        page: u64,\n        per_page: u64,\n        current_user: &Claims,\n    ) -> Result<UserListResponse, ApiError> {\n        // Only admins can list all users\n        if current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Only admins can list users\".to_string()));\n        }\n\n        let page = page.max(1);\n        let per_page = per_page.min(100).max(1); // Limit to 100 per page\n\n        // Get total count\n        let total = users::Entity::find()\n            .count(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to count users: {}\", e)))?;\n\n        // Get users\n        let users = users::Entity::find()\n            .order_by_asc(users::Column::CreatedAt)\n            .paginate(self.db.get_connection(), per_page)\n            .fetch_page(page - 1)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch users: {}\", e)))?;\n\n        let users: Vec<UserResponse> = users.into_iter().map(UserResponse::from).collect();\n\n        let total_pages = (total + per_page - 1) / per_page;\n\n        Ok(UserListResponse {\n            users,\n            total,\n            page,\n            per_page,\n            total_pages,\n        })\n    }\n\n    /// Delete user (soft delete by setting is_active to false)\n    pub async fn delete_user(\n        &self,\n        user_id: Uuid,\n        current_user: &Claims,\n    ) -> Result<(), ApiError> {\n        // Only admins can delete users\n        if current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Only admins can delete users\".to_string()));\n        }\n\n        // Get user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Soft delete by setting is_active to false\n        let mut user: users::ActiveModel = user.into();\n        user.is_active = Set(false);\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to delete user: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// Update user's last login time\n    pub async fn update_last_login(&self, user_id: Uuid) -> Result<(), ApiError> {\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        let mut user: users::ActiveModel = user.into();\n        user.last_login = Set(Some(Utc::now()));\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update last login: {}\", e)))?;\n\n        Ok(())\n    }\n}\n", "modifiedCode": "use anyhow::Result;\nuse auth::{AuthService, Claims};\nuse chrono::Utc;\nuse database::{\n    entities::{users, UserR<PERSON>},\n    DatabaseConnection,\n};\nuse sea_orm::{\n    ActiveModelTrait, ColumnTrait, EntityTrait, PaginatorTrait, Query<PERSON>ilter, QueryOrder, Set,\n};\nuse serde::{Deserialize, Serialize};\nuse std::sync::Arc;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{handlers::ApiError, AppState};\n\n/// User creation request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct CreateUserRequest {\n    #[validate(email)]\n    pub email: String,\n    \n    #[validate(length(min = 8, max = 128))]\n    pub password: String,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub full_name: String,\n    \n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n}\n\n/// User update request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct UpdateUserRequest {\n    #[validate(email)]\n    pub email: Option<String>,\n    \n    #[validate(length(min = 1, max = 255))]\n    pub full_name: Option<String>,\n    \n    pub organization_id: Option<Option<Uuid>>,\n    pub role: Option<UserRole>,\n    pub is_active: Option<bool>,\n}\n\n/// User password change request\n#[derive(Debug, Serialize, Deserialize, Validate)]\npub struct ChangePasswordRequest {\n    #[validate(length(min = 8, max = 128))]\n    pub current_password: String,\n    \n    #[validate(length(min = 8, max = 128))]\n    pub new_password: String,\n}\n\n/// User response DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct UserResponse {\n    pub id: Uuid,\n    pub email: String,\n    pub full_name: String,\n    pub organization_id: Option<Uuid>,\n    pub role: UserRole,\n    pub is_active: bool,\n    pub last_login: Option<String>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n\nimpl From<users::Model> for UserResponse {\n    fn from(user: users::Model) -> Self {\n        Self {\n            id: user.id,\n            email: user.email,\n            full_name: user.full_name,\n            organization_id: user.organization_id,\n            role: user.role,\n            is_active: user.is_active,\n            last_login: user.last_login.map(|dt| dt.to_rfc3339()),\n            created_at: user.created_at.to_rfc3339(),\n            updated_at: user.updated_at.to_rfc3339(),\n        }\n    }\n}\n\n/// Paginated user list response\n#[derive(Debug, Serialize, Deserialize)]\npub struct UserListResponse {\n    pub users: Vec<UserResponse>,\n    pub total: u64,\n    pub page: u64,\n    pub per_page: u64,\n    pub total_pages: u64,\n}\n\n/// User service for managing user operations\npub struct UserService {\n    db: Arc<DatabaseConnection>,\n    auth_service: Arc<AuthService>,\n}\n\nimpl UserService {\n    /// Create new user service\n    pub fn new(db: Arc<DatabaseConnection>, auth_service: Arc<AuthService>) -> Self {\n        Self { db, auth_service }\n    }\n\n    /// Create a new user\n    pub async fn create_user(&self, request: CreateUserRequest) -> Result<UserResponse, ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check if user with email already exists\n        let existing_user = users::Entity::find()\n            .filter(users::Column::Email.eq(&request.email))\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to check existing user: {}\", e)))?;\n\n        if existing_user.is_some() {\n            return Err(ApiError::BadRequest(\"User with this email already exists\".to_string()));\n        }\n\n        // Hash password\n        let password_hash = self\n            .auth_service\n            .hash_password(&request.password)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to hash password: {}\", e)))?;\n\n        // Create user\n        let user = users::ActiveModel {\n            email: Set(request.email),\n            password_hash: Set(password_hash),\n            full_name: Set(request.full_name),\n            organization_id: Set(request.organization_id),\n            role: Set(request.role),\n            ..Default::default()\n        };\n\n        let user = user\n            .insert(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to create user: {}\", e)))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Get user by ID\n    pub async fn get_user(&self, user_id: Uuid) -> Result<UserResponse, ApiError> {\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Get user by email\n    pub async fn get_user_by_email(&self, email: &str) -> Result<users::Model, ApiError> {\n        let user = users::Entity::find()\n            .filter(users::Column::Email.eq(email))\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        Ok(user)\n    }\n\n    /// Update user\n    pub async fn update_user(\n        &self,\n        user_id: Uuid,\n        request: UpdateUserRequest,\n        current_user: &Claims,\n    ) -> Result<UserResponse, ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check permissions - users can only update themselves unless they're admin\n        if current_user.sub != user_id.to_string() && current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Insufficient permissions\".to_string()));\n        }\n\n        // Get existing user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Check if email is being changed and if it's already taken\n        if let Some(ref new_email) = request.email {\n            if new_email != &user.email {\n                let existing_user = users::Entity::find()\n                    .filter(users::Column::Email.eq(new_email))\n                    .one(self.db.get_connection())\n                    .await\n                    .map_err(|e| ApiError::DatabaseError(format!(\"Failed to check existing user: {}\", e)))?;\n\n                if existing_user.is_some() {\n                    return Err(ApiError::BadRequest(\"Email already in use\".to_string()));\n                }\n            }\n        }\n\n        // Update user\n        let mut user: users::ActiveModel = user.into();\n        \n        if let Some(email) = request.email {\n            user.email = Set(email);\n        }\n        if let Some(full_name) = request.full_name {\n            user.full_name = Set(full_name);\n        }\n        if let Some(organization_id) = request.organization_id {\n            user.organization_id = Set(organization_id);\n        }\n        if let Some(role) = request.role {\n            // Only admins can change roles\n            if current_user.role != \"admin\" {\n                return Err(ApiError::Forbidden(\"Only admins can change user roles\".to_string()));\n            }\n            user.role = Set(role);\n        }\n        if let Some(is_active) = request.is_active {\n            // Only admins can change active status\n            if current_user.role != \"admin\" {\n                return Err(ApiError::Forbidden(\"Only admins can change user status\".to_string()));\n            }\n            user.is_active = Set(is_active);\n        }\n\n        user.updated_at = Set(Utc::now());\n\n        let user = user\n            .update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update user: {}\", e)))?;\n\n        Ok(UserResponse::from(user))\n    }\n\n    /// Change user password\n    pub async fn change_password(\n        &self,\n        user_id: Uuid,\n        request: ChangePasswordRequest,\n        current_user: &Claims,\n    ) -> Result<(), ApiError> {\n        // Validate request\n        request.validate().map_err(|e| ApiError::BadRequest(format!(\"Validation error: {}\", e)))?;\n\n        // Check permissions - users can only change their own password\n        if current_user.sub != user_id.to_string() {\n            return Err(ApiError::Forbidden(\"Can only change your own password\".to_string()));\n        }\n\n        // Get user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Verify current password\n        let is_valid = self\n            .auth_service\n            .verify_password(&request.current_password, &user.password_hash)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to verify password: {}\", e)))?;\n\n        if !is_valid {\n            return Err(ApiError::BadRequest(\"Current password is incorrect\".to_string()));\n        }\n\n        // Hash new password\n        let new_password_hash = self\n            .auth_service\n            .hash_password(&request.new_password)\n            .map_err(|e| ApiError::InternalServerError(format!(\"Failed to hash password: {}\", e)))?;\n\n        // Update password\n        let mut user: users::ActiveModel = user.into();\n        user.password_hash = Set(new_password_hash);\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update password: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// List users with pagination\n    pub async fn list_users(\n        &self,\n        page: u64,\n        per_page: u64,\n        current_user: &Claims,\n    ) -> Result<UserListResponse, ApiError> {\n        // Only admins can list all users\n        if current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Only admins can list users\".to_string()));\n        }\n\n        let page = page.max(1);\n        let per_page = per_page.min(100).max(1); // Limit to 100 per page\n\n        // Get total count\n        let total = users::Entity::find()\n            .count(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to count users: {}\", e)))?;\n\n        // Get users\n        let users = users::Entity::find()\n            .order_by_asc(users::Column::CreatedAt)\n            .paginate(self.db.get_connection(), per_page)\n            .fetch_page(page - 1)\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch users: {}\", e)))?;\n\n        let users: Vec<UserResponse> = users.into_iter().map(UserResponse::from).collect();\n\n        let total_pages = (total + per_page - 1) / per_page;\n\n        Ok(UserListResponse {\n            users,\n            total,\n            page,\n            per_page,\n            total_pages,\n        })\n    }\n\n    /// Delete user (soft delete by setting is_active to false)\n    pub async fn delete_user(\n        &self,\n        user_id: Uuid,\n        current_user: &Claims,\n    ) -> Result<(), ApiError> {\n        // Only admins can delete users\n        if current_user.role != \"admin\" {\n            return Err(ApiError::Forbidden(\"Only admins can delete users\".to_string()));\n        }\n\n        // Get user\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        // Soft delete by setting is_active to false\n        let mut user: users::ActiveModel = user.into();\n        user.is_active = Set(false);\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to delete user: {}\", e)))?;\n\n        Ok(())\n    }\n\n    /// Update user's last login time\n    pub async fn update_last_login(&self, user_id: Uuid) -> Result<(), ApiError> {\n        let user = users::Entity::find_by_id(user_id)\n            .one(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to fetch user: {}\", e)))?\n            .ok_or_else(|| ApiError::NotFound(\"User not found\".to_string()))?;\n\n        let mut user: users::ActiveModel = user.into();\n        user.last_login = Set(Some(Utc::now()));\n        user.updated_at = Set(Utc::now());\n\n        user.update(self.db.get_connection())\n            .await\n            .map_err(|e| ApiError::DatabaseError(format!(\"Failed to update last login: {}\", e)))?;\n\n        Ok(())\n    }\n}\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n    use auth::AuthService;\n    use database::entities::users;\n    use mockall::predicate::*;\n    use mockall::mock;\n    use sea_orm::{DatabaseBackend, MockDatabase, MockExecResult};\n    use std::sync::Arc;\n    use tokio_test;\n\n    // Mock database connection for testing\n    mock! {\n        DatabaseConn {}\n\n        impl DatabaseConnection for DatabaseConn {\n            async fn get_connection(&self) -> Result<sea_orm::DatabaseConnection>;\n        }\n    }\n\n    fn create_test_user_service() -> UserService {\n        // Create a mock database\n        let db = MockDatabase::new(DatabaseBackend::Postgres)\n            .into_connection();\n\n        UserService::new(Arc::new(DatabaseConnection::from_connection(db)))\n    }\n\n    fn create_test_user_model() -> users::Model {\n        users::Model {\n            id: Uuid::new_v4(),\n            email: \"<EMAIL>\".to_string(),\n            password_hash: \"$argon2id$v=19$m=65536,t=3,p=4$test\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n            is_active: true,\n            last_login: None,\n            created_at: Utc::now(),\n            updated_at: Utc::now(),\n        }\n    }\n\n    #[tokio::test]\n    async fn test_create_user_request_validation() {\n        let valid_request = CreateUserRequest {\n            email: \"<EMAIL>\".to_string(),\n            password: \"password123\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n        };\n\n        assert!(valid_request.validate().is_ok());\n\n        // Test invalid email\n        let invalid_email_request = CreateUserRequest {\n            email: \"invalid-email\".to_string(),\n            password: \"password123\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n        };\n\n        assert!(invalid_email_request.validate().is_err());\n\n        // Test short password\n        let short_password_request = CreateUserRequest {\n            email: \"<EMAIL>\".to_string(),\n            password: \"short\".to_string(),\n            full_name: \"Test User\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n        };\n\n        assert!(short_password_request.validate().is_err());\n\n        // Test empty full name\n        let empty_name_request = CreateUserRequest {\n            email: \"<EMAIL>\".to_string(),\n            password: \"password123\".to_string(),\n            full_name: \"\".to_string(),\n            organization_id: None,\n            role: UserRole::Analyst,\n        };\n\n        assert!(empty_name_request.validate().is_err());\n    }\n\n    #[tokio::test]\n    async fn test_update_user_request_validation() {\n        let valid_request = UpdateUserRequest {\n            email: Some(\"<EMAIL>\".to_string()),\n            full_name: Some(\"New Name\".to_string()),\n            organization_id: None,\n            role: Some(UserRole::ProjectManager),\n            is_active: Some(false),\n        };\n\n        assert!(valid_request.validate().is_ok());\n\n        // Test invalid email\n        let invalid_email_request = UpdateUserRequest {\n            email: Some(\"invalid-email\".to_string()),\n            full_name: None,\n            organization_id: None,\n            role: None,\n            is_active: None,\n        };\n\n        assert!(invalid_email_request.validate().is_err());\n    }\n\n    #[tokio::test]\n    async fn test_change_password_request_validation() {\n        let valid_request = ChangePasswordRequest {\n            current_password: \"currentpass123\".to_string(),\n            new_password: \"newpassword123\".to_string(),\n        };\n\n        assert!(valid_request.validate().is_ok());\n\n        // Test short current password\n        let short_current_request = ChangePasswordRequest {\n            current_password: \"short\".to_string(),\n            new_password: \"newpassword123\".to_string(),\n        };\n\n        assert!(short_current_request.validate().is_err());\n\n        // Test short new password\n        let short_new_request = ChangePasswordRequest {\n            current_password: \"currentpass123\".to_string(),\n            new_password: \"short\".to_string(),\n        };\n\n        assert!(short_new_request.validate().is_err());\n    }\n\n    #[test]\n    fn test_user_response_from_model() {\n        let user_model = create_test_user_model();\n        let user_id = user_model.id;\n        let created_at = user_model.created_at;\n        let updated_at = user_model.updated_at;\n\n        let response = UserResponse::from(user_model);\n\n        assert_eq!(response.id, user_id);\n        assert_eq!(response.email, \"<EMAIL>\");\n        assert_eq!(response.full_name, \"Test User\");\n        assert_eq!(response.organization_id, None);\n        assert_eq!(response.role, UserRole::Analyst);\n        assert!(response.is_active);\n        assert_eq!(response.last_login, None);\n        assert_eq!(response.created_at, created_at.to_rfc3339());\n        assert_eq!(response.updated_at, updated_at.to_rfc3339());\n    }\n\n    #[test]\n    fn test_user_role_display() {\n        assert_eq!(UserRole::Admin.to_string(), \"admin\");\n        assert_eq!(UserRole::ProjectManager.to_string(), \"project_manager\");\n        assert_eq!(UserRole::Analyst.to_string(), \"analyst\");\n        assert_eq!(UserRole::Viewer.to_string(), \"viewer\");\n    }\n}\n"}