{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000004_create_templates.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\nuse sea_orm_migration::prelude::extension::postgres::Type;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Create template_type enum\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(TemplateType::Table)\n                    .values([\n                        TemplateType::ProjectLandInfo,\n                        TemplateType::ProjectDesign,\n                        TemplateType::ProjectAssumptions,\n                        TemplateType::ProjectCosting,\n                        TemplateType::TaxReview,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create templates table\n        manager\n            .create_table(\n                Table::create()\n                    .table(Templates::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(Templates::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(Templates::Name).string().not_null())\n                    .col(ColumnDef::new(Templates::Description).text())\n                    .col(\n                        ColumnDef::new(Templates::TemplateType)\n                            .enumeration(TemplateType::Table, [\n                                TemplateType::ProjectLandInfo,\n                                TemplateType::ProjectDesign,\n                                TemplateType::ProjectAssumptions,\n                                TemplateType::ProjectCosting,\n                                TemplateType::TaxReview,\n                            ])\n                            .not_null(),\n                    )\n                    .col(ColumnDef::new(Templates::Version).string().not_null().default(\"1.0.0\"))\n                    .col(ColumnDef::new(Templates::Schema).json().not_null())\n                    .col(ColumnDef::new(Templates::BusinessRules).json().not_null())\n                    .col(ColumnDef::new(Templates::ValidationRules).json().not_null())\n                    .col(ColumnDef::new(Templates::IsActive).boolean().not_null().default(true))\n                    .col(ColumnDef::new(Templates::IsDefault).boolean().not_null().default(false))\n                    .col(ColumnDef::new(Templates::CreatedBy).uuid().not_null())\n                    .col(ColumnDef::new(Templates::CreatedAt).timestamp().not_null())\n                    .col(ColumnDef::new(Templates::UpdatedAt).timestamp().not_null())\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_templates_created_by\")\n                            .from(Templates::Table, Templates::CreatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Templates::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(TemplateType::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Templates {\n    Table,\n    Id,\n    Name,\n    Description,\n    TemplateType,\n    Version,\n    Schema,\n    BusinessRules,\n    ValidationRules,\n    IsActive,\n    IsDefault,\n    CreatedBy,\n    CreatedAt,\n    UpdatedAt,\n}\n\n#[derive(Iden)]\nenum TemplateType {\n    Table,\n    ProjectLandInfo,\n    ProjectDesign,\n    ProjectAssumptions,\n    ProjectCosting,\n    TaxReview,\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n}\n"}