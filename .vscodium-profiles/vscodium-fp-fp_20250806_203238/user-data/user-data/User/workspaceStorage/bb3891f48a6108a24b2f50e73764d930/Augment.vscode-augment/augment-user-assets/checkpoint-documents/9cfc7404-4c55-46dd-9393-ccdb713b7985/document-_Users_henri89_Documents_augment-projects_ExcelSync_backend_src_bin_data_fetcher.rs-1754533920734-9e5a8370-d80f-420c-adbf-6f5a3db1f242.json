{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/bin/data_fetcher.rs"}, "modifiedCode": "use anyhow::Result;\nuse database::DataFetcher;\nuse serde_json;\nuse std::env;\nuse tokio;\nuse tracing::{info, error};\nuse tracing_subscriber;\n\n#[tokio::main]\nasync fn main() -> Result<()> {\n    // Initialize logging\n    tracing_subscriber::fmt::init();\n\n    // Database connection string\n    let database_url = \"********************************************/postgres\";\n    \n    info!(\"Starting data fetcher for PostgreSQL database\");\n    info!(\"Connecting to: 14.232.245.39:5432/postgres\");\n\n    // Create data fetcher\n    let fetcher = match DataFetcher::new(database_url).await {\n        Ok(f) => f,\n        Err(e) => {\n            error!(\"Failed to create data fetcher: {}\", e);\n            return Err(e);\n        }\n    };\n\n    // Test connection\n    info!(\"Testing database connection...\");\n    match fetcher.test_connection().await {\n        Ok(_) => info!(\"✅ Database connection successful!\"),\n        Err(e) => {\n            error!(\"❌ Database connection failed: {}\", e);\n            return Err(e);\n        }\n    }\n\n    // Get command line arguments\n    let args: Vec<String> = env::args().collect();\n    \n    if args.len() < 2 {\n        print_usage();\n        return Ok(());\n    }\n\n    let command = &args[1];\n\n    match command.as_str() {\n        \"tables\" => {\n            info!(\"Fetching list of tables...\");\n            match fetcher.get_tables().await {\n                Ok(tables) => {\n                    println!(\"\\n📋 Available tables:\");\n                    for (i, table) in tables.iter().enumerate() {\n                        println!(\"  {}. {}\", i + 1, table);\n                    }\n                    println!(\"\\nTotal: {} tables\", tables.len());\n                }\n                Err(e) => error!(\"Failed to fetch tables: {}\", e),\n            }\n        }\n        \n        \"schema\" => {\n            if args.len() < 3 {\n                println!(\"Usage: data_fetcher schema <table_name>\");\n                return Ok(());\n            }\n            \n            let table_name = &args[2];\n            info!(\"Fetching schema for table: {}\", table_name);\n            \n            match fetcher.get_table_schema(table_name).await {\n                Ok(result) => {\n                    println!(\"\\n📊 Schema for table '{}':\", table_name);\n                    println!(\"{}\", serde_json::to_string_pretty(&result)?);\n                }\n                Err(e) => error!(\"Failed to fetch schema: {}\", e),\n            }\n        }\n        \n        \"data\" => {\n            if args.len() < 3 {\n                println!(\"Usage: data_fetcher data <table_name> [limit]\");\n                return Ok(());\n            }\n            \n            let table_name = &args[2];\n            let limit = if args.len() > 3 {\n                args[3].parse::<u32>().ok()\n            } else {\n                Some(10) // Default limit\n            };\n            \n            info!(\"Fetching data from table: {} (limit: {:?})\", table_name, limit);\n            \n            match fetcher.fetch_table_data(table_name, limit).await {\n                Ok(result) => {\n                    println!(\"\\n📄 Data from table '{}' (showing {} rows):\", table_name, result.row_count);\n                    println!(\"{}\", serde_json::to_string_pretty(&result)?);\n                }\n                Err(e) => error!(\"Failed to fetch data: {}\", e),\n            }\n        }\n        \n        \"count\" => {\n            if args.len() < 3 {\n                println!(\"Usage: data_fetcher count <table_name>\");\n                return Ok(());\n            }\n            \n            let table_name = &args[2];\n            info!(\"Counting rows in table: {}\", table_name);\n            \n            match fetcher.count_table_rows(table_name).await {\n                Ok(count) => {\n                    println!(\"\\n🔢 Table '{}' has {} rows\", table_name, count);\n                }\n                Err(e) => error!(\"Failed to count rows: {}\", e),\n            }\n        }\n        \n        \"query\" => {\n            if args.len() < 3 {\n                println!(\"Usage: data_fetcher query \\\"<SQL_QUERY>\\\"\");\n                return Ok(());\n            }\n            \n            let sql = &args[2];\n            info!(\"Executing custom query: {}\", sql);\n            \n            match fetcher.execute_query(sql).await {\n                Ok(result) => {\n                    println!(\"\\n🔍 Query result ({} rows):\", result.row_count);\n                    println!(\"{}\", serde_json::to_string_pretty(&result)?);\n                }\n                Err(e) => error!(\"Failed to execute query: {}\", e),\n            }\n        }\n        \n        _ => {\n            println!(\"Unknown command: {}\", command);\n            print_usage();\n        }\n    }\n\n    Ok(())\n}\n\nfn print_usage() {\n    println!(\"\\n🚀 ExcelSync Data Fetcher\");\n    println!(\"Usage: data_fetcher <command> [arguments]\");\n    println!(\"\\nCommands:\");\n    println!(\"  tables                    - List all tables in the database\");\n    println!(\"  schema <table_name>       - Show schema for a specific table\");\n    println!(\"  data <table_name> [limit] - Fetch data from a table (default limit: 10)\");\n    println!(\"  count <table_name>        - Count rows in a table\");\n    println!(\"  query \\\"<SQL>\\\"             - Execute a custom SQL query\");\n    println!(\"\\nExamples:\");\n    println!(\"  data_fetcher tables\");\n    println!(\"  data_fetcher schema users\");\n    println!(\"  data_fetcher data products 20\");\n    println!(\"  data_fetcher count orders\");\n    println!(\"  data_fetcher query \\\"SELECT * FROM users WHERE active = true\\\"\");\n}\n"}