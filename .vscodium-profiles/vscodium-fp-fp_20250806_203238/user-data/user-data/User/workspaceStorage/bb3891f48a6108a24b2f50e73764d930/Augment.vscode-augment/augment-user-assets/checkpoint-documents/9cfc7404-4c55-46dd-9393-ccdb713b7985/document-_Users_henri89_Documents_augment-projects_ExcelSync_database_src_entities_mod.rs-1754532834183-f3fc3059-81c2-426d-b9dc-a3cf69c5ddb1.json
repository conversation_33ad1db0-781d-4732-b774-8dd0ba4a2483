{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/mod.rs"}, "originalCode": "pub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod project_data;\npub mod sessions;\npub mod audit_logs;\n\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use project_data::*;\npub use sessions::*;\npub use audit_logs::*;\n\nuse chrono::{DateTime, Utc};\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\n/// Common fields for all entities\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Timestamps {\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n/// User roles in the system\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum UserRole {\n    Admin,\n    ProjectManager,\n    Analyst,\n    Viewer,\n}\n\n/// Project types for real estate\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum ProjectType {\n    LandDevelopment,\n    ResidentialBuilding,\n    CommercialBuilding,\n    MixedUse,\n}\n\n/// Project status\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum ProjectStatus {\n    Planning,\n    InProgress,\n    OnHold,\n    Completed,\n    Cancelled,\n}\n\n/// Template types\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum TemplateType {\n    ProjectLandInfo,\n    ProjectDesign,\n    ProjectAssumptions,\n    ProjectCosting,\n    TaxReview,\n}\n", "modifiedCode": "pub mod users;\npub mod organizations;\npub mod projects;\npub mod templates;\npub mod project_data;\npub mod sessions;\npub mod audit_logs;\n\npub use users::*;\npub use organizations::*;\npub use projects::*;\npub use templates::*;\npub use project_data::*;\npub use sessions::*;\npub use audit_logs::*;\n\nuse chrono::{DateTime, Utc};\nuse sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\n\n/// Common fields for all entities\n#[derive(Debug, Clone, Serialize, Deserialize)]\npub struct Timestamps {\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n/// User roles in the system\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum UserRole {\n    Admin,\n    ProjectManager,\n    Analyst,\n    Viewer,\n}\n\n/// Project types for real estate\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum ProjectType {\n    LandDevelopment,\n    ResidentialBuilding,\n    CommercialBuilding,\n    MixedUse,\n}\n\n/// Project status\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum ProjectStatus {\n    Planning,\n    InProgress,\n    OnHold,\n    Completed,\n    Cancelled,\n}\n\n/// Template types\n#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]\npub enum TemplateType {\n    ProjectLandInfo,\n    ProjectDesign,\n    ProjectAssumptions,\n    ProjectCosting,\n    TaxReview,\n}\n"}