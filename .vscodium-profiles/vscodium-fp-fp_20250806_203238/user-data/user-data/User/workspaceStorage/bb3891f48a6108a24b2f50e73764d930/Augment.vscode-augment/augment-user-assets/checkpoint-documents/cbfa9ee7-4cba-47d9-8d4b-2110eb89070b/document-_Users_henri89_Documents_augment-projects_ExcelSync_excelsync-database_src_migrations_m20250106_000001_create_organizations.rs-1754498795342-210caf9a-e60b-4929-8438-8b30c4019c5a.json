{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "excelsync-database/src/migrations/m20250106_000001_create_organizations.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_table(\n                Table::create()\n                    .table(Organizations::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(Organizations::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(Organizations::Name).string().not_null())\n                    .col(ColumnDef::new(Organizations::Description).text())\n                    .col(ColumnDef::new(Organizations::Address).text())\n                    .col(ColumnDef::new(Organizations::Phone).string())\n                    .col(ColumnDef::new(Organizations::Email).string())\n                    .col(ColumnDef::new(Organizations::TaxCode).string())\n                    .col(\n                        ColumnDef::new(Organizations::IsActive)\n                            .boolean()\n                            .not_null()\n                            .default(true),\n                    )\n                    .col(\n                        ColumnDef::new(Organizations::CreatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .col(\n                        ColumnDef::new(Organizations::UpdatedAt)\n                            .timestamp_with_time_zone()\n                            .not_null()\n                            .default(Expr::current_timestamp()),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(Organizations::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum Organizations {\n    Table,\n    Id,\n    Name,\n    Description,\n    Address,\n    Phone,\n    Email,\n    TaxCode,\n    IsActive,\n    CreatedAt,\n    UpdatedAt,\n}\n"}