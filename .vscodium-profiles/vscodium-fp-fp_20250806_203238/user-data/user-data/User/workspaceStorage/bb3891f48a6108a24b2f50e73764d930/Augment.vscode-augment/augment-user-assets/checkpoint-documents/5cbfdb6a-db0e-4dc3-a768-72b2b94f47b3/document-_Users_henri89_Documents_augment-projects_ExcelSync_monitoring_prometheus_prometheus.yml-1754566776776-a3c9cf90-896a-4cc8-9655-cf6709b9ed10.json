{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/prometheus/prometheus.yml"}, "modifiedCode": "# Prometheus configuration for ExcelSync monitoring\nglobal:\n  scrape_interval: 15s\n  evaluation_interval: 15s\n  external_labels:\n    monitor: 'excelsync-monitor'\n\n# Alertmanager configuration\nalerting:\n  alertmanagers:\n    - static_configs:\n        - targets:\n          - alertmanager:9093\n\n# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.\nrule_files:\n  - \"alert_rules.yml\"\n\n# Scrape configurations\nscrape_configs:\n  # ExcelSync API monitoring\n  - job_name: 'excelsync-api'\n    static_configs:\n      - targets: ['localhost:3000']\n    metrics_path: '/metrics'\n    scrape_interval: 15s\n    scrape_timeout: 10s\n    honor_labels: false\n    honor_timestamps: true\n    scheme: http\n    \n  # Health check monitoring\n  - job_name: 'excelsync-health'\n    static_configs:\n      - targets: ['localhost:3000']\n    metrics_path: '/health'\n    scrape_interval: 30s\n    scrape_timeout: 10s\n    scheme: http\n    \n  # Readiness check monitoring\n  - job_name: 'excelsync-readiness'\n    static_configs:\n      - targets: ['localhost:3000']\n    metrics_path: '/ready'\n    scrape_interval: 30s\n    scrape_timeout: 10s\n    scheme: http\n\n  # PostgreSQL monitoring (if postgres_exporter is available)\n  - job_name: 'postgresql'\n    static_configs:\n      - targets: ['localhost:9187']\n    scrape_interval: 30s\n    scrape_timeout: 10s\n    \n  # Redis monitoring (if redis_exporter is available)\n  - job_name: 'redis'\n    static_configs:\n      - targets: ['localhost:9121']\n    scrape_interval: 30s\n    scrape_timeout: 10s\n\n  # Node exporter for system metrics (if available)\n  - job_name: 'node'\n    static_configs:\n      - targets: ['localhost:9100']\n    scrape_interval: 30s\n    scrape_timeout: 10s\n\n  # Prometheus self-monitoring\n  - job_name: 'prometheus'\n    static_configs:\n      - targets: ['localhost:9090']\n    scrape_interval: 30s\n    scrape_timeout: 10s\n\n# Remote write configuration (optional, for long-term storage)\n# remote_write:\n#   - url: \"http://localhost:8086/api/v1/prom/write?db=prometheus\"\n\n# Remote read configuration (optional, for long-term storage)\n# remote_read:\n#   - url: \"http://localhost:8086/api/v1/prom/read?db=prometheus\"\n"}