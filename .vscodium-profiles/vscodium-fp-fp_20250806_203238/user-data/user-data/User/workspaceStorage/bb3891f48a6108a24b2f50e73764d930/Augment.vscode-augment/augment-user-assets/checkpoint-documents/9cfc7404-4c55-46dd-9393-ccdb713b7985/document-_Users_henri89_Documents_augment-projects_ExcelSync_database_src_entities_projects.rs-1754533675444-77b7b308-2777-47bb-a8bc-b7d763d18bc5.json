{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/projects.rs"}, "originalCode": "use sea_orm::entity::prelude::*;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::{ProjectType, ProjectStatus};\n\n#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"projects\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: ProjectType,\n    pub status: ProjectStatus,\n    pub owner_id: Uuid,\n    pub organization_id: Uuid,\n    \n    // Real estate specific fields\n    pub land_area: Option<Decimal>,\n    pub land_location: Option<String>,\n    pub land_use_type: Option<String>,\n    pub total_investment: Option<Decimal>,\n    pub expected_revenue: Option<Decimal>,\n    pub roi_percentage: Option<Decimal>,\n    \n    // Timeline\n    pub start_date: Option<DateTime<Utc>>,\n    pub end_date: Option<DateTime<Utc>>,\n    pub completion_percentage: Option<i16>,\n    \n    // Metadata\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::OwnerId\",\n        to = \"super::users::Column::Id\"\n    )]\n    Owner,\n    \n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::project_data::Entity\")]\n    ProjectData,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Owner.def()\n    }\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::project_data::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::ProjectData.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            status: Set(ProjectStatus::Planning),\n            completion_percentage: Set(Some(0)),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n", "modifiedCode": "use sea_orm::entity::prelude::*;\nuse sea_orm::Set;\nuse serde::{Deserialize, Serialize};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\nuse super::{ProjectType, ProjectStatus};\n\n#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Serialize, Deserialize)]\n#[sea_orm(table_name = \"projects\")]\npub struct Model {\n    #[sea_orm(primary_key, auto_increment = false)]\n    pub id: Uuid,\n    \n    pub name: String,\n    pub description: Option<String>,\n    pub project_type: ProjectType,\n    pub status: ProjectStatus,\n    pub owner_id: Uuid,\n    pub organization_id: Uuid,\n    \n    // Real estate specific fields\n    pub land_area: Option<Decimal>,\n    pub land_location: Option<String>,\n    pub land_use_type: Option<String>,\n    pub total_investment: Option<Decimal>,\n    pub expected_revenue: Option<Decimal>,\n    pub roi_percentage: Option<Decimal>,\n    \n    // Timeline\n    pub start_date: Option<DateTime<Utc>>,\n    pub end_date: Option<DateTime<Utc>>,\n    pub completion_percentage: Option<i16>,\n    \n    // Metadata\n    pub created_at: DateTime<Utc>,\n    pub updated_at: DateTime<Utc>,\n}\n\n#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]\npub enum Relation {\n    #[sea_orm(\n        belongs_to = \"super::users::Entity\",\n        from = \"Column::OwnerId\",\n        to = \"super::users::Column::Id\"\n    )]\n    Owner,\n    \n    #[sea_orm(\n        belongs_to = \"super::organizations::Entity\",\n        from = \"Column::OrganizationId\",\n        to = \"super::organizations::Column::Id\"\n    )]\n    Organization,\n    \n    #[sea_orm(has_many = \"super::project_data::Entity\")]\n    ProjectData,\n}\n\nimpl Related<super::users::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Owner.def()\n    }\n}\n\nimpl Related<super::organizations::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::Organization.def()\n    }\n}\n\nimpl Related<super::project_data::Entity> for Entity {\n    fn to() -> RelationDef {\n        Relation::ProjectData.def()\n    }\n}\n\nimpl ActiveModelBehavior for ActiveModel {\n    fn new() -> Self {\n        Self {\n            id: Set(Uuid::new_v4()),\n            created_at: Set(Utc::now()),\n            updated_at: Set(Utc::now()),\n            status: Set(ProjectStatus::Planning),\n            completion_percentage: Set(Some(0)),\n            ..ActiveModelTrait::default()\n        }\n    }\n    \n    fn before_save<C>(mut self, _db: &C, _insert: bool) -> Result<Self, DbErr>\n    where\n        C: ConnectionTrait,\n    {\n        self.updated_at = Set(Utc::now());\n        Ok(self)\n    }\n}\n"}