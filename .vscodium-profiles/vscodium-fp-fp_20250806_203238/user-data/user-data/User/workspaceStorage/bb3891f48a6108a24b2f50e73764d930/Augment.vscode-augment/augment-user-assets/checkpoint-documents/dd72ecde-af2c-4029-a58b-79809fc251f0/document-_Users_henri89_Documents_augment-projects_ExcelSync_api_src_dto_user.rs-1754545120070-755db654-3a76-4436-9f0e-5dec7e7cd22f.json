{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/user.rs"}, "originalCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse super::common::{PaginatedResponse, PaginationInfo};\n\n/// User creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateUserRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n    #[validate(length(min = 2))]\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// User update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateUserRequest {\n    #[validate(email)]\n    pub email: Option<String>,\n    #[validate(length(min = 2))]\n    pub full_name: Option<String>,\n    pub role: Option<String>,\n    pub is_active: Option<bool>,\n}\n\n/// User response DTO\n#[derive(Debug, Serialize)]\npub struct UserResponse {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n    pub is_active: bool,\n    pub last_login: Option<String>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n", "modifiedCode": "use serde::{Deserialize, Serialize};\nuse validator::Validate;\nuse super::common::{PaginatedResponse, PaginationInfo};\n\n/// User creation request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct CreateUserRequest {\n    #[validate(email)]\n    pub email: String,\n    #[validate(length(min = 8))]\n    pub password: String,\n    #[validate(length(min = 2))]\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n}\n\n/// User update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateUserRequest {\n    #[validate(email)]\n    pub email: Option<String>,\n    #[validate(length(min = 2))]\n    pub full_name: Option<String>,\n    pub role: Option<String>,\n    pub is_active: Option<bool>,\n}\n\n/// User response DTO\n#[derive(Debug, Serialize)]\npub struct UserResponse {\n    pub id: String,\n    pub email: String,\n    pub full_name: String,\n    pub role: String,\n    pub organization_id: Option<String>,\n    pub is_active: bool,\n    pub last_login: Option<String>,\n    pub created_at: String,\n    pub updated_at: String,\n}\n\n/// User list response with pagination\npub type UserListResponse = PaginatedResponse<UserResponse>;\n\n/// Change password request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct ChangePasswordRequest {\n    #[validate(length(min = 8, max = 128))]\n    pub current_password: String,\n\n    #[validate(length(min = 8, max = 128))]\n    pub new_password: String,\n\n    #[validate(must_match(other = \"new_password\"))]\n    pub confirm_password: String,\n}\n\n/// User profile update request DTO\n#[derive(Debug, Deserialize, Validate)]\npub struct UpdateUserProfileRequest {\n    #[validate(length(min = 2, max = 255))]\n    pub full_name: Option<String>,\n\n    pub preferences: Option<serde_json::Value>,\n    pub timezone: Option<String>,\n    pub language: Option<String>,\n}\n\n/// User preferences DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct UserPreferences {\n    pub theme: Option<String>,\n    pub notifications: NotificationPreferences,\n    pub dashboard_layout: Option<serde_json::Value>,\n    pub default_currency: Option<String>,\n    pub date_format: Option<String>,\n    pub number_format: Option<String>,\n}\n\n/// Notification preferences DTO\n#[derive(Debug, Serialize, Deserialize)]\npub struct NotificationPreferences {\n    pub email_notifications: bool,\n    pub push_notifications: bool,\n    pub project_updates: bool,\n    pub financial_alerts: bool,\n    pub system_notifications: bool,\n}\n\n/// User activity summary DTO\n#[derive(Debug, Serialize)]\npub struct UserActivitySummary {\n    pub user_id: String,\n    pub last_login: Option<String>,\n    pub login_count: u64,\n    pub projects_created: u64,\n    pub projects_modified: u64,\n    pub calculations_performed: u64,\n    pub templates_used: u64,\n    pub activity_score: f64,\n}\n"}