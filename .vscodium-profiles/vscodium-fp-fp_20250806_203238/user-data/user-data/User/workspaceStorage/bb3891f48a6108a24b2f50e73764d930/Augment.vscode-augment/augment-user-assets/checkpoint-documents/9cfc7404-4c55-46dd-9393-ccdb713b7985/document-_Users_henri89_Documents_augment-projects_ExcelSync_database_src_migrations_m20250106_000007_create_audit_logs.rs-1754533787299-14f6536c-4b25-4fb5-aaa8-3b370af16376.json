{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000007_create_audit_logs.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\nuse sea_orm_migration::prelude::extension::postgres::Type;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        // Create audit_action enum\n        manager\n            .create_type(\n                Type::create()\n                    .as_enum(AuditAction::Table)\n                    .values([\n                        AuditAction::Create,\n                        AuditAction::Update,\n                        AuditAction::Delete,\n                        AuditAction::Login,\n                        AuditAction::Logout,\n                        AuditAction::Export,\n                        AuditAction::Import,\n                    ])\n                    .to_owned(),\n            )\n            .await?;\n\n        // Create audit_logs table\n        manager\n            .create_table(\n                Table::create()\n                    .table(AuditLogs::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(AuditLogs::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(AuditLogs::UserId).uuid().not_null())\n                    .col(\n                        ColumnDef::new(AuditLogs::Action)\n                            .enumeration(AuditAction::Table, [\n                                AuditAction::Create,\n                                AuditAction::Update,\n                                AuditAction::Delete,\n                                AuditAction::Login,\n                                AuditAction::Logout,\n                                AuditAction::Export,\n                                AuditAction::Import,\n                            ])\n                            .not_null(),\n                    )\n                    .col(ColumnDef::new(AuditLogs::EntityType).string().not_null())\n                    .col(ColumnDef::new(AuditLogs::EntityId).uuid())\n                    .col(ColumnDef::new(AuditLogs::OldValues).json())\n                    .col(ColumnDef::new(AuditLogs::NewValues).json())\n                    .col(ColumnDef::new(AuditLogs::IpAddress).string().not_null())\n                    .col(ColumnDef::new(AuditLogs::UserAgent).text().not_null())\n                    .col(ColumnDef::new(AuditLogs::CreatedAt).timestamp().not_null())\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_audit_logs_user\")\n                            .from(AuditLogs::Table, AuditLogs::UserId)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(AuditLogs::Table).to_owned())\n            .await?;\n\n        manager\n            .drop_type(Type::drop().name(AuditAction::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum AuditLogs {\n    Table,\n    Id,\n    UserId,\n    Action,\n    EntityType,\n    EntityId,\n    OldValues,\n    NewValues,\n    IpAddress,\n    UserAgent,\n    CreatedAt,\n}\n\n#[derive(Iden)]\nenum AuditAction {\n    Table,\n    Create,\n    Update,\n    Delete,\n    Login,\n    Logout,\n    Export,\n    Import,\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n}\n"}