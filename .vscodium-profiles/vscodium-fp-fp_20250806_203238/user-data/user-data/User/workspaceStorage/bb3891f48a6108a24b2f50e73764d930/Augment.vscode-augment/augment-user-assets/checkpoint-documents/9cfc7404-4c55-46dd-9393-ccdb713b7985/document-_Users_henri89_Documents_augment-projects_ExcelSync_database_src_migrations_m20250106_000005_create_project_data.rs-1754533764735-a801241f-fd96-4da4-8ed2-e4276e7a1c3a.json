{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000005_create_project_data.rs"}, "modifiedCode": "use sea_orm_migration::prelude::*;\n\n#[derive(DeriveMigrationName)]\npub struct Migration;\n\n#[async_trait::async_trait]\nimpl MigrationTrait for Migration {\n    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .create_table(\n                Table::create()\n                    .table(ProjectData::Table)\n                    .if_not_exists()\n                    .col(\n                        ColumnDef::new(ProjectData::Id)\n                            .uuid()\n                            .not_null()\n                            .primary_key(),\n                    )\n                    .col(ColumnDef::new(ProjectData::ProjectId).uuid().not_null())\n                    .col(ColumnDef::new(ProjectData::TemplateId).uuid().not_null())\n                    .col(ColumnDef::new(ProjectData::Data).json().not_null())\n                    .col(ColumnDef::new(ProjectData::Version).integer().not_null().default(1))\n                    .col(ColumnDef::new(ProjectData::IsCurrent).boolean().not_null().default(true))\n                    .col(ColumnDef::new(ProjectData::CreatedBy).uuid().not_null())\n                    .col(ColumnDef::new(ProjectData::CreatedAt).timestamp().not_null())\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_project_data_project\")\n                            .from(ProjectData::Table, ProjectData::ProjectId)\n                            .to(Projects::Table, Projects::Id)\n                            .on_delete(ForeignKeyAction::Cascade),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_project_data_template\")\n                            .from(ProjectData::Table, ProjectData::TemplateId)\n                            .to(Templates::Table, Templates::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .foreign_key(\n                        ForeignKey::create()\n                            .name(\"fk_project_data_created_by\")\n                            .from(ProjectData::Table, ProjectData::CreatedBy)\n                            .to(Users::Table, Users::Id)\n                            .on_delete(ForeignKeyAction::Restrict),\n                    )\n                    .to_owned(),\n            )\n            .await\n    }\n\n    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {\n        manager\n            .drop_table(Table::drop().table(ProjectData::Table).to_owned())\n            .await\n    }\n}\n\n#[derive(Iden)]\nenum ProjectData {\n    Table,\n    Id,\n    ProjectId,\n    TemplateId,\n    Data,\n    Version,\n    IsCurrent,\n    CreatedBy,\n    CreatedAt,\n}\n\n#[derive(Iden)]\nenum Projects {\n    Table,\n    Id,\n}\n\n#[derive(Iden)]\nenum Templates {\n    Table,\n    Id,\n}\n\n#[derive(Iden)]\nenum Users {\n    Table,\n    Id,\n}\n"}