{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/logging.rs"}, "originalCode": "use tower_http::trace::{TraceLayer, DefaultMakeSpan, DefaultOnResponse};\nuse tower_http::classify::{SharedClassifier, ServerErrorsAsFailures};\n\n/// Create logging middleware layer\npub fn logging_layer() -> TraceLayer<SharedClassifier<ServerErrorsAsFailures>, DefaultMakeSpan, DefaultOnResponse> {\n    TraceLayer::new_for_http()\n}\n", "modifiedCode": "use tower_http::trace::TraceLayer;\n\n/// Create logging middleware layer\npub fn logging_layer() -> impl tower::Layer<axum::Router> + Clone {\n    TraceLayer::new_for_http()\n}\n"}