{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".env.example"}, "modifiedCode": "# ExcelSync Backend Configuration\n\n# Server Configuration\nSERVER_HOST=0.0.0.0\nSERVER_PORT=8080\nSERVER_WORKERS=4\n\n# Database Configuration\nDATABASE_URL=********************************************/postgres\n\n# Redis Configuration\nREDIS_URL=redis://localhost:6379\n\n# Security Configuration\nJWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long\nENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef\nPASSWORD_SALT=your-password-salt-for-additional-security\n\n# Logging Configuration\nLOG_LEVEL=info\nLOG_FORMAT=json\nLOG_FILE=/var/log/excelsync/backend.log\n\n# Performance Configuration\nMAX_CONNECTIONS=20\nMIN_CONNECTIONS=5\nCONNECTION_TIMEOUT=30\nIDLE_TIMEOUT=600\n\n# Rate Limiting\nRATE_LIMIT_REQUESTS=100\nRATE_LIMIT_WINDOW=60\n\n# Session Configuration\nSESSION_TIMEOUT=28800\nMAX_SESSIONS_PER_USER=5\n"}